package com.chidhagni.config;

import com.chidhagni.donationreceipt.security.oauth2.CustomOAuth2UserService;
import com.chidhagni.donationreceipt.security.oauth2.HttpCookieOAuth2AuthorizationRequestRepository;
import com.chidhagni.donationreceipt.security.oauth2.OAuth2AuthenticationFailureHandler;
import com.chidhagni.donationreceipt.security.oauth2.OAuth2AuthenticationSuccessHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(securedEnabled = true, jsr250Enabled = true, prePostEnabled = true)
@Profile({"local","test"})
public class SecurityConfig {

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

    @Autowired
    private JwtRequestFilter jwtRequestFilter;

    @Autowired
    private CustomOAuth2UserService customOAuth2UserService;

    @Autowired
    private AuthenticationConfiguration authenticationConfiguration;

    @Autowired
    private OAuth2AuthenticationSuccessHandler auth2AuthenticationSuccessHandler;

    @Autowired
    private OAuth2AuthenticationFailureHandler auth2AuthenticationFailureHandler;

    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                .cors().and().csrf().disable()
                .authorizeRequests()
                .antMatchers("/auth/**").permitAll()
                .antMatchers("/oauth2/**").permitAll()
                .antMatchers("/public/**").permitAll()
                .antMatchers("/documents/all").permitAll()
                .antMatchers("/service-profile/all").permitAll()
                .antMatchers("/emi-calculator").permitAll()
                .antMatchers("/api/v1/mobile-otp/**").permitAll()
                .antMatchers("/list-values/listNameId").permitAll()
                .antMatchers("/user-services/statistics/{userId}").permitAll()
                .antMatchers("/documentCategories/all").permitAll()
                .antMatchers("/documentSubCategories/all").permitAll()
                .antMatchers("/listNamesValues/all").permitAll()
                .antMatchers("/documents/projects/images").permitAll()
                .antMatchers("/service-groups/all").permitAll()
                .antMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-ui.html", "/dsajczxouidsaljrjesa.html").permitAll()
                .antMatchers("/donationreceipt/v3/api-docs/**").permitAll()
                .antMatchers("/cookie").permitAll()
                .antMatchers(HttpMethod.GET, "/settings", "/requestEnquiry/**").permitAll()
                .anyRequest().authenticated()
                // Reject every unauthenticated request and send error code 401.
                .and().exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint)
                // We don't need sessions to be created.
                .and().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .oauth2Login()
                .authorizationEndpoint()
                .baseUri("/oauth2/authorize")
                .authorizationRequestRepository(cookieAuthorizationRequestRepository())
                .and()
                .redirectionEndpoint()
                .baseUri("/oauth2/callback/*")
                .and()
                .userInfoEndpoint()
                .userService(customOAuth2UserService)
                .and()
                .successHandler(auth2AuthenticationSuccessHandler)
                .failureHandler(auth2AuthenticationFailureHandler); // For custom handling of the OAuth2 authorization request;

        http.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public OAuth2UserService<OAuth2UserRequest, OAuth2User> oAuth2UserService() {
        return customOAuth2UserService;
    }

    @Bean
    public HttpCookieOAuth2AuthorizationRequestRepository cookieAuthorizationRequestRepository() {
        return new HttpCookieOAuth2AuthorizationRequestRepository();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}