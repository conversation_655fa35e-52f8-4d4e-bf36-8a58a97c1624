package com.chidhagni.config;


import com.chidhagni.donationreceipt.security.oauth2.CustomOAuth2UserService;
import com.chidhagni.donationreceipt.security.oauth2.HttpCookieOAuth2AuthorizationRequestRepository;
import com.chidhagni.donationreceipt.security.oauth2.OAuth2AuthenticationFailureHandler;
import com.chidhagni.donationreceipt.security.oauth2.OAuth2AuthenticationSuccessHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(
        securedEnabled = true,
        jsr250Enabled = true,
        prePostEnabled = true
)
@Profile({"dev","prod"})
public class SecurityDevProdConfig extends WebSecurityConfigurerAdapter {

    @Autowired
    private JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;
    @Autowired
    private JwtRequestFilter jwtRequestFilter;



    @Autowired
    private CustomOAuth2UserService customOAuth2UserService;

    @Autowired
    private AuthenticationConfiguration authenticationConfiguration;

    @Autowired
    private OAuth2AuthenticationSuccessHandler auth2AuthenticationSuccessHandler;

    @Autowired
    private OAuth2AuthenticationFailureHandler auth2AuthenticationFailureHandler;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return authenticationConfiguration.getAuthenticationManager();
    }

    @Override
    protected void configure(HttpSecurity httpSecurity) throws Exception {
        // Disable CSRF
        httpSecurity.cors().and().csrf().disable()
                // Permit all other request without authentication
                .authorizeRequests()
                .antMatchers("/auth/**", "/public/**", "/documents/all", "/emi-calculator","/api/v1/mobile-otp/**",
                        "/list-values/listNameId", "/user-services/statistics/{userId}", "/documentCategories/all",
                        "/documentSubCategories/all", "/listNamesValues/all", "/documents/projects/images",
                        "/cookie", "/settings", "/requestEnquiry/**","/service-groups/all").permitAll()
                .anyRequest().authenticated()

                // Reject every unauthenticated request and send error code 401.
                .and().exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint)
                // We don't need sessions to be created.
                .and().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .oauth2Login()
                .authorizationEndpoint()
                .baseUri("/oauth2/authorize")
                .authorizationRequestRepository(cookieAuthorizationRequestRepository())
                .and()
                .redirectionEndpoint()
                .baseUri("/oauth2/callback/*")
                .and()
                .userInfoEndpoint()
                .userService(customOAuth2UserService)
                .and()
                .successHandler(auth2AuthenticationSuccessHandler)
                .failureHandler(auth2AuthenticationFailureHandler) // For custom handling of the OAuth2 authorization request;
                .and().exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint)
                // We don't need sessions to be created.
                .and().sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        // Add a filter to validate the tokens with every request
        httpSecurity.addFilterBefore(jwtRequestFilter, UsernamePasswordAuthenticationFilter.class);
    }

    @Bean
    public OAuth2UserService<OAuth2UserRequest, OAuth2User> oAuth2UserService() {
        return customOAuth2UserService;
    }

    @Bean
    public HttpCookieOAuth2AuthorizationRequestRepository cookieAuthorizationRequestRepository() {
        return new HttpCookieOAuth2AuthorizationRequestRepository();
    }


}