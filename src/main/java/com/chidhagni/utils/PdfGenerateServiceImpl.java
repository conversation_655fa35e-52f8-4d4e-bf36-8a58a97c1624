package com.chidhagni.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;
import com.itextpdf.html2pdf.HtmlConverter;

import java.io.*;

@Service
public class PdfGenerateServiceImpl implements PdfGenerateService {
    private Logger logger = LoggerFactory.getLogger(PdfGenerateServiceImpl.class);

    @Autowired
    private SpringTemplateEngine templateEngine;


 //To generate pdf in Local using in Integration test Case
//@Override
//public void generatePdfFile(String templateName, Context context, String pdfFileName) {
//
//    String htmlContent = templateEngine.process(templateName, context);
//    try {
//        File pdfFile = new File(pdfFileName); // Adjust the path and filename as necessary
//
//        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
//            HtmlConverter.convertToPdf(htmlContent, fos);
//            FileSystemResource resource = new FileSystemResource(pdfFile);
//
//        } catch (IOException e) {
//            throw new RuntimeException("Error during PDF creation", e);
//        }
//
//    } catch (Exception e) {
//        logger.error(e.getMessage(), e);
//    }
//}
    @Override
    public byte[] generatePdfFile(String templateName, Context context, String pdfFileName) {
        String htmlContent = templateEngine.process(templateName, context);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            HtmlConverter.convertToPdf(htmlContent, baos);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException("Error during PDF creation", e);
        }
        return baos.toByteArray();
    }

    @Override
    public File storePdfFileFromBytes(byte[] content, String filename) {
        File pdfFile = new File(filename);
        try (FileOutputStream fos = new FileOutputStream(pdfFile)) {
            fos.write(content);
        } catch (IOException e) {
            logger.error("Error during PDF", e);
            throw new RuntimeException("Error during PDF", e);
        }
        return pdfFile;
    }


}