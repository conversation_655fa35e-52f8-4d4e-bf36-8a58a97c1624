package com.chidhagni.utils;


import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.individualpermission.IndividualRolesToIndividualPermissionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;
import com.chidhagni.donationreceipt.individualpermission.IndividualPermissionService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@AllArgsConstructor
public class PermissionsData {

    public Map<Integer, String> permissions;
    private final IndividualRolesToIndividualPermissionService individualRolesToIndividualPermissionService;
    private final IndividualRoleDao individualRoleDao;

    public List<GrantedAuthority> getIndividualAuthorities(Individual individual) {
        List<GrantedAuthority> authorities = new ArrayList<>();

        List<IndividualRole> individualRole = individualRoleDao.fetchByIndividualId(individual.getId());

        String rolesString = "";
        if (individualRole.get(0).getRoleId() != null) {
            rolesString = individualRolesToIndividualPermissionService.getPermissionsByIndividualId(individual.getId());
            // Check if rolesString is non-empty before adding
            if (rolesString != null && !rolesString.isEmpty()) {
                authorities.add(new SimpleGrantedAuthority(rolesString));
            } else {
                log.error("Permissions not found for user ID: " + individual.getId());
            }
        } else {
            log.error("Role is not mapped for this user.");
        }
        return authorities;
    }
}