package com.chidhagni.utils;


//import com.chidhagni.donation_receipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;

@Component
public class JwtTokenUtil implements Serializable {
    private static final long serialVersionUID = -2550185165626007488L;

    @Value("${jwt.secret}")
    private String secret;

    @Value("${accessToken.expiry}")
    private long accessTokenExpiryTime;

    @Value("${refreshToken.expiry}")
    private long refreshTokenExpiryTime;

    // retrieve username from jwt token
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    // retrieve expiration date from jwt token
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    // for retrieving any information from token we will need the secret key
    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parserBuilder().setSigningKey(secret).build().parseClaimsJws(token).getBody();
    }

    // check if the token has expired
    public Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

    // while creating the token -
    // 1. Define claims of the token, like Issuer, Expiration, Subject, and the ID
    // 2. Sign the JWT using the HS512 algorithm and secret key.
    // 3. According to JWS Compact
    // Serialization(https://tools.ietf.org/html/draft-ietf-jose-json-web-signature-41#section-3.1)
    // compaction of the JWT to a URL-safe string
    private String doGenerateToken(Map<String, Object> claims, String subject, UUID memberId, Long expiry) {
        return Jwts.builder().setClaims(claims).setId(memberId.toString()).setSubject(subject).setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(System.currentTimeMillis() + expiry)).signWith(SignatureAlgorithm.HS512, secret).compact();
    }

    public String generateAccessToken(UserPrincipal userPrincipal) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("EID", userPrincipal.getId()); // Assuming UserPrincipal has getId() method
        return doGenerateToken(claims, userPrincipal.getName(), userPrincipal.getId(), accessTokenExpiryTime);
    }


    // validate token
    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = getUsernameFromToken(token);
        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }

    //Method to get expiry timestamp in UTC LocalDateTime
    public LocalDateTime getExpiryTimestamp(String token) {
        Date expirationDate = getExpirationDateFromToken(token);
        Instant instant = expirationDate.toInstant();
        return LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
    }

    //Method to get issued-at timestamp in UTC LocalDateTime
    public LocalDateTime getIssuedAtTimestamp(String token) {
        Date issuedAtDate = getIssuedAtDateFromToken(token);
        Instant instant = issuedAtDate.toInstant();
        return LocalDateTime.ofInstant(instant, ZoneId.of("UTC"));
    }

    // Helper method to retrieve issued-at date from the token
    private Date getIssuedAtDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getIssuedAt);
    }

    // generate access token for user
    public String generateAccessToken(Individual individual, UUID organisationId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("EID", organisationId);
        return doGenerateToken(claims, individual.getEmail(), individual.getId(), accessTokenExpiryTime);
    }

    // generate refresh token for user
    public String generateRefreshToken(Individual individual, UUID organisationId) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("EID", organisationId);
        return doGenerateToken(claims, individual.getEmail(), individual.getId(), refreshTokenExpiryTime);
    }
}