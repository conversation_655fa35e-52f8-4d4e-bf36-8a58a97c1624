package com.chidhagni.donationreceipt.notificationtemplate;

//import com.chidhagni.houzer.db.jooq.tables.daos.NotificationTemplatesDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

//import static com.chidhagni.houzer.db.jooq.tables.NotificationTemplates.NOTIFICATION_TEMPLATES;

@Repository
@RequiredArgsConstructor
public class NotificationTemplateRepository {
//    private final NotificationTemplatesDao notificationTemplatesDao;
//
//    public UUID getTemplateIdByType(String templateType){
//        try{
//            return notificationTemplatesDao.ctx().select(NOTIFICATION_TEMPLATES.ID)
//                    .from(NOTIFICATION_TEMPLATES)
//                    .where(NOTIFICATION_TEMPLATES.TYPE.eq(templateType))
//                    .limit(1)
//                    .fetchOneInto(UUID.class);
//        }catch(Exception ex){
//            throw new InternalServerError(String.format("An Notification Template is not available with the given [type=%s]", templateType));
//        }
//    }
}