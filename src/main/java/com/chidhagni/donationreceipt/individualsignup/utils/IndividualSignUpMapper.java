package com.chidhagni.donationreceipt.individualsignup.utils;

//import com.chidhagni.houzer.db.jooq.tables.pojos.Individual;
//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualPasswordResetAudit;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Organisation;
//import com.chidhagni.houzer.db.jooq.tables.pojos.OrganisationLeadAssignment;
//import com.chidhagni.houzer.individualsignup.dto.SocietyReadinessReport;
//import com.chidhagni.houzer.individualsignup.dto.request.IndividualSignUpDTO;
//import com.chidhagni.houzer.legalbinding.dto.LegalBindingDTO;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(componentModel = "spring")
public interface IndividualSignUpMapper {

//    ObjectMapper objectMapper = new ObjectMapper();
//
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "firstName", source = "individualSignUpDTO.firstName")
//    @Mapping(target = "lastName", source = "individualSignUpDTO.lastName")
//    @Mapping(target = "email", source = "individualSignUpDTO.email")
//    @Mapping(target = "mobileNumber", source = "individualSignUpDTO.mobileNumber")
//    @Mapping(target = "password", source = "individualSignUpDTO.password")
//    @Mapping(target = "metaData", expression = "java(new com.chidhagni.houzer.individual.IndividualMetaDataDTO()" +
//            ".builder().loggedInSocietyMemberIndividualId(individual.getId()).build())")
//    @Mapping(target = "isActive", expression = "java(true)")
//    @Mapping(target = "createdBy", expression = "java(null)")
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    Individual mapSignUpDtoToIndividual(IndividualSignUpDTO individualSignUpDTO);
//
//    @Mapping(target = "userId", source = "individual.id")
//    @Mapping(target = "servicesProvided", expression = "java(new UUID[0])")
//    @Mapping(target = "systemCode", expression = "java(com.chidhagni.utils.CommonOperations.generateSystemCode(" +
//            "com.chidhagni.houzer.member.SystemCodes.SP))")
//    @Mapping(target = "referenceType", ignore = true)
//    @Mapping(target = "otherReference", ignore = true)
//    @Mapping(target = "individualName", source = "individual.firstName")
//    @Mapping(target = "companyName", source = "individualSignUpDTO.organisationName")
//    @Mapping(target = "companyType", source = "individualSignUpDTO.companyType")
//    @Mapping(target = "designation", ignore = true)
//    @Mapping(target = "otherDesignation", ignore = true)
//    @Mapping(target = "email", source = "individual.email")
//    @Mapping(target = "mobileNumber", source = "individual.mobileNumber")
//    @Mapping(target = "alternateMobileNumber", ignore = true)
//    @Mapping(target = "websiteUrl", ignore = true)
//    @Mapping(target = "socialMediaPresence", ignore = true)
//    @Mapping(target = "portalsRegistered", ignore = true)
//    @Mapping(target = "spAddresses", ignore = true)
//    @Mapping(target = "gstNo", ignore = true)
//    @Mapping(target = "panNo", ignore = true)
//    @Mapping(target = "tanNo", ignore = true)
//    @Mapping(target = "cinNo", ignore = true)
//    @Mapping(target = "teamSize", ignore = true)
//    @Mapping(target = "noOfSalesTeamMembers", ignore = true)
//    @Mapping(target = "typeOfClientServed", ignore = true)
//    @Mapping(target = "anyOtherServiceProvided", ignore = true)
//    @Mapping(target = "anyOtherPortalRegistered", ignore = true)
//    @Mapping(target = "yearsOfExperienceId", ignore = true)
//    @Mapping(target = "yearsOfExperience", ignore = true)
//    @Mapping(target = "lastThreeYearsTurnOver", ignore = true)
//    @Mapping(target = "completedProjectsOrCases", ignore = true)
//    @Mapping(target = "onGoingProjectsOrCases", ignore = true)
//    @Mapping(target = "accountNumber", ignore = true)
//    @Mapping(target = "ifscCode", ignore = true)
//    @Mapping(target = "branch", ignore = true)
//    @Mapping(target = "bankName", ignore = true)
//    @Mapping(target = "awardImages", ignore = true)
//    @Mapping(target = "assignedTo", source = "assignedTo")
//    @Mapping(target = "leadStatus", ignore = true)
//    @Mapping(target = "leadPriority", ignore = true)
//    @Mapping(target = "remarks", ignore = true)
//    @Mapping(target = "packageType", ignore = true)
//    @Mapping(target = "isListingEmpanelled", ignore = true)
//    @Mapping(target = "isMicrositeEmpanelled", ignore = true)
//    @Mapping(target = "isStrategicPartner", ignore = true)
//    @Mapping(target = "createdOn", ignore = true)
//    @Mapping(target = "updatedOn", ignore = true)
//    @Mapping(target = "createdBy", expression = "java(individual.getId())")
//    SpProfileDTO mapIndividualToSpProfileDTO(Individual individual, IndividualSignUpDTO individualSignUpDTO
//            , UUID assignedTo);
//
//    @Mapping(target = "userId", source = "individual.id")
//    @Mapping(target = "name", source = "individualSignUpDTO.organisationName")
//    @Mapping(target = "loginEmail", source = "individual.email")
//    @Mapping(target = "mobileNumber", source = "individual.mobileNumber")
//    @Mapping(target = "systemCode", expression = "java(com.chidhagni.utils.CommonOperations.generateSystemCode(" +
//            "com.chidhagni.houzer.member.SystemCodes.CHS))")
//    @Mapping(target = "googleMapLocation", source = "individualSignUpDTO.googleLocation")
//    @Mapping(target = "assignedTo", source = "assignedTo")
//    @Mapping(target = "createdOn", ignore = true)
//    @Mapping(target = "updatedOn", ignore = true)
//    @Mapping(target = "consentAgreement", source = "individualSignUpDTO.consentAgreement")
//    @Mapping(target = "registeredFor", source = "individualSignUpDTO.registeredFor")
//    @Mapping(target = "readiness", source = "individualSignUpDTO.readiness")
//    @Mapping(target = "societyMemberName", expression = ("java(individualSignUpDTO.getFirstName()" +
//            " + individual.getLastName() != null ? \" \"+individual.getLastName(): \"\")"))
//    @Mapping(target = "referenceType", source = "websiteReferralListValueId")
//    ChsProfileDTO mapIndividualToChsProfileDTO(Individual individual, IndividualSignUpDTO individualSignUpDTO
//            , UUID assignedTo, UUID websiteReferralListValueId);
//
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "name", source = "individualSignUpDTO.organisationName")
//    @Mapping(target = "category", source = "individualSignUpDTO.oragnisationType")
//    @Mapping(target = "isActive", expression = "java(true)")
//    @Mapping(target = "createdBy", source = "individual.id")
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "metaData", ignore = true)
//    Organisation mapIndividualToOrganisation(Individual individual, IndividualSignUpDTO individualSignUpDTO);
//
//    @Mapping(target = "metaData", expression = "java(IndividualSignUpMapper.convertSpProfileDTOToJsonB(spProfileDTO))")
//    @Mapping(target = "createdBy", ignore = true)
//    @Mapping(target = "updatedBy", ignore = true)
//    @Mapping(target = "createdOn", ignore = true)
//    @Mapping(target = "updatedOn", ignore = true)
//    @Mapping(target = "name", ignore = true)
//    void insertSpMetaDataToOrganisation(@MappingTarget Organisation orgsanisation, SpProfileDTO spProfileDTO);
//
//    @Mapping(target = "metaData", expression = "java(IndividualSignUpMapper.convertChsProfileDTOToJsonB(" +
//            "chsProfileDTO))")
//    @Mapping(target = "createdBy", ignore = true)
//    @Mapping(target = "updatedBy", ignore = true)
//    @Mapping(target = "createdOn", ignore = true)
//    @Mapping(target = "updatedOn", ignore = true)
//    @Mapping(target = "name", ignore = true)
//    void insertChsMetaDataToOrganisation(@MappingTarget Organisation orgsanisation
//            , ChsProfileDTO chsProfileDTO);
//
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "organisationId", source = "organisation.id")
//    @Mapping(target = "assignedTo", source = "assignedTo")
//    @Mapping(target = "isActive", expression = "java(true)")
//    @Mapping(target = "createdBy", source = "organisation.createdBy")
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    OrganisationLeadAssignment mapOrganisationToLeadAssignment(Organisation organisation, UUID assignedTo);
//
//    @Mapping(target = "email", expression = "java(individualSignUpDTO.getEmail())")
//    @Mapping(target = "password", expression = "java(individualSignUpDTO.getPassword())")
//    @Mapping(target = "ipAddress", expression = "java(individualSignUpDTO.getIpAddress())")
//    @Mapping(target = "overrideExistingLogins", ignore = true)
//    IndividualLoginRequest mapSignUpDtoToIndividualLoginRequest(IndividualSignUpDTO individualSignUpDTO);
//
////    @Mapping(target = "firstName", expression = "java(societyReadinessReport.getNeedAssessment().getName())")
////    @Mapping(target = "email", expression = "java(societyReadinessReport.getFinancialClosure().getEmailId())")
////    @Mapping(target = "password", ignore = true)
////    @Mapping(target = "mobileNumber", expression = "java(societyReadinessReport.getFinancialClosure()" +
////            ".getContactNumber())")
////    @Mapping(target = "oragnisationType", expression = "java(com.chidhagni.houzer.organisation.constants" +
////            ".DefaultOrganisationCategoryEnums.SOCIETY)")
////    @Mapping(target = "organisationName", expression = "java(societyReadinessReport.getNeedAssessment()" +
////            ".getSocietyName())")
////    @Mapping(target = "ipAddress", source = "ipAddress")
////    @Mapping(target = "companyType", ignore = true)
////    @Mapping(target = "consentAgreement", expression = "java(true)")
////    @Mapping(target = "readiness", expression = "java(com.chidhagni.houzer.organisation.constants.ReadinessEnums.YES)")
////    @Mapping(target = "registeredFor", expression = "java(java.util.List.of(com.chidhagni.houzer.organisation.constants" +
////            ".RegisteredFor.RED))")
////    @Mapping(target = "googleLocation", ignore = true)
////    IndividualSignUpDTO mapIndividualReadinessReportToSignUpDto(SocietyReadinessReport societyReadinessReport
////            , String ipAddress);
//
//
//    @Mapping(target = "firstName", expression = "java(societyReadinessReport.getNeedAssessment() != null ? societyReadinessReport.getNeedAssessment().getName() : null)")
//    @Mapping(target = "email", expression = "java(societyReadinessReport.getFinancialClosure() != null ? societyReadinessReport.getFinancialClosure().getEmailId() : null)")
//    @Mapping(target = "password", ignore = true)
//    @Mapping(target = "mobileNumber", expression = "java(societyReadinessReport.getFinancialClosure() != null ? societyReadinessReport.getFinancialClosure().getContactNumber() : null)")
//    @Mapping(target = "oragnisationType", expression = "java(com.chidhagni.houzer.organisation.constants.DefaultOrganisationCategoryEnums.SOCIETY)")
//    @Mapping(target = "organisationName", expression = "java(societyReadinessReport.getNeedAssessment() != null ? societyReadinessReport.getNeedAssessment().getSocietyName() : null)")
//    @Mapping(target = "ipAddress", source = "ipAddress")
//    @Mapping(target = "companyType", ignore = true)
//    @Mapping(target = "consentAgreement", expression = "java(true)")
//    @Mapping(target = "readiness", expression = "java(com.chidhagni.houzer.organisation.constants.ReadinessEnums.YES)")
//    @Mapping(target = "registeredFor", expression = "java(java.util.List.of(com.chidhagni.houzer.organisation.constants.RegisteredFor.RED))")
//    @Mapping(target = "googleLocation", ignore = true)
//    IndividualSignUpDTO mapIndividualReadinessReportToSignUpDto(SocietyReadinessReport societyReadinessReport, String ipAddress);
//
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "ipAddress", expression = "java(individualSignUpDTO.getIpAddress())")
//    @Mapping(target = "emailId", expression =
//            "java(individualSignUpDTO.getEmail())")
//    @Mapping(target = "individualId", source = "individualId")
//    @Mapping(target = "foolProof", expression = "java(Boolean.TRUE)")
//    @Mapping(target = "typeOfTransaction", ignore = true)
//    @Mapping(target = "legalBindingMetaData", expression =
//            "java(new com.chidhagni.houzer.legalbinding.dto.LegalBindingMetaData().builder().build())")
//    @Mapping(target = "createdBy", source = "individualId")
//    @Mapping(target = "updatedBy", ignore = true)
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    LegalBindingDTO mapIndividualSignUpDtoToLegalBinding(IndividualSignUpDTO individualSignUpDTO, UUID individualId);
//
//    @Mapping(source = "firstName", target = "needAssessment.name")
//    @Mapping(source = "organisationName", target = "needAssessment.societyName")
//    @Mapping(source = "email", target = "financialClosure.emailId")
//    @Mapping(source = "mobileNumber", target = "financialClosure.contactNumber")
//    @Mapping(target = "needAssessment", expression = "java(com.chidhagni.houzer.individualsignup.dto.NeedAssessment.builder().hasYourSocietyConsideredForRedevelopment(\"No\").build())")
//    @Mapping(target = "typesOfRedevelopment", expression = "java(com.chidhagni.houzer.individualsignup.dto.TypesOfRedevelopment.builder().build())")
//    @Mapping(target = "committee", expression = "java(com.chidhagni.houzer.individualsignup.dto.Committee.builder().build())")
//    @Mapping(target = "consents", expression = "java(com.chidhagni.houzer.individualsignup.dto.Consents.builder().build())")
//    @Mapping(target = "conveyance", expression = "java(com.chidhagni.houzer.individualsignup.dto.Conveyance.builder().build())")
//    @Mapping(target = "managingCommittee", expression = "java(com.chidhagni.houzer.individualsignup.dto.ValidManagingCommittee.builder().build())")
//    @Mapping(target = "documents", expression = "java(com.chidhagni.houzer.individualsignup.dto.Documents.builder().build())")
//    @Mapping(target = "appointmentOfProfessionals", expression = "java(com.chidhagni.houzer.individualsignup.dto.AppointmentOfProfessionals.builder().build())")
//    @Mapping(target = "preTenderingStage", expression = "java(com.chidhagni.houzer.individualsignup.dto.PreTenderingStage.builder().build())")
//    @Mapping(target = "tenderingStage", expression = "java(com.chidhagni.houzer.individualsignup.dto.TenderingStage.builder().build())")
//    @Mapping(target = "financialClosure", expression = "java(com.chidhagni.houzer.individualsignup.dto.FinancialClosure.builder().haveSecuredFinancialClosure(\"No\").build())")
//    @Mapping(target = "percentage", constant = "0")
//    @Mapping(target = "generateReport", constant = "false")
//    SocietyReadinessReport mapToSocietyReadinessReport(IndividualSignUpDTO individualSignUpDTO);
//
//    static JSONB convertSpProfileDTOToJsonB(SpProfileDTO spProfileDTO) {
//        try {
//            return JSONB.valueOf(objectMapper.writeValueAsString(spProfileDTO));
//        } catch (Exception e) {
//            throw new RuntimeException("Error converting SpProfileDTO to JSONB", e);
//        }
//    }
//
//    static JSONB convertChsProfileDTOToJsonB(ChsProfileDTO chsProfileDTO) {
//        try {
//            return JSONB.valueOf(objectMapper.writeValueAsString(chsProfileDTO));
//        } catch (Exception e) {
//            throw new RuntimeException("Error converting SocietyRegisterDTO to JSONB", e);
//        }
//    }
//
    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
    @Mapping(target = "individualId", source = "individual.id")
    @Mapping(target = "email", source = "individual.email")
    @Mapping(target = "resetLink", ignore = true)
    @Mapping(target = "resetLinkRequestedAt", ignore = true)
    @Mapping(target = "resetLinkExpiresAt", ignore = true)
    @Mapping(target = "resetCompletedAt", ignore = true)
    @Mapping(target = "isActive", expression = "java(true)")
    @Mapping(target = "createdBy", source = "individual.id")
    @Mapping(target = "updatedBy", ignore = true)
    IndividualPasswordResetAudit mapIndividualToPasswordResetAudit(Individual individual);
}

