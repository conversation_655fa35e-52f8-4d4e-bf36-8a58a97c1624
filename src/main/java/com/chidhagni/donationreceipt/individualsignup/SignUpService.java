package com.chidhagni.donationreceipt.individualsignup;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.*;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.individual.util.IndividualHelperMethods;
import com.chidhagni.donationreceipt.individualPasswordResetAudit.IndividualPasswordResetRepository;
import com.chidhagni.donationreceipt.individualPasswordResetAudit.utils.IndividualPasswordResetAuditMapper;
import com.chidhagni.donationreceipt.individualpermission.IndividualPermissionRepository;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.individualsignup.dto.request.ActivationDTO;
import com.chidhagni.donationreceipt.individualsignup.dto.request.IndividualOrganisationResponse;
import com.chidhagni.donationreceipt.individualsignup.dto.request.IndividualSignUpDTO;
import com.chidhagni.donationreceipt.individualsignup.dto.request.ProcessEnum;
import com.chidhagni.donationreceipt.individualsignup.utils.IndividualSignUpHelperMethods;
import com.chidhagni.donationreceipt.individualsignup.utils.IndividualSignUpMapper;
import com.chidhagni.donationreceipt.individualsignup.utils.SignUpMapper;
import com.chidhagni.donationreceipt.individualverificationaudit.IndividualVerificationAuditRepository;
import com.chidhagni.donationreceipt.individualverificationaudit.IndividualVerificationAuditService;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import com.chidhagni.donationreceipt.individualverificationaudit.utils.IndividualVerificationMapper;
import com.chidhagni.donationreceipt.mail.EmailHelperService;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.roles.RolesRepository;
import com.chidhagni.utils.DateUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.mail.MessagingException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class SignUpService {
    private final IndividualVerificationMapper individualVerificationMapper;
    private final IndividualVerificationAuditRepository individualVerificationAuditRepository;
    private final NotificationManager notificationManager;
    private final IndividualHelperMethods individualHelperMethods;
    private final IndividualVerificationAuditService individualVerificationAuditService;
    private final ObjectMapper objectMapper;
    private final IndividualRepository individualRepository;
    private final OrganizationRepository organizationRepository;
    private final IndividualRoleRepository individualRoleRepository;
    private final IndividualPermissionRepository individualPermissionRepository;
    private final RolesRepository rolesRepository;
    private final IndividualPasswordResetRepository individualPasswordResetRepository;
    private final PasswordEncoder passwordEncoder;
    private final IndividualPasswordResetAuditMapper individualPasswordResetAuditMapper;
    private final IndividualSignUpMapper individualSignUpMapper;
    private final EmailHelperService emailHelperService;
    private final SignUpMapper signUpMapper;
    private final IndividualSignUpHelperMethods individualSignUpHelperMethods;

    @Value("${server.hostUrl}")
    public String resetUrl;

    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;

    @Value("${roles.finance.account}")
    private UUID financeAccountant;

    @Value("${roles.data.entry-operator}")
    private UUID dataEntryOperator;

    @Value("${roles.donor}")
    private UUID donorRoleId;

    @Value("${donor.type.entity}")
    private UUID entityDonorType;


    public void signUp(IndividualSignUpDTO signUpDTO) throws JsonProcessingException, MessagingException {
        List<IndividualVerificationAudit> existingAudits = individualVerificationAuditRepository.getByEmail(signUpDTO.getEmail());

        if (!existingAudits.isEmpty()) {
            IndividualVerificationAudit existingAudit = existingAudits.get(0);

            if (existingAudit.getVerificationStatus().equals(VerificationStatusEnum.VERIFIED.name())) {
                throw new IllegalArgumentException("Email already verified. Please login.");
            } else if (existingAudit.getVerificationStatus().equals(VerificationStatusEnum.PENDING.name()) &&
                    existingAudit.getActivationLinkExpiresAt().isAfter(LocalDateTime.now())) {
                throw new IllegalArgumentException("Activation link already sent. Please check your email.");
            } else {
                log.info("Activation link expired. Resending activation link.");
                String activationLink = emailHelperService.generateActivationLink(signUpDTO.getEmail(), existingAudit.getRoleId());
                notificationManager.sendActivationLink(signUpDTO.getEmail(), activationLink, signUpDTO.getTrustName());
                return;
            }
        }
        // No existing verification
        if (signUpDTO.getRoleId()!=null&&signUpDTO.getRoleId().equals(donorRoleId)) {

            IndividualVerificationAudit newAudit = individualVerificationMapper.createEmailIndividualVerificationDonor(signUpDTO);
            log.info("Inserting new individual verification record for donor.");
            individualVerificationAuditRepository.insertIndividualVerificationAudit(newAudit);

            log.info("Generating activation link for donor.");
            String activationLink = emailHelperService.generateActivationLink(signUpDTO.getEmail(), newAudit.getRoleId());

            log.info("Sending activation link to registered email for donor.");
            notificationManager.sendActivationLinkDonor(signUpDTO.getEmail(), activationLink, signUpDTO.getDonorName());
        } else {
            IndividualVerificationAudit newAudit = individualVerificationMapper.createIndividualVerification(signUpDTO);
            log.info("Inserting new individual verification record.");
            individualVerificationAuditRepository.insertIndividualVerificationAudit(newAudit);

            log.info("Generating activation link.");
            String activationLink = emailHelperService.generateActivationLink(signUpDTO.getEmail(), newAudit.getRoleId());

            log.info("Sending activation link to registered email.");
            notificationManager.sendActivationLink(signUpDTO.getEmail(), activationLink, signUpDTO.getTrustName());

        }
    }
    @Transactional
    public void activate(ActivationDTO activationDTO) {

        List<IndividualVerificationAudit> individualVerificationAudit =
                individualVerificationAuditRepository.getByEmail(activationDTO.getEmail());

        if (individualVerificationAudit.isEmpty()) {
            throw new IllegalArgumentException("Email Not found. Please Sign Up");
        }

        if (VerificationStatusEnum.VERIFIED.name().equalsIgnoreCase(individualVerificationAudit.get(0).getVerificationStatus())
                && Boolean.TRUE.equals(individualVerificationAudit.get(0).getActivationLink())) {
            throw new IllegalArgumentException("Account is already activated. Please log in.");
        }
        if (activationDTO.getProcessEnum().equals(ProcessEnum.account_activation) &&
                activationDTO.getRoleId().equals(tenantAdminRoleId)) {

            IndividualVerificationAudit individualVerificationAuditUpdated =
                    individualVerificationAuditService.updateActivateLink(individualVerificationAudit.get(0));
            log.info("Saving the data into individual and organisation");
            IndividualOrganisationResponse individualOrganisationResponse =
                    individualSignUpHelperMethods.saveIndividualAndOrganisation(individualVerificationAuditUpdated);
            individualVerificationAuditUpdated.setOrgId(individualOrganisationResponse.getOrganisationId());
            individualVerificationAuditRepository.updateIndividualVerificationAudit(individualVerificationAuditUpdated);
            signUpMapper.saveIndividualDerived(activationDTO.getRoleId(), individualOrganisationResponse);
            createRoles(individualOrganisationResponse.getOrganisationId(), individualOrganisationResponse.getIndividualId());
        }
        else if(activationDTO.getProcessEnum().equals(ProcessEnum.account_activation) &&
                activationDTO.getRoleId().equals(donorRoleId)){

            IndividualVerificationAudit individualVerificationAuditUpdated =
                    individualVerificationAuditService.updateActivateLink(individualVerificationAudit.get(0));
            saveDonor(individualVerificationAuditUpdated);
        }else {
            IndividualVerificationAudit individualVerificationAuditUpdated =
                    individualVerificationAuditService.updateActivateLink(individualVerificationAudit.get(0));
            log.info("Successfully Update ActivateLink");
            IndividualOrganisationResponse individualOrganisationResponse =
                    individualSignUpHelperMethods.saveIndividualAndOrganisation(individualVerificationAuditUpdated);
            signUpMapper.saveIndividualDerived(activationDTO.getRoleId(), individualOrganisationResponse);

        }
    }


    public void createPassword(String emailId, String password) {
        Individual individual = individualRepository.getByEmail(emailId);

        if (individual == null) {
            throw new IllegalArgumentException("Individual not found with the given email. Please verify your email and try again.");
        }
        if (passwordEncoder.matches(password, individual.getPassword())) {
            throw new IllegalArgumentException("New password cannot be the same as the old password");
        }
        individual.setPassword(passwordEncoder.encode(password));
        individual.setUpdatedOn(DateUtils.currentTimeIST());
        individual.setUpdatedBy(individual.getId());
        individualRepository.update(individual);
    }

    public void forgotPassword(String email) throws MessagingException {
        Individual individual = individualRepository.getByEmail(email);
        if (individual == null) {
            throw new IllegalArgumentException("Email Id Not Found. Please use the email which is used for sign up");
        }
        UUID individualId = individual.getId();
        if (!individualPasswordResetRepository.isExistPasswordResetAuditByIndividualId(individualId)) {
            log.info("no individual password reset audit found for individual id :: {}, so inserting one", individualId);
            insertPasswordResetAudit(individual);
        }

        List<IndividualRole> individualRole = individualRoleRepository.getByIndivdiualId(individual.getId());
        IndividualPasswordResetAudit individualPasswordResetAudit
                = individualPasswordResetRepository.getByIndividualId(individualId);
        IndividualPasswordResetAudit generateNewResetCode
                = emailHelperService.generateNewResetCode(individualPasswordResetAudit);
        individualPasswordResetRepository.update(generateNewResetCode);
        notificationManager.sendResetPasswordLink(individual, generateNewResetCode, individualRole.get(0).getRoleId());
    }

    public void insertPasswordResetAudit(Individual individual) {
        IndividualPasswordResetAudit individualPasswordResetAudit
                = individualSignUpMapper.mapIndividualToPasswordResetAudit(individual);
        individualPasswordResetRepository.create(individualPasswordResetAudit);
    }


    public void resetPassword(String emailId, String resetCode, String password) {

        Individual individual = individualRepository.getByEmail(emailId);
        if (individual == null) {
            throw new IllegalArgumentException("Individual not found with the given email. Please verify your email and try again.");
        }

        IndividualPasswordResetAudit individualPasswordResetAudit
                = individualPasswordResetRepository.getByIndividualId(individual.getId());

        if (individualPasswordResetAudit == null || !individualPasswordResetAudit.getResetLink().equals(resetCode)) {
            throw new IllegalArgumentException("Invalid or expired reset code.");
        }
        if (passwordEncoder.matches(password, individual.getPassword())) {
            throw new IllegalArgumentException("New password cannot be the same as the old password");
        }

        individual.setPassword(passwordEncoder.encode(password));
        individualRepository.update(individual);

        IndividualPasswordResetAudit individualPasswordResetAuditMap = individualPasswordResetAuditMapper
                .map(individualPasswordResetAudit, individual.getId());
        individualPasswordResetRepository.update(individualPasswordResetAuditMap);
    }


    private void createRoles(UUID organisationId, UUID individualId) {
        createAndSaveRole(financeAccountant, organisationId, individualId);
        createAndSaveRole(dataEntryOperator, organisationId, individualId);
    }

    private void createAndSaveRole(UUID roleId, UUID organisationId, UUID individualId) {
        Roles role = Objects.requireNonNull(rolesRepository.getRoleById(roleId), "Role not found");
        role.setOrgId(organisationId);
        role.setId(UUID.randomUUID());
        role.setCreatedBy(individualId);
        role.setCreatedOn(DateUtils.currentTimeIST());
        role.setUpdatedBy(individualId);
        role.setUpdatedOn(DateUtils.currentTimeIST());
        rolesRepository.createRole(role);
    }


    public void saveDonor(IndividualVerificationAudit individualVerificationAudit)
    {

        UUID individualId = UUID.randomUUID();
        UUID orgId = UUID.randomUUID();
        Individual individual = individualSignUpHelperMethods.createIndividual(individualVerificationAudit, individualId);
        individualRepository.insertIndividual(individual);
        log.info("Inserted into individuals");

        Organisation organisation = individualSignUpHelperMethods.createOrganisation(individualVerificationAudit, individual, orgId);
        organizationRepository.insertOrganisation(organisation);
        log.info("Inserted into organisation");

        individualSignUpHelperMethods.insertIndividualRole(individualVerificationAudit, individualId, orgId);
        individualSignUpHelperMethods.insertIndividualPermissions(individualVerificationAudit, individualId, orgId);
        individualSignUpHelperMethods.insertPasswordResetAudit(individualVerificationAudit, individualId);
    }

}
