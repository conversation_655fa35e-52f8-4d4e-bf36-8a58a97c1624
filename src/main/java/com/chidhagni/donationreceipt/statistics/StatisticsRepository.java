package com.chidhagni.donationreceipt.statistics;

import com.chidhagni.donationreceipt.db.jooq.tables.daos.DonationHeadsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.DonationReceiptsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.OrganisationDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.donationreceipts.DonationReceiptRepository;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.statistics.dto.DonationAmount;
import com.chidhagni.donationreceipt.statistics.dto.DonationHeadDistribution;
import com.chidhagni.donationreceipt.statistics.dto.GraphType;
import com.chidhagni.donationreceipt.statistics.dto.SignupGraphData;
import liquibase.pro.packaged.B;
import lombok.RequiredArgsConstructor;
import org.jooq.DatePart;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.Month;
import java.time.format.TextStyle;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS;
import static com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;


@RequiredArgsConstructor
@Repository
public class StatisticsRepository {

    private final DonationHeadsDao donationHeadsDao;
    private final DonationReceiptsDao donationReceiptsDao;

    private final OrganizationRepository organizationRepository;
    private final OrganisationDao organisationDao;
    private final DonationReceiptRepository donationReceiptRepository;

    public List<DonationHeadDistribution> getDonationHeadDistribution() {
        try {
            Integer totalReceipts = donationReceiptsDao.ctx().selectCount()
                    .from(DONATION_RECEIPTS)
                    .fetchOneInto(Integer.class);

            if (totalReceipts == 0) {
                return Collections.emptyList();
            }
            return donationHeadsDao.ctx().select(
                            DONATION_HEADS.NAME.as("donationHeadName"),
                            DSL.round(
                                    DSL.count(DONATION_RECEIPTS.ID)
                                            .mul(100.0)
                                            .div(totalReceipts), 2
                            ).cast(String.class).as("percentage")
                    )
                    .from(DONATION_HEADS)
                    .leftJoin(DONATION_RECEIPTS)
                    .on(DONATION_RECEIPTS.DONATION_HEAD_ID.eq(DONATION_HEADS.ID))
                    .groupBy(DONATION_HEADS.ID, DONATION_HEADS.NAME)
                    .fetchInto(DonationHeadDistribution.class);
        }
        catch (Exception e) {
            System.err.println("An unexpected error occurred: " + e.getMessage());
            return Collections.emptyList();
        }
    }


    public List<SignupGraphData> getSignupGraphData(GraphType graphType) {
        List<SignupGraphData> response = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        switch (graphType) {
            case YEARLY:
                int currentYear = now.getYear();
                List<Integer> lastFiveYears = IntStream.rangeClosed(currentYear - 4, currentYear)
                        .boxed()
                        .collect(Collectors.toList());

                Map<Integer, Integer> yearToCountMap = organisationDao.ctx().select(
                                DSL.extract(ORGANISATION.CREATED_ON, DatePart.YEAR).as("year"),
                                DSL.count().as("count")
                        )
                        .from(ORGANISATION)
                        .where(DSL.extract(ORGANISATION.CREATED_ON, DatePart.YEAR).in(lastFiveYears))
                        .groupBy(DSL.extract(ORGANISATION.CREATED_ON, DatePart.YEAR))
                        .fetch()
                        .stream()
                        .collect(Collectors.toMap(
                                record -> record.get("year", Integer.class),
                                record -> record.get("count", Integer.class)
                        ));

                response = lastFiveYears.stream()
                        .map(year -> new SignupGraphData(
                                String.valueOf(year),
                                yearToCountMap.getOrDefault(year, 0)
                        ))
                        .collect(Collectors.toList());
                break;
            case MONTHLY:
                int currentYearForMonth = now.getYear();

                List<Month> allMonths = Arrays.asList(Month.values()); // JAN to DEC

                Map<Month, Integer> monthToCountMap = organisationDao.ctx().select(
                                DSL.extract(ORGANISATION.CREATED_ON, DatePart.MONTH).as("month"),
                                DSL.count().as("count")
                        )
                        .from(ORGANISATION)
                        .where(DSL.extract(ORGANISATION.CREATED_ON, DatePart.YEAR).eq(currentYearForMonth))
                        .groupBy(DSL.extract(ORGANISATION.CREATED_ON, DatePart.MONTH))
                        .fetch()
                        .stream()
                        .collect(Collectors.toMap(
                                record -> Month.of(record.get("month", Integer.class)),
                                record -> record.get("count", Integer.class)
                        ));

                response = allMonths.stream()
                        .map(month -> new SignupGraphData(
                                month.name(),   // label as "JANUARY", "FEBRUARY" etc.
                                monthToCountMap.getOrDefault(month, 0)
                        ))
                        .collect(Collectors.toList());
                break;


            case WEEKLY:
                List<SignupGraphData> weeklyData = new ArrayList<>();

                LocalDate today = LocalDate.now();
                LocalDate eightWeeksAgo = today.minusWeeks(7); // because current week + 7 past = 8 total

                Map<Integer, Integer> weekNumberToCountMap = organisationDao.ctx().select(
                                DSL.extract(ORGANISATION.CREATED_ON, DatePart.WEEK).as("week"),
                                DSL.count().as("count")
                        )
                        .from(ORGANISATION)
                        .where(ORGANISATION.CREATED_ON.between(eightWeeksAgo.atStartOfDay(), today.atTime(LocalTime.MAX)))
                        .groupBy(DSL.extract(ORGANISATION.CREATED_ON, DatePart.WEEK))
                        .fetch()
                        .stream()
                        .collect(Collectors.toMap(
                                record -> record.get("week", Integer.class),
                                record -> record.get("count", Integer.class)
                        ));

                for (int i = 7; i >= 0; i--) {
                    LocalDate weekStartDate = today.minusWeeks(i);
                    int weekNumber = weekStartDate.get(WeekFields.ISO.weekOfWeekBasedYear());
                    Month month = weekStartDate.getMonth();
                    int weekInMonth = (weekStartDate.getDayOfMonth() - 1) / 7 + 1;

                    String label = month.getDisplayName(TextStyle.SHORT, Locale.ENGLISH) + "W" + weekInMonth;
                    int count = weekNumberToCountMap.getOrDefault(weekNumber, 0);

                    weeklyData.add(new SignupGraphData(label, count));
                }

                response = weeklyData;
                break;
            case DAILY:
                List<SignupGraphData> dailyData = new ArrayList<>();

                LocalDate todayDaily = LocalDate.now();
                LocalDateTime startOfDay = todayDaily.atStartOfDay();
                LocalDateTime endOfDay = todayDaily.atTime(LocalTime.MAX);

                List<Organisation> todaysOrganisations = organizationRepository.findAllByCreatedOnBetween(startOfDay, endOfDay);
                LinkedHashMap<String, Integer> slots = new LinkedHashMap<>();
                slots.put("12AM-4AM", 0);
                slots.put("4AM-8AM", 0);
                slots.put("8AM-12PM", 0);
                slots.put("12PM-4PM", 0);
                slots.put("4PM-8PM", 0);
                slots.put("8PM-12AM", 0);

                for (Organisation org : todaysOrganisations) {
                    int hour = org.getCreatedOn().getHour();

                    if (hour >= 0 && hour < 4) {
                        slots.put("12AM-4AM", slots.get("12AM-4AM") + 1);
                    } else if (hour >= 4 && hour < 8) {
                        slots.put("4AM-8AM", slots.get("4AM-8AM") + 1);
                    } else if (hour >= 8 && hour < 12) {
                        slots.put("8AM-12PM", slots.get("8AM-12PM") + 1);
                    } else if (hour >= 12 && hour < 16) {
                        slots.put("12PM-4PM", slots.get("12PM-4PM") + 1);
                    } else if (hour >= 16 && hour < 20) {
                        slots.put("4PM-8PM", slots.get("4PM-8PM") + 1);
                    } else if (hour >= 20 && hour < 24) {
                        slots.put("8PM-12AM", slots.get("8PM-12AM") + 1);
                    }
                }

                for (Map.Entry<String, Integer> entry : slots.entrySet()) {
                    dailyData.add(new SignupGraphData(entry.getKey(), entry.getValue()));
                }

                response = dailyData;
                break;

            default:
                throw new IllegalArgumentException("Invalid Type.Please Select Type such as Yearly,Monthly,Weekly,Daily only");
        }

            return response;
    }

    public List<DonationAmount> getDonationAmount(GraphType graphType) {
        List<DonationAmount> response = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        switch (graphType) {
            case YEARLY:
                int currentYear = now.getYear();
                List<Integer> lastFiveYears = IntStream.rangeClosed(currentYear - 4, currentYear)
                        .boxed()
                        .collect(Collectors.toList());

                Map<Integer, BigDecimal> yearToAmountMap = donationReceiptsDao.ctx().select(
                                DSL.extract(DONATION_RECEIPTS.CREATED_ON, DatePart.YEAR).as("year"),
                                DSL.sum(DSL.field("CAST(meta_data->>'amount' AS NUMERIC)", BigDecimal.class)).as("amount")
                        )
                        .from(DONATION_RECEIPTS)
                        .where(DSL.extract(DONATION_RECEIPTS.CREATED_ON, DatePart.YEAR).in(lastFiveYears))
                        .groupBy(DSL.extract(DONATION_RECEIPTS.CREATED_ON, DatePart.YEAR))
                        .fetch()
                        .stream()
                        .collect(Collectors.toMap(
                                record -> record.get("year", Integer.class),
                                record -> record.get("amount", BigDecimal.class)
                        ));

                response = lastFiveYears.stream()
                        .map(year -> new DonationAmount(
                                String.valueOf(year),
                                yearToAmountMap.getOrDefault(year, BigDecimal.ZERO)
                        ))
                        .collect(Collectors.toList());
                break;

            case MONTHLY:
                int currentYearForMonth = now.getYear();

                List<Month> allMonths = Arrays.asList(Month.values());

                Map<Integer, BigDecimal> monthToAmountMap = donationReceiptsDao.ctx().select(
                                DSL.extract(DONATION_RECEIPTS.CREATED_ON, DatePart.MONTH).as("month"),
                                DSL.sum(DSL.field("CAST(meta_data->>'amount' AS NUMERIC)", BigDecimal.class)).as("amount")
                        )
                        .from(DONATION_RECEIPTS)
                        .where(DSL.extract(DONATION_RECEIPTS.CREATED_ON, DatePart.YEAR).eq(currentYearForMonth))
                        .groupBy(DSL.extract(DONATION_RECEIPTS.CREATED_ON, DatePart.MONTH))
                        .fetch()
                        .stream()
                        .collect(Collectors.toMap(
                                record -> record.get("month", Integer.class),
                                record -> record.get("amount", BigDecimal.class)
                        ));

                response = allMonths.stream()
                        .map(month -> new DonationAmount(
                                month.name(),
                                monthToAmountMap.getOrDefault(month.getValue(), BigDecimal.ZERO)
                        ))
                        .collect(Collectors.toList());
                break;

            case WEEKLY:
                List<DonationAmount> weeklyData = new ArrayList<>();

                LocalDate today = LocalDate.now();
                LocalDate eightWeeksAgo = today.minusWeeks(7);

                Map<Integer, BigDecimal> weekNumberToAmountMap = donationReceiptsDao.ctx().select(
                                DSL.extract(DONATION_RECEIPTS.CREATED_ON, DatePart.WEEK).as("week"),
                                DSL.sum(DSL.field("CAST(meta_data->>'amount' AS NUMERIC)", BigDecimal.class)).as("amount")
                        )
                        .from(DONATION_RECEIPTS)
                        .where(DONATION_RECEIPTS.CREATED_ON.between(eightWeeksAgo.atStartOfDay(), today.atTime(LocalTime.MAX)))
                        .groupBy(DSL.extract(DONATION_RECEIPTS.CREATED_ON, DatePart.WEEK))
                        .fetch()
                        .stream()
                        .collect(Collectors.toMap(
                                record -> record.get("week", Integer.class),
                                record -> record.get("amount", BigDecimal.class)
                        ));

                for (int i = 7; i >= 0; i--) {
                    LocalDate weekStartDate = today.minusWeeks(i);
                    int weekNumber = weekStartDate.get(WeekFields.ISO.weekOfWeekBasedYear());
                    Month month = weekStartDate.getMonth();
                    int weekInMonth = (weekStartDate.getDayOfMonth() - 1) / 7 + 1;

                    String label = month.getDisplayName(TextStyle.SHORT, Locale.ENGLISH) + "W" + weekInMonth;
                    BigDecimal amount = weekNumberToAmountMap.getOrDefault(weekNumber, BigDecimal.ZERO);

                    weeklyData.add(new DonationAmount(label, amount));
                }

                response = weeklyData;
                break;

            case DAILY:
                List<DonationAmount> dailyData = new ArrayList<>();

                LocalDate todayDaily = LocalDate.now();
                LocalDateTime startOfDay = todayDaily.atStartOfDay();
                LocalDateTime endOfDay = todayDaily.atTime(LocalTime.MAX);

                List<DonationReceipts> todaysReceipts = donationReceiptRepository.findAllByCreatedOnBetween(startOfDay, endOfDay);

                LinkedHashMap<String, BigDecimal> slots = new LinkedHashMap<>();
                slots.put("12AM-4AM", BigDecimal.ZERO);
                slots.put("4AM-8AM", BigDecimal.ZERO);
                slots.put("8AM-12PM", BigDecimal.ZERO);
                slots.put("12PM-4PM", BigDecimal.ZERO);
                slots.put("4PM-8PM", BigDecimal.ZERO);
                slots.put("8PM-12AM", BigDecimal.ZERO);

                for (DonationReceipts receipt : todaysReceipts) {
                    int hour = receipt.getCreatedOn().getHour();
                    BigDecimal amount = getAmountFromMetaData(receipt.getMetaData());

                    if (hour >= 0 && hour < 4) {
                        slots.put("12AM-4AM", slots.get("12AM-4AM").add(amount));
                    } else if (hour >= 4 && hour < 8) {
                        slots.put("4AM-8AM", slots.get("4AM-8AM").add(amount));
                    } else if (hour >= 8 && hour < 12) {
                        slots.put("8AM-12PM", slots.get("8AM-12PM").add(amount));
                    } else if (hour >= 12 && hour < 16) {
                        slots.put("12PM-4PM", slots.get("12PM-4PM").add(amount));
                    } else if (hour >= 16 && hour < 20) {
                        slots.put("4PM-8PM", slots.get("4PM-8PM").add(amount));
                    } else if (hour >= 20 && hour < 24) {
                        slots.put("8PM-12AM", slots.get("8PM-12AM").add(amount));
                    }
                }

                for (Map.Entry<String, BigDecimal> entry : slots.entrySet()) {
                    dailyData.add(new DonationAmount(entry.getKey(), entry.getValue()));
                }

                response = dailyData;
                break;

            default:
                throw new IllegalArgumentException("Invalid Type.Please Select Type such as Yearly,Monthly,Weekly,Daily only");
        }

        return response;
    }

    private BigDecimal getAmountFromMetaData(DonationReceiptMetaDataDTO metaData) {
        if (metaData == null || metaData.getAmount() == null) {
            return BigDecimal.ZERO;  // Handle null case
        }

        try {
            String amountString = (String) metaData.getAmount();
            return new BigDecimal(amountString);
        } catch (NumberFormatException e) {
            System.err.println("Invalid format for amount: " + metaData.getAmount());
            return BigDecimal.ZERO;
        } catch (ClassCastException e) {
            System.err.println("Expected a String for amount, but got: " + metaData.getAmount().getClass());
            return BigDecimal.ZERO;
        }
    }


}
