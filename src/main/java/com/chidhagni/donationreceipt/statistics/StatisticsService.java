package com.chidhagni.donationreceipt.statistics;


import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.statistics.dto.*;

import com.chidhagni.donationreceipt.statistics.dto.DonationAmount;
import com.chidhagni.donationreceipt.statistics.dto.DonationHeadDistribution;
import com.chidhagni.donationreceipt.statistics.dto.GraphType;
import com.chidhagni.donationreceipt.statistics.dto.SignupGraphData;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Result;
import org.jooq.exception.DataAccessException;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.Tables.*;
import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsService {


    private final DSLContext dsl;

    private final StatisticsRepository statisticsRepository;

    public DonationReceiptsCardsResponse getSuperAdminDashboard() {
        try {
            Long totalTenantsRegistered = dsl.selectCount()
                    .from(ORGANISATION)
                    .where(ORGANISATION.CATEGORY.eq(OrganisationEnums.TENANT.name()))
                    .fetchOne(0, Long.class);

            Result<Record1<String>> amounts = dsl.select(DSL.field("meta_data->>'amount'").cast(String.class))
                    .from(DONATION_RECEIPTS)
                    .fetch();

            Long totalTenantsDonationsReceived = amounts.stream()
                    .map(Record1::value1)
                    .filter(a -> a != null && !a.trim().isEmpty()) // Filter out nulls and blanks
                    .map(this::parseAmount)
                    .filter(a -> a.matches("\\d+")) // Ensure it's all digits
                    .mapToLong(Long::parseLong)
                    .sum();

            Long last30DaysDonations = Optional.ofNullable(
                    dsl.selectCount()
                            .from(DONATION_RECEIPTS)
                            .where(DONATION_RECEIPTS.RECEIPT_DATE.greaterOrEqual(LocalDate.now().minusDays(30)))
                            .fetchOneInto(Long.class)
            ).orElse(0L);


            Double averageDonation = amounts.stream()
                    .map(Record1::value1)
                    .filter(a -> a != null && !a.trim().isEmpty())
                    .map(this::parseAmount)
                    .filter(a -> a.matches("\\d+")) // Only keep numeric strings
                    .mapToLong(Long::parseLong)
                    .average()
                    .orElse(0.0);


            return DonationReceiptsCardsResponse.builder()
                    .totalTenantsRegistered(totalTenantsRegistered != null ? totalTenantsRegistered : 0L)
                    .totalTenantsDonationsReceived(totalTenantsDonationsReceived != null ? totalTenantsDonationsReceived : 0L)
                    .last30DaysDonations(last30DaysDonations != null ? last30DaysDonations : 0L)
                    .averageDonation(averageDonation)
                    .build();
        } catch (DataAccessException e) {
            return DonationReceiptsCardsResponse.builder().build();
        }
    }


    public DonationReceiptsCardsResponse getTenantDashboard(UUID orgId) {
        try {
            Result<Record1<String>> amounts = dsl.select(DSL.field("meta_data->>'amount'").cast(String.class))
                    .from(DONATION_RECEIPTS)
                    .where(DONATION_RECEIPTS.TENANT_ORG_ID.eq(orgId))
                    .fetch();

            Long totalDonations = amounts.stream()
                    .map(Record1::value1)
                    .filter(a -> a != null && !a.trim().isEmpty())
                    .map(this::parseAmount)
                    .filter(a -> a.matches("\\d+")) // Ensure only digits
                    .mapToLong(Long::parseLong)
                    .sum();

            Long uniqueDonors = dsl.select(DSL.countDistinct(DSL.field("meta_data->>'donorEmail'")))
                    .from(DONATION_RECEIPTS)
                    .where(DONATION_RECEIPTS.TENANT_ORG_ID.eq(orgId))
                    .fetchOne(0, Long.class);

            Long last30DaysDonations = Optional.ofNullable(
                    dsl.selectCount()
                            .from(DONATION_RECEIPTS)
                            .where(DONATION_RECEIPTS.TENANT_ORG_ID.eq(orgId)
                                    .and(DONATION_RECEIPTS.RECEIPT_DATE.greaterOrEqual(LocalDate.now().minusDays(30))))
                            .fetchOneInto(Long.class)
            ).orElse(0L);

            Double averageDonation = amounts.stream()
                    .map(Record1::value1)
                    .filter(a -> a != null && !a.trim().isEmpty())
                    .map(this::parseAmount)
                    .filter(a -> a.matches("\\d+")) // Ensure only digits
                    .mapToLong(Long::parseLong)
                    .average()
                    .orElse(0.0);

            return DonationReceiptsCardsResponse.builder()
                    .totalDonations(totalDonations != null ? totalDonations : 0L)
                    .uniqueDonors(uniqueDonors != null ? uniqueDonors : 0L)
                    .last30DaysDonations(last30DaysDonations != null ? last30DaysDonations : 0L)
                    .averageDonation(averageDonation)
                    .build();
        } catch (DataAccessException e) {
            return DonationReceiptsCardsResponse.builder().build();
        } catch (NumberFormatException e) {
            // Log the error if needed
            return DonationReceiptsCardsResponse.builder().build();
        }
    }

    private String parseAmount(String amount) {
        return amount != null ? amount.replaceAll("[^0-9.]", "").trim() : "0";
    }

    public MonthlyDonationResponse getMonthlyDonations(UUID orgId) {
        try {
            // Fetch donation data for the current year
            Result<?> result = dsl.select(
                            DSL.month(DONATION_RECEIPTS.RECEIPT_DATE).as("month"),
                            DSL.field("COALESCE(SUM(CAST(NULLIF(meta_data->>'amount', '') AS BIGINT)), 0)").as("total_amount")
                    )
                    .from(DONATION_RECEIPTS)
                    .where(orgId != null ? DONATION_RECEIPTS.TENANT_ORG_ID.eq(orgId) : DSL.noCondition())
                    .and(DONATION_RECEIPTS.RECEIPT_DATE.isNotNull())
                    .and(DSL.year(DONATION_RECEIPTS.RECEIPT_DATE).eq(DSL.year(DSL.currentDate())))
                    .groupBy(DSL.month(DONATION_RECEIPTS.RECEIPT_DATE))
                    .orderBy(DSL.month(DONATION_RECEIPTS.RECEIPT_DATE))
                    .fetch();


            // Map results to MonthlyDonationData
            Map<Integer, Long> monthlyTotals = new HashMap<>();
            for (Record record : result) {
                Integer monthNum = record.get("month", Integer.class);
                Long amount = record.get("total_amount", Long.class);
                if (monthNum != null && amount != null) {
                    monthlyTotals.put(monthNum, amount);
                }
            }

            // Generate all 12 months for the current year with 0 if no data
            List<MonthlyDonationData> fullYearData = new ArrayList<>();
            for (int month = 1; month <= 12; month++) {
                String monthName = LocalDate.of(LocalDate.now().getYear(), month, 1)
                        .format(DateTimeFormatter.ofPattern("MMM"));
                Long amount = monthlyTotals.getOrDefault(month, 0L);
                fullYearData.add(new MonthlyDonationData(monthName, amount));
            }


            return new MonthlyDonationResponse(fullYearData);
        } catch (DataAccessException e) {

            return new MonthlyDonationResponse(Collections.emptyList());
        } catch (Exception e) {

            return new MonthlyDonationResponse(Collections.emptyList());
        }
    }

    public DonationHeadResponse getDonationsByHead(UUID orgId) {
        try {
            // Step 1: Fetch all active donation heads for the given orgId
            List<String> allHeads = dsl.select(DONATION_HEADS.NAME)
                    .from(DONATION_HEADS)
                    .where(DONATION_HEADS.IS_ACTIVE.eq(true))
                    .and(DONATION_HEADS.ORG_ID.eq(orgId))
                    .fetch(DONATION_HEADS.NAME);



            // Step 2: Define all months
            List<String> months = Arrays.asList("Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec");

            // If no active heads exist for this orgId, return a response with all months and empty amounts
            if (allHeads.isEmpty()) {
                List<DonationHeadData> data = months.stream()
                        .map(month -> new DonationHeadData(month, new HashMap<>()))
                        .collect(Collectors.toList());

                return new DonationHeadResponse(data);
            }

            // Step 3: Fetch donation data for the current year, filtered by orgId, without alias
            Result<?> result = dsl.select(
                            DSL.month(DONATION_RECEIPTS.RECEIPT_DATE).as("month"),
                            DONATION_HEADS.NAME.as("head_name"),
                            DSL.field("COALESCE(SUM(CAST(NULLIF(DONATION_RECEIPTS.meta_data->>'amount', '') AS BIGINT)), 0)").as("total_amount")
                    )
                    .from(DONATION_RECEIPTS)
                    .leftJoin(DONATION_HEADS).on(DONATION_HEADS.ID.eq(DONATION_RECEIPTS.DONATION_HEAD_ID))
                    .where(DONATION_RECEIPTS.TENANT_ORG_ID.eq(orgId))
                    .and(DONATION_RECEIPTS.RECEIPT_DATE.isNotNull())
                    .and(DSL.year(DONATION_RECEIPTS.RECEIPT_DATE).eq(DSL.year(DSL.currentDate())))
                    .and(DONATION_HEADS.IS_ACTIVE.eq(true).or(DONATION_HEADS.ID.isNull()))
                    .groupBy(DSL.month(DONATION_RECEIPTS.RECEIPT_DATE), DONATION_HEADS.NAME)
                    .fetch();

            // Debug: Log the query result

            // Step 4: Map results to monthly data
            Map<String, Map<String, Long>> monthlyData = new HashMap<>();
            for (Record record : result) {
                Integer monthNum = record.get("month", Integer.class);
                if (monthNum == null) continue;
                String month = LocalDate.of(LocalDate.now().getYear(), monthNum, 1)
                        .format(DateTimeFormatter.ofPattern("MMM"));
                String headName = record.get("head_name", String.class);
                Long amount = record.get("total_amount", Long.class);

                if (headName != null) {
                    monthlyData.computeIfAbsent(month, k -> new HashMap<>())
                            .put(headName, amount != null ? amount : 0L);
                }
            }

            // Step 5: Ensure all heads are present for each month
            List<DonationHeadData> data = new ArrayList<>();
            for (String month : months) {
                Map<String, Long> amounts = monthlyData.getOrDefault(month, new HashMap<>());
                Map<String, Long> completeAmounts = new HashMap<>();
                for (String head : allHeads) {
                    completeAmounts.put(head, amounts.getOrDefault(head, 0L));
                }
                data.add(new DonationHeadData(month, completeAmounts));
            }

            // Debug: Log the final response

            return new DonationHeadResponse(data);
        } catch (DataAccessException e) {

            return new DonationHeadResponse(Collections.emptyList());
        } catch (Exception e) {

            return new DonationHeadResponse(Collections.emptyList());
        }
    }


   

    public List<DonationHeadDistribution> getDonationHeadDistribution()
    {
        return statisticsRepository.getDonationHeadDistribution();
    }


    public List<SignupGraphData> getSignupGraphData(GraphType graphType) {
        return statisticsRepository.getSignupGraphData(graphType);
    }


    public List<DonationAmount> getDonationAmount(GraphType graphType) {
        return statisticsRepository.getDonationAmount(graphType);
    }

}

