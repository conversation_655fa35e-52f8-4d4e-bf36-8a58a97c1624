package com.chidhagni.donationreceipt.donationreceipts;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.*;
import com.chidhagni.donationreceipt.donationhead.DonationHeadRepository;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptPaginationRequest;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptRequestDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonationReceiptResponseDTO;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonationResponseForPdfGeneration;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.GetAllDonationReceipts;
import com.chidhagni.donationreceipt.donationreceipts.dto.response.SelfRegisteredDonorDTO;
import com.chidhagni.donationreceipt.donationreceipts.utils.DonationReceiptMapper;
import com.chidhagni.donationreceipt.donationreceipts.utils.DonationReceiptsMapper;
import com.chidhagni.donationreceipt.donationreceipts.utils.NumberToWordsConverter;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.member.SystemCodes;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.MetaDataDTO;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.organisation.dto.OrganisationTenantMetaData;
import com.chidhagni.donationreceipt.services.listvalues.ListValuesRepository;
import com.chidhagni.donationreceipt.wati.WatiDTO;
import com.chidhagni.donationreceipt.wati.WatiService;
import com.chidhagni.utils.CommonOperations;
import com.chidhagni.utils.DateUtils;
import com.chidhagni.utils.PdfGenerateService;
import com.google.api.client.http.ByteArrayContent;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.model.FileList;
import com.google.api.services.drive.model.Permission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.tools.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import javax.mail.MessagingException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.http.HttpResponse;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.Tables.DONATION_RECEIPTS;

@Service
@RequiredArgsConstructor
@Slf4j
public class DonationReceiptService {

    private final DonationReceiptRepository donationReceiptRepository;
    private final DonationReceiptMapper donationReceiptMapper;
    private final IndividualRoleRepository individualRoleRepository;
    private final CommonOperations commonOperations;
    private final DonationHeadRepository donationHeadRepository;
    private final OrganizationRepository organizationRepository;
    private final IndividualRepository individualRepository;
    private final ListValuesRepository listValuesRepository;
    private final PdfGenerateService pdfGenerateService;
    private final SpringTemplateEngine templateEngine;
    private final NotificationManager notificationManager;
    private final DonorsRepository donorsRepository;
    private final DonationReceiptsMapper donationReceiptsMapper;


    private final WatiService watiService;
    @Value("${token}")
    private String token;
    @Value("${donation.receipt.pdf}")
    private String donationReceiptPdf;

    private static final String RECEIPT_TEMPLATE = "receipt-generation-pdf.html";

    public UUID createDonationReceipt(DonationReceiptRequestDTO requestDTO, UUID userId) {
        String generatedReceiptNo = commonOperations.generateSystemCode(SystemCodes.DR);
        DonationReceipts donationReceipt =
                donationReceiptMapper.donationReceiptRequestDtoToDonationReceipt(requestDTO, userId, UUID.randomUUID());
        donationReceipt.setReceiptNo(generatedReceiptNo);
        donationReceiptRepository.saveDonationReceipt(donationReceipt);
        log.info("DonationReceipt created with [id={}]", donationReceipt.getId());
        return donationReceipt.getId();
    }

    public void updateDonationReceipt(UUID id, DonationReceiptRequestDTO requestDTO, UUID userId) {
        DonationReceipts donationReceiptExisting = getDonationReceiptOrThrow(id);
        DonationReceipts updated = donationReceiptMapper.updateDonationReceiptFromDto(requestDTO,
                donationReceiptExisting, userId);
        donationReceiptRepository.updateDonationReceipt(updated);
        log.info("DonationReceipt updated with [id={}]", updated.getId());
    }

    public void activateDonationReceipt(UUID id, UUID userId) {
        DonationReceipts donationReceipt = getDonationReceiptOrThrow(id);
        donationReceipt.setIsActive(true);
        donationReceipt.setUpdatedOn(DateUtils.currentDatetime());
        donationReceipt.setUpdatedBy(userId);
        donationReceiptRepository.updateDonationReceipt(donationReceipt);
        log.info("DonationReceipt activated with [id={}]", id);
    }

    public void deactivateDonationReceipt(UUID id, UUID userId) {
        DonationReceipts donationReceipt = getDonationReceiptOrThrow(id);
        donationReceipt.setIsActive(false);
        donationReceipt.setUpdatedOn(DateUtils.currentDatetime());
        donationReceipt.setUpdatedBy(userId);
        donationReceiptRepository.updateDonationReceipt(donationReceipt);
        log.info("DonationReceipt deactivated with [id={}]", id);
    }

    public GetAllDonationReceipts fetchAllDonationReceipts(DonationReceiptPaginationRequest request, UUID individualId) {
        applyDefaults(request);

        // Build the base search condition
        Condition searchCondition = getSearchingCondition(request);

        // Apply organization filter based on category
        Condition finalCondition = individualRoleRepository.applyOrganizationFilterDonationReceipt(individualId, searchCondition);

        List<Record> result = donationReceiptRepository.fetchDonationReceiptByPagination(request, finalCondition);

        List<DonationReceiptResponseDTO> donationReceipts = result.stream()
                .map(record -> {
                    UUID donationReceiptId = record.get(DONATION_RECEIPTS.ID);
                    UUID donorId = record.get(DONATION_RECEIPTS.DONOR_ID);
                    UUID donorOrgId = record.get(DONATION_RECEIPTS.DONOR_ORG_ID);
                    UUID tenantOrgId = record.get(DONATION_RECEIPTS.TENANT_ORG_ID);


                    DonationReceipts donationReceipt = donationReceiptRepository.getById(donationReceiptId);
                    Donors donors = null;
                    if (donorId != null) {
                        donors = donorsRepository.getByDonorId(donorId);
                    }

                    Individual individual = null;
                    if (donorOrgId != null) {
                        Organisation organisation = organizationRepository.getById(donorOrgId);
                        List<IndividualRole> individualRoleList = individualRoleRepository.getIndividualIdByOrganisationId(organisation.getId());
                        if (!individualRoleList.isEmpty()) {
                            individual = individualRepository.getById(individualRoleList.get(0).getIndividualId());
                        }
                    }

                    Organisation tenantOrganisation = organizationRepository.getById(tenantOrgId);
                    String tenantOrgName = tenantOrganisation != null ? tenantOrganisation.getName() : null;
                    return donationReceiptsMapper.mapToDonationReceiptResponseDto(donationReceipt, donors, individual, tenantOrgName);
                })
                .collect(Collectors.toList());
        Integer count = donationReceiptRepository.fetchDonationReceiptCount(finalCondition);
        return GetAllDonationReceipts.builder()
                .donationReceipts(donationReceipts)
                .rowCount(count)
                .build();
    }

    public DonationReceiptResponseDTO getDonationReceiptById(UUID id) {
        Record record = donationReceiptRepository.fetchOneByIdIncludesOrgNameDonationHead(id);
        if (record == null) {
            throw new IllegalArgumentException("DR GET - No Record Found");
        }
        return mapDbRecordToDonationReceiptResponseDTO(record);
    }

    private void applyDefaults(DonationReceiptPaginationRequest request) {
        if (request.getPage() == null) request.setPage(1);
        if (request.getPageSize() == null) request.setPageSize(10);
    }

    private Condition getSearchingCondition(DonationReceiptPaginationRequest paginationRequest) {
        String searchByReceiptNo = paginationRequest.getReceiptNoFilter();
        UUID orgId = paginationRequest.getOrgIdFilter();
        UUID donationTypeId = paginationRequest.getDonationTypeIdFilter();
        UUID donationHeadId = paginationRequest.getDonationHeadIdFilter();
        String searchByDonorName = paginationRequest.getDonorName();
        UUID searchByDonorPaymentMode = paginationRequest.getPaymentMode();
        String searchKeyword = paginationRequest.getSearchKeyWord();

        if (searchKeyword == null) {
            searchKeyword = "";
        }


        List<Condition> conditions = new ArrayList<>();

        if (searchByReceiptNo != null && !searchByReceiptNo.isEmpty()) {
            conditions.add(DONATION_RECEIPTS.RECEIPT_NO.containsIgnoreCase(searchByReceiptNo));
        }

        if (orgId != null) {
            conditions.add(DONATION_RECEIPTS.TENANT_ORG_ID.eq(orgId));
        }

        if (donationTypeId != null) {
            conditions.add(DONATION_RECEIPTS.DONATION_TYPE_ID.eq(donationTypeId));
        }

        if (donationHeadId != null) {
            conditions.add(DONATION_RECEIPTS.DONATION_HEAD_ID.eq(donationHeadId));
        }

        if(searchByDonorName!=null && !searchByDonorName.isEmpty())
        {

            conditions.add(DSL.condition("DONATION_RECEIPTS.meta_data->>'donorName' ILIKE ?", "%" + searchByDonorName + "%"));
        }
        if (searchByDonorPaymentMode != null) {
            conditions.add(DSL.condition("DONATION_RECEIPTS.meta_data->>'paymentMode' ILIKE ?", "%" + searchByDonorPaymentMode + "%"));
        }

        if (searchKeyword != null && !searchKeyword.isEmpty()) {
            conditions.add(
                    DSL.or(
                            DSL.condition("DONATION_RECEIPTS.meta_data->>'donorName' ILIKE ?", "%" + searchKeyword + "%"),
                            DSL.condition("DONATION_RECEIPTS.meta_data->>'donorEmail' ILIKE ?", "%" + searchKeyword + "%")
                    )
            );
        }

        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
    }

    private DonationReceiptResponseDTO mapDbRecordToDonationReceiptResponseDTO(Record record) {
        return DonationReceiptResponseDTO.builder()
                .id(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.ID))
                .receiptNo(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.RECEIPT_NO))
                .orgId(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.TENANT_ORG_ID))
                .metaData(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.META_DATA))
                .donationTypeId(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.DONATION_TYPE_ID))
                .donationHeadId(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.DONATION_HEAD_ID))
                .receiptDate(String.valueOf(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.RECEIPT_DATE)))
                .createdOn(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.CREATED_ON))
                .createdBy(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.CREATED_BY))
                .updatedOn(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.UPDATED_ON))
                .updatedBy(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.UPDATED_BY))
                .isActive(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationReceipts.DONATION_RECEIPTS.IS_ACTIVE))
                .orgName(record.get("orgName", String.class))
                .donationHead(record.get("donationHead", String.class))
                .build();
    }

    public DonationReceipts getDonationReceiptOrThrow(UUID id) {
        DonationReceipts donationReceipt = donationReceiptRepository.fetchOneById(id);
        if (donationReceipt == null) {
            throw new RuntimeException("Donation Receipt not found for ID: " + id);
        }
        return donationReceipt;
    }




    public byte[] generateReceipt(UUID receiptId) throws IOException, MessagingException {
        DonationResponseForPdfGeneration donations=getByReceiptNo(receiptId);
        byte[] firstPageBytes = generateReceiptPageOne(donations);

        File mergedPdfFile = convertBytesToFile(firstPageBytes);
        notificationManager.sendGeneratedReceipt(mergedPdfFile, donations.getOrgName(), donations.getDonorEmail(), donations.getDonorName()
                , donations.getOrgEmail(), donations.getEmail());
        return firstPageBytes;
    }

    private byte[] generateReceiptPageOne(DonationResponseForPdfGeneration donations) {

        Context context = new Context();
        context.setVariable("donorPhone", donations.getMobileNumber());
        context.setVariable("trustName", donations.getOrgName());
        context.setVariable("email", donations.getOrgEmail());
        context.setVariable("panNo", donations.getPanNo());
        context.setVariable("receiptNo", donations.getReceiptNo());
        context.setVariable("currentDate", LocalDate.now());
        context.setVariable("donorName", donations.getDonorName());
        context.setVariable("donorAddress", donations.getDonorAddress());
        context.setVariable("donorMobileNo", donations.getDonorMobileNumber());
        context.setVariable("donorEmail", donations.getDonorEmail());
        context.setVariable("donorPanNo", donations.getDonorPanNo());
        context.setVariable("donorAadhaar", donations.getDonorAadharNo());
        context.setVariable("amount", donations.getAmount());
        String amountInWords = null;
        String amount = donations.getAmount();
        if (amount != null && !amount.isBlank()) {
            try {
                amountInWords = NumberToWordsConverter.convert(Long.parseLong(amount));
            } catch (NumberFormatException e) {
                log.warn("Invalid amount format: {}", amount, e);
            }
        }
        context.setVariable("amountInWords", amountInWords);
        context.setVariable("donationHeadName", donations.getDonationHeadName());
        context.setVariable("paymentMode", donations.getPaymentMode());
        context.setVariable("regdNo", donations.getRegdNo());
        templateEngine.process(RECEIPT_TEMPLATE, context);
        log.info("Template is rendered for First Page in the pdf");
        return pdfGenerateService.generatePdfFile(RECEIPT_TEMPLATE, context, "receipt.pdf");
    }


    public DonationResponseForPdfGeneration getByReceiptNo(UUID receiptNo) {

        DonationReceiptResponseDTO donationReceipts = getDonationReceiptsById(receiptNo);
        if (donationReceipts == null) {
            throw new IllegalArgumentException("Provided Receipt No Not found");
        }
        DonationHeads donationHeads = donationHeadRepository.fetchOneById(donationReceipts.getDonationHeadId());

        if (donationHeads == null) {
            log.warn("Donation Head Not found");
        }
        Organisation organisation = organizationRepository.getById(donationReceipts.getOrgId());

        List<IndividualRole> individualRoleList = individualRoleRepository.getTenantAdminOfOrganisation(organisation.getId());
        Individual individual = individualRepository.getById(individualRoleList.get(0).getIndividualId());
        DonationResponseForPdfGeneration donationResponseForPdfGeneration = new DonationResponseForPdfGeneration();
        donationResponseForPdfGeneration.setOrgName(organisation.getName());
        donationResponseForPdfGeneration.setMobileNumber(individual.getMobileNumber());
        donationResponseForPdfGeneration.setEmail(individual.getEmail());

        MetaDataDTO metaData = organisation.getMetaData();
        if (metaData instanceof OrganisationTenantMetaData) {
            OrganisationTenantMetaData tenantMetaData = (OrganisationTenantMetaData) metaData;
            donationResponseForPdfGeneration.setOrgEmail(tenantMetaData.getOrgEmail());
            donationResponseForPdfGeneration.setRegdNo(tenantMetaData.getRegistrationNo());
            donationResponseForPdfGeneration.setPanNo(tenantMetaData.getPanNo());
        }

        donationResponseForPdfGeneration.setTenantAdminName(individual.getName());
        donationResponseForPdfGeneration.setReceiptNo(donationReceipts.getReceiptNo());
        donationResponseForPdfGeneration.setReceiptDate(LocalDate.parse(donationReceipts.getReceiptDate()));

        if (donationReceipts.getSelfRegisteredDonorDTO() != null&&donationReceipts.getSelfRegisteredDonorDTO().getId()!=null) {
            donationResponseForPdfGeneration.setDonorName(donationReceipts.getSelfRegisteredDonorDTO().getDonorName());
            donationResponseForPdfGeneration.setDonorAddress(donationReceipts.getSelfRegisteredDonorDTO().getAddress());
            donationResponseForPdfGeneration.setDonorMobileNumber(donationReceipts.getSelfRegisteredDonorDTO().getDonorMobileNumber());
            donationResponseForPdfGeneration.setDonorEmail(donationReceipts.getSelfRegisteredDonorDTO().getDonorEmail());
            donationResponseForPdfGeneration.setDonorPanNo(donationReceipts.getSelfRegisteredDonorDTO().getDonorPanNo());
            donationResponseForPdfGeneration.setDonorAadharNo(donationReceipts.getSelfRegisteredDonorDTO().getDonorAadhaarNo());
        } else if (donationReceipts.getTenantDonorsDTO() != null) {
            donationResponseForPdfGeneration.setDonorName(donationReceipts.getTenantDonorsDTO().getName());
            donationResponseForPdfGeneration.setDonorAddress(donationReceipts.getTenantDonorsDTO().getAddress());
            donationResponseForPdfGeneration.setDonorMobileNumber(donationReceipts.getTenantDonorsDTO().getMobileNumber());
            donationResponseForPdfGeneration.setDonorEmail(donationReceipts.getTenantDonorsDTO().getEmail());
            donationResponseForPdfGeneration.setDonorPanNo(donationReceipts.getTenantDonorsDTO().getPanNo());
        }
        DonationReceiptMetaDataDTO donorMetaData = donationReceipts.getMetaData();
        if (donorMetaData != null) {
            donationResponseForPdfGeneration.setAmount(donorMetaData.getAmount());
            donationResponseForPdfGeneration.setPaymentMode(
                    listValuesRepository.fetchNameById(donorMetaData.getPaymentMode())
            );
        }
        donationResponseForPdfGeneration.setDonationHeadName(donationHeads.getName());
        return donationResponseForPdfGeneration;

    }

    private File convertBytesToFile(byte[] bytes) throws IOException {
        File tempFile = File.createTempFile("report", ".pdf");
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(bytes);
        }
        return tempFile;
    }

    public boolean sendReceiptMessage(String mobileNumber, UUID receiptId) {
        try {
            if (StringUtils.isBlank(mobileNumber)) {
                log.error("Mobile number is blank or null");
                throw new IllegalArgumentException("Mobile number is required");
            }
            if (receiptId == null) {
                log.error("Receipt ID is null");
                throw new IllegalArgumentException("Receipt ID is required");
            }

            DonationReceiptResponseDTO donationReceiptResponseDTO = getDonationReceiptById(receiptId);
            String name = null;
            if(donationReceiptResponseDTO.getSelfRegisteredDonorDTO()!=null)
            {
                name=donationReceiptResponseDTO.getSelfRegisteredDonorDTO().getDonorName();
            }
            else if(donationReceiptResponseDTO.getTenantDonorsDTO()!=null)
            {
                name=donationReceiptResponseDTO.getTenantDonorsDTO().getName();
            }

            String trustName = donationReceiptResponseDTO.getOrgName();

            byte[] pdfBytes = generateReceipt(receiptId);
            if (pdfBytes == null || pdfBytes.length == 0) {
                log.error("Failed to generate PDF for receiptId: {}", receiptId);
                throw new RuntimeException("Failed to generate PDF");
            }

            Drive driveService = GoogleDriveConfig.getDriveService();

            String folderId = getOrCreateFolder(driveService, "Receipts");
            String fileName = "receipt-" + receiptId + ".pdf";
            com.google.api.services.drive.model.File fileMetadata = new com.google.api.services.drive.model.File();
            fileMetadata.setName(fileName);
            fileMetadata.setParents(Collections.singletonList(folderId));
            fileMetadata.setMimeType("application/pdf");

            ByteArrayContent mediaContent = new ByteArrayContent("application/pdf", pdfBytes);
            com.google.api.services.drive.model.File uploadedFile = driveService.files().create(fileMetadata, mediaContent)
                    .setFields("id")
                    .execute();
            log.info("Uploaded PDF to Google Drive with file ID: {}", uploadedFile.getId());

            Permission permission = new Permission()
                    .setType("anyone")
                    .setRole("reader");
            driveService.permissions().create(uploadedFile.getId(), permission).execute();
            log.debug("Set public read permission for file ID: {}", uploadedFile.getId());

            String pdfUrl = "https://drive.google.com/uc?export=download&id=" + uploadedFile.getId();
            //String pdfUrl="https://tmpfiles.org/dl/775603/donation_receipt4.pdf";
            log.info("Generated shareable PDF URL: {}", pdfUrl);

            WatiDTO watiDTO = WatiDTO.builder()
                    .template_name(donationReceiptPdf)
                    .broadcast_name(donationReceiptPdf)
                    .parameters(new ArrayList<>())
                    .build();
            watiDTO.getParameters().add(WatiDTO.Parameters.builder()
                    .name("pdf")
                    .value(pdfUrl)
                    .build());
            watiDTO.getParameters().add(WatiDTO.Parameters.builder()
                    .name("name")
                    .value(StringUtils.defaultIfBlank(name, null))
                    .build());
            watiDTO.getParameters().add(WatiDTO.Parameters.builder()
                    .name("trustName")
                    .value(StringUtils.defaultIfBlank(trustName, null))
                    .build());

            HttpResponse<String> response = watiService.sendTemplateMessage("+91" + "6281569311", watiDTO);
            if (response.statusCode() != 200) {
                log.error("Failed to send template message. Status: {}, Body: {}",
                        response.statusCode(), response.body());
                throw new RuntimeException("Failed to send template message via WATI");
            }
            log.info("Successfully sent receipt to {} with PDF URL: {}", mobileNumber, pdfUrl);
            return true;
        } catch (IOException e) {
            log.error("IO error while processing PDF for {}: {}", mobileNumber, e.getMessage());
            throw new RuntimeException("Error processing PDF: " + e.getMessage());
        } catch (Exception e) {
            log.error("Error sending receipt message to {}: {}", mobileNumber, e.getMessage());
            throw new RuntimeException("Error sending receipt message: " + e.getMessage());
        }
    }


    private String getOrCreateFolder(Drive driveService, String folderName) throws IOException {
        String query = String.format("mimeType='application/vnd.google-apps.folder' and name='%s' and trashed=false", folderName);
        FileList result = driveService.files().list()
                .setQ(query)
                .setSpaces("drive")
                .setFields("files(id, name)")
                .execute();

        List<com.google.api.services.drive.model.File> files = result.getFiles();
        if (files != null && !files.isEmpty()) {
            // Folder exists, return its ID
            log.info("Found existing folder '{}': ID = {}", folderName, files.get(0).getId());
            return files.get(0).getId();
        }

        // Folder doesn't exist, create it
        log.info("Folder '{}' does not exist, creating it...", folderName);
        com.google.api.services.drive.model.File folderMetadata = new com.google.api.services.drive.model.File();
        folderMetadata.setName(folderName);
        folderMetadata.setMimeType("application/vnd.google-apps.folder");

        com.google.api.services.drive.model.File folder = driveService.files().create(folderMetadata)
                .setFields("id")
                .execute();
        log.info("Created folder '{}': ID = {}", folderName, folder.getId());
        return folder.getId();
    }



    public DonationReceiptResponseDTO getDonationReceiptsById(UUID id) {
        DonationReceipts donationReceipts=donationReceiptRepository.getById(id);
        if(donationReceipts==null)
        {
            throw new IllegalArgumentException("Donation Receipt Not found");
        }
        Donors donors=null;
        Individual individual=null;
        if(donationReceipts.getDonorId()!=null)
        {
            donors=donorsRepository.getByDonorId(donationReceipts.getDonorId());
        }
        if(donationReceipts.getDonorOrgId()!=null)
        {
            Organisation organisation=organizationRepository.getById(donationReceipts.getDonorOrgId());
            List<IndividualRole> individualRoleList=individualRoleRepository.getIndividualIdByOrganisationId(organisation.getId());
             individual=individualRepository.getById(individualRoleList.get(0).getIndividualId());
        }
        Organisation tenantOrganisation=organizationRepository.getById(donationReceipts.getTenantOrgId());
        return donationReceiptsMapper.mapToDonationReceiptResponseDto(donationReceipts,donors,individual,tenantOrganisation.getName());
    }
}