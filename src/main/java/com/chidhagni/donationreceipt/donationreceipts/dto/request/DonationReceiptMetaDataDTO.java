package com.chidhagni.donationreceipt.donationreceipts.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationReceiptMetaDataDTO {
    private String amount;
    private UUID paymentMode;
    private String paymentDetails;
    private String reference;
    private String additionalNotes;
    private UUID paymentType;

}
