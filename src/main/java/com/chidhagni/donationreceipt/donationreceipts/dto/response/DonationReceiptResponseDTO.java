package com.chidhagni.donationreceipt.donationreceipts.dto.response;

import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationReceiptResponseDTO {
    private UUID id;
    private String receiptNo;
    private UUID orgId;
    private String orgName;
    private DonationReceiptMetaDataDTO metaData;
    private UUID donationTypeId;
    private String donationHead;
    private UUID donationHeadId;
    private String receiptDate;
    private LocalDateTime createdOn;
    private UUID createdBy;
    private LocalDateTime updatedOn;
    private UUID updatedBy;
    private Boolean isActive;

    private SelfRegisteredDonorDTO selfRegisteredDonorDTO;
    private TenantDonorsDTO tenantDonorsDTO;
}