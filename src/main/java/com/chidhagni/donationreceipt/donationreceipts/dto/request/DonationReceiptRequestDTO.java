package com.chidhagni.donationreceipt.donationreceipts.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DonationReceiptRequestDTO {
    @NotNull(message = "orgId is mandatory")
    private UUID orgId;
    private DonationReceiptMetaDataDTO metaData;
    @NotNull(message = "donationTypeId is mandatory")
    private UUID donationTypeId;
    @NotNull(message = "donationHeadId is mandatory")
    private UUID donationHeadId;
    private String receiptDate;
    private UUID donorId;
    private UUID donorOrgId;

}