package com.chidhagni.donationreceipt.donationreceipts;

import com.chidhagni.donationreceipt.db.jooq.tables.daos.DonationReceiptsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationReceipts;
import com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptPaginationRequest;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static com.chidhagni.donationreceipt.db.jooq.Tables.*;

@Repository
@RequiredArgsConstructor
public class DonationReceiptRepository {

    private final DonationReceiptsDao donationReceiptDao;
    private final DSLContext dslContext;

    public void saveDonationReceipt(DonationReceipts donationReceipt) {
        try {
            donationReceiptDao.insert(donationReceipt);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while saving donation receipt", ex);
        }
    }

    public void updateDonationReceipt(DonationReceipts donationReceipt) {
        try {
            donationReceiptDao.update(donationReceipt);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while saving donation receipt", ex);
        }
    }

    public DonationReceipts fetchOneById(UUID id) {
        try {
            return donationReceiptDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while fetching donation receipt", ex);
        }
    }


    public List<Record> fetchDonationReceiptByPagination(DonationReceiptPaginationRequest request, Condition condition) {
        try {
            Integer offset = (request.getPage() - 1) * request.getPageSize();
            List<Field<?>> fields = new ArrayList<>();
            fields.addAll(Arrays.asList(DONATION_RECEIPTS.fields()));
            fields.add(DONATION_HEADS.NAME.as("donationHead"));
            fields.add(ORGANISATION.NAME.as("orgName"));

            return dslContext.select(fields)
                    .from(DONATION_RECEIPTS)
                    .leftJoin(DONATION_HEADS).on(DONATION_RECEIPTS.DONATION_HEAD_ID.eq(DONATION_HEADS.ID))
                    .leftJoin(ORGANISATION).on(DONATION_RECEIPTS.TENANT_ORG_ID.eq(ORGANISATION.ID))
                    .where(condition)
                    .orderBy(DONATION_RECEIPTS.CREATED_ON.desc())
                    .offset(offset)
                    .limit(request.getPageSize())
                    .fetch();
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while fetching donation receipts", ex);
        }
    }

    public Integer fetchDonationReceiptCount(Condition condition) {
        try {
            return dslContext.selectCount()
                    .from(DONATION_RECEIPTS)
                    .where(condition)
                    .fetchOneInto(Integer.class);
        } catch (Exception ex) {
            throw new RuntimeException("Exception occurred while fetching donation receipt count", ex);
        }
    }


    public Record fetchOneByIdIncludesOrgNameDonationHead(UUID id) {
        return dslContext.select(
                        Stream.concat(
                                Arrays.stream(DONATION_RECEIPTS.fields()),
                                Stream.of(
                                        DONATION_HEADS.NAME.as("donationHead"),
                                        ORGANISATION.NAME.as("orgName")
                                )
                        ).toArray(Field[]::new)
                )
                .from(DONATION_RECEIPTS)
                .leftJoin(DONATION_HEADS)
                .on(DONATION_RECEIPTS.DONATION_HEAD_ID.eq(DONATION_HEADS.ID))
                .leftJoin(ORGANISATION)
                .on(DONATION_RECEIPTS.TENANT_ORG_ID.eq(ORGANISATION.ID))
                .where(DONATION_RECEIPTS.ID.eq(id))
                .fetchOne();
    }

    public List<DonationReceipts> findAllByCreatedOnBetween(LocalDateTime startOfDay, LocalDateTime endOfDay) {
        return donationReceiptDao.ctx().select()
                .from(DONATION_RECEIPTS)
                .where(DONATION_RECEIPTS.CREATED_ON.between(startOfDay,endOfDay))
                .fetchInto(DonationReceipts.class);
    }

    public DonationReceipts getById(UUID id) {
        return donationReceiptDao.ctx().select()
                .from(DONATION_RECEIPTS)
                .where(DONATION_RECEIPTS.ID.eq(id))
                .fetchOneInto(DonationReceipts.class);
    }
}