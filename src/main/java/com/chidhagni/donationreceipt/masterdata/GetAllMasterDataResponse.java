package com.chidhagni.donationreceipt.masterdata;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GetAllMasterDataResponse {
    private List<MasterDataResponseDTO> masterDataResponse;

    private PaginationRequestMasterData paginationRequest;

    private Integer rowCount;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MasterDataResponseDTO {
        private UUID id;

        private String name;

        private MasterDataType masterDataType;

        private Boolean isActive;

        private String createdOn;
        private String updatedOn;
        private String createdBy;
        private String updatedBy;
    }
}
