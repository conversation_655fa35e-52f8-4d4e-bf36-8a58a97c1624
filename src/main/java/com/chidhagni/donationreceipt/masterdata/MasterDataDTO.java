package com.chidhagni.donationreceipt.masterdata;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MasterDataDTO {

    private UUID id;

    private String name;

    private MasterDataType masterDataType;

    private Boolean isActive;

    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;
}
