package com.chidhagni.donationreceipt.masterdata;

//import com.chidhagni.houzer.db.jooq.tables.daos.MasterDataDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.MasterData;
import org.springframework.stereotype.Service;
//
//import static com.chidhagni.houzer.db.jooq.Tables.MASTER_DATA;
//import static com.chidhagni.houzer.db.jooq.tables.Member.MEMBER;

@Service
public class MasterDataService {

//    private final MasterDataMapper mapper;
//
//    private final MasterDataDao masterDataDao;
//
//    private final DSLContext dslContext;
//
//
//
//    public MasterDataService(MasterDataMapper mapper, MasterDataDao masterDataDao, DSLContext dslContext) {
//        this.mapper = mapper;
//        this.masterDataDao = masterDataDao;
//        this.dslContext = dslContext;
//    }
//
//    public MasterDataDTO create(MasterDataDTO masterDataDTO, UserPrincipal userPrincipal) {
//
//
//        MasterData mappedCreate = mapper.mapCreate(masterDataDTO, userPrincipal);
//        masterDataDao.insert(mappedCreate);
//
//        MasterDataDTO masterDataMapped = mapper.map(mappedCreate);
//
//        return masterDataMapped;
//    }
//
//    @Transactional
//    public MasterDataDTO updateMasterData(MasterDataDTO masterDataDTO, UserPrincipal userPrincipal) {
//
//        MasterData masterDataEntity = masterDataDao.fetchOneById(masterDataDTO.getId());
//
//        if (masterDataEntity == null) {
//            throw new IllegalArgumentException("Master data Doesn't Exist");
//        }
//
//        MasterData masterData = mapper.mapUpdate(masterDataDTO,userPrincipal,masterDataEntity);
//
//        masterDataDao.update(masterData);
//
//        return mapper.map(masterData);
//    }
//
//    @Transactional
//    public void deleteMasterData(UUID id, UserPrincipal userPrincipal) {
//        MasterData masterData = masterDataDao.fetchOneById(id);
//        if (masterData == null) {
//            throw new IllegalArgumentException("Master data Doesn't Exist");
//        }
//        masterData.setIsActive(false);
//        masterData.setUpdatedBy(userPrincipal.getId());
//        masterData.setUpdatedOn(DateUtils.currentTimeIST());
//
//        masterDataDao.update(masterData);
//    }
//
//    @Transactional
//    public void activateMasterData(UUID id, UserPrincipal userPrincipal) {
//        MasterData masterData = masterDataDao.fetchOneById(id);
//        if (masterData == null) {
//            throw new IllegalArgumentException("Master data Doesn't Exist");
//        }
//        masterData.setIsActive(true);
//        masterData.setUpdatedBy(userPrincipal.getId());
//        masterData.setUpdatedOn(DateUtils.currentTimeIST());
//
//        masterDataDao.update(masterData);
//    }
//
//    @Transactional
//    public MasterDataDTO getMasterDataById(UUID id) throws JsonProcessingException {
//        MasterData masterData = masterDataDao.fetchOneById(id);
//
//        if (masterData == null) {
//            throw new IllegalArgumentException("Employee Doesn't Exist");
//        }
//
//        return mapper.map(masterData);
//    }
//
//    public GetAllMasterDataResponse getAll(PaginationRequestMasterData paginationRequestMasterData) {
//        if (paginationRequestMasterData.getSearchKeyword() == null) {
//            paginationRequestMasterData.setSearchKeyword("");
//        }
//
//        if (paginationRequestMasterData.getPage() == null) {
//            paginationRequestMasterData.setPage(1);
//        }
//
//        if (paginationRequestMasterData.getPageSize() == null) {
//            paginationRequestMasterData.setPageSize(100);
//        }
//
//        int pageNo = (paginationRequestMasterData.getPage() - 1) * paginationRequestMasterData.getPageSize();
//        String dateQuery = "TO_CHAR({0} AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'YYYY-MM-DD HH24:MI:SS')";
//
//        List<GetAllMasterDataResponse.MasterDataResponseDTO> fetchedMasterDataList = dslContext.select(
//                        MASTER_DATA.ID, MASTER_DATA.NAME,
//                        MASTER_DATA.IS_ACTIVE, MASTER_DATA.TYPE.as("masterDataType"),
//
//                        DSL.field(dateQuery, String.class, MASTER_DATA.CREATED_ON).as("createdOn"),
//                        DSL.field(dateQuery, String.class, MASTER_DATA.UPDATED_ON).as("updatedOn"),
//                MEMBER.as("m2").LOGIN_EMAIL.as("createdBy"),
//                MEMBER.as("m3").LOGIN_EMAIL.as("updatedBy"))
//
//                .from(MASTER_DATA)
//                .leftOuterJoin(MEMBER.as("m2")).on(MASTER_DATA.CREATED_BY.eq(MEMBER.as("m2").ID))
//                .leftOuterJoin(MEMBER.as("m3")).on(MASTER_DATA.UPDATED_BY.eq(MEMBER.as("m3").ID))
//                .where(searchConditions(paginationRequestMasterData))
//                .orderBy(MASTER_DATA.CREATED_ON.desc())
//                .limit(paginationRequestMasterData.getPageSize())
//                .offset(pageNo)
//                .fetchInto(GetAllMasterDataResponse.MasterDataResponseDTO.class);
//
//        int count = dslContext.selectCount()
//                .from(MASTER_DATA)
//                .where(searchConditions(paginationRequestMasterData))
//                .fetchOne(0, int.class);
//
//        return GetAllMasterDataResponse.builder()
//                .masterDataResponse(fetchedMasterDataList)
//                .paginationRequest(paginationRequestMasterData)
//                .rowCount(count)
//                .build();
//    }
//
//    private Condition searchConditions(PaginationRequestMasterData paginationRequestMasterData) {
//
//        Condition condition = MASTER_DATA.NAME.containsIgnoreCase(paginationRequestMasterData.getSearchKeyword());
//
//        if (paginationRequestMasterData.getMasterDataType() != null) {
//            condition = condition.and(MASTER_DATA.TYPE.equalIgnoreCase(paginationRequestMasterData.getMasterDataType().toString()));
//        }
//
//        return condition;
//
//    }
}