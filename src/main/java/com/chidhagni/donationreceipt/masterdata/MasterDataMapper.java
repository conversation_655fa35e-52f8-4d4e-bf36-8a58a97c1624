package com.chidhagni.donationreceipt.masterdata;

//import com.chidhagni.houzer.db.jooq.tables.pojos.MasterData;
import org.springframework.stereotype.Component;

@Component
public class MasterDataMapper {
//    public MasterData mapCreate(MasterDataDTO masterDataDTO, UserPrincipal userPrincipal) {
//
//        MasterData masterData=new MasterData();
//
//        masterData.setId(UUID.randomUUID());
//        masterData.setName(masterDataDTO.getName());
//        masterData.setType(String.valueOf(masterDataDTO.getMasterDataType()));
//        masterData.setIsActive(true);
//        masterData.setCreatedOn(DateUtils.currentTimeIST());
//        masterData.setUpdatedOn(DateUtils.currentTimeIST());
//        masterData.setCreatedBy(userPrincipal.getId());
//        masterData.setUpdatedBy(userPrincipal.getId());
//        return masterData;
//    }
//
//
//    public MasterData mapUpdate(MasterDataDTO masterDataDTO, UserPrincipal userPrincipal, MasterData masterDataEntity) {
//
//        MasterData masterData=new MasterData();
//
//        masterData.setId(masterDataDTO.getId());
//        masterData.setName(masterDataDTO.getName());
//        masterData.setType(String.valueOf(masterDataDTO.getMasterDataType()));
//        masterData.setIsActive(masterDataDTO.getIsActive());
//        masterData.setCreatedOn(masterDataEntity.getCreatedOn());
//        masterData.setUpdatedOn(DateUtils.currentTimeIST());
//        masterData.setCreatedBy(masterDataEntity.getCreatedBy());
//        masterData.setUpdatedBy(userPrincipal.getId());
//        return masterData;
//    }
//    public MasterDataDTO map(MasterData mappedCreate) {
//
//        return MasterDataDTO.builder()
//                .id(mappedCreate.getId())
//                .masterDataType(MasterDataType.valueOf(mappedCreate.getType()))
//                .name(mappedCreate.getName())
//                .isActive(mappedCreate.getIsActive())
//                .createdOn(mappedCreate.getCreatedOn())
//                .updatedOn(mappedCreate.getUpdatedOn())
//                .createdBy(mappedCreate.getCreatedBy())
//                .updatedBy(mappedCreate.getUpdatedBy())
//                .build();
//    }
}