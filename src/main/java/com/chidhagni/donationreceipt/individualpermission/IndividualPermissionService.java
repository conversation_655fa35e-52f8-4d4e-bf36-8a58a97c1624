package com.chidhagni.donationreceipt.individualpermission;

//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.individualpermission.util.IIndividualPermissionMapper;
import com.chidhagni.donationreceipt.roles.RolesService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class IndividualPermissionService {
    private final RolesService rolesService;
    private final IIndividualPermissionMapper interfaceIndividualPermissionMapper;
    @Value("${service.provider.roles.id}")
    private UUID serviceProviderId;
    @Value("${society.roles.id}")
    private UUID societyId;
    @Value("${employee.roles.id}")
    private UUID employeeRoleId;
    private final IndividualPermissionRepository individualPermissionRepository;

//    public IndividualPermission createIndividualPermission(UUID individualId, UUID organisationId
//            , DefaultOrganisationCategoryEnums role) throws JsonProcessingException {
//        UUID roleId = role.equals(DefaultOrganisationCategoryEnums.SERVICE_PROVIDER)
//                ? serviceProviderId :
//                DefaultOrganisationCategoryEnums.EMPLOYEE.equals(role) ? employeeRoleId : societyId;
//        RolesPermissionsResponse roleById = rolesService.getRoleById(roleId);
//        return interfaceIndividualPermissionMapper.createIndividualPermission(individualId, organisationId
//                , roleById.getPermissions());
//    }
//
//    public IndividualPermission createIndividualPermissionByGivenRoleId(UUID individualId, UUID organisationId
//            , UUID roleId){
//        RolesPermissionsResponse roleById = rolesService.getRoleById(roleId);
//        return interfaceIndividualPermissionMapper.createIndividualPermission(individualId, organisationId
//                , roleById.getPermissions());
//    }
//
//    public void updateIndividualPermission(UUID individualId, UUID roleId, UserPrincipal userPrincipal){
//        List<IndividualPermission> individualPermissionsList = individualPermissionRepository.getIndividualPermissionsByIndividualId(individualId);
//        if(individualPermissionsList == null || CollectionUtils.isEmpty(individualPermissionsList)){
//            throw new IllegalArgumentException(
//                    String.format(
//                            "As there is no individual permissions present for the given individual id :: %s"
//                            , individualId));
//        }
//        IndividualPermission individualPermission = individualPermissionsList.get(0);
//        RolesPermissionsResponse roleById = rolesService.getRoleById(roleId);
//        interfaceIndividualPermissionMapper.updateIndividualPermission(individualPermission, roleById.getPermissions(), userPrincipal.getId());
//        individualPermissionRepository.upsertIndividualPermissions(List.of(individualPermission));
//    }

}
