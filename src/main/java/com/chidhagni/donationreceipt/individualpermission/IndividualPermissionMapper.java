package com.chidhagni.donationreceipt.individualpermission;


//import com.chidhagni.houzer.datamigration.common.BatchJobProperties;
//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualPermissionDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class IndividualPermissionMapper {


//    private final BatchJobProperties batchJobProperties;
//    private final IndividualPermissionDao individualPermissionDao;
//
//    public List<IndividualPermission> mapToIndividualPermissions(List<IndividualPermissionDTO> dtoList) {
//        return dtoList.stream()
//                .map(this::mapIndividualPermissionDTO)
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList());
//    }
//
//    private IndividualPermission mapIndividualPermissionDTO(IndividualPermissionDTO individualPermissionDTO) {
//        UUID individualId = individualPermissionDTO.getIndividualId();
//        List<RoleNode> permission = individualPermissionDTO.getPermission();
//        UUID orgId = individualPermissionDTO.getOrgId();
//
//        if (individualId == null || permission == null) {
//            log.warn("Skipping record due to missing data: individual_id={}", individualId);
//            return null;
//        }
//
//        List<IndividualPermission> existingIndividualId = individualPermissionDao.fetchByIndividualId(individualId);
//
//        // Create IndividualPermission instance
//        IndividualPermission individualPermission = new IndividualPermission();
//        if (existingIndividualId.isEmpty()) {
//            individualPermission.setId(UUID.randomUUID());
//        } else {
//            individualPermission.setId(existingIndividualId.get(0).getId());
//        }
//        individualPermission.setIndividualId(individualId);
//        individualPermission.setPermissions(permission);
//        individualPermission.setCreatedOn(LocalDateTime.now());
//        individualPermission.setUpdatedOn(LocalDateTime.now());
//        individualPermission.setCreatedBy(UUID.randomUUID());
//        individualPermission.setUpdatedBy(UUID.randomUUID());
//        individualPermission.setOrgId(orgId);
//
//        log.info("Mapped IndividualPermission");
//        return individualPermission;
//    }






}
