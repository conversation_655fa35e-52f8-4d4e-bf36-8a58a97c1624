package com.chidhagni.donationreceipt.individualpermission;

//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualPermissionDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPermissionDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

@Slf4j
@Repository
@RequiredArgsConstructor
public class IndividualPermissionRepository {
   private final IndividualPermissionDao individualPermissionDao;
//
//    /**
//     * Get individual permission by individual id
//     * <ul>Logic:<ul/>
//     * <li>Fetch list of individual permission records by individual id using jooq in build dao's</li>
//     * @param individualId
//     * @return list of individual permission objects
//     */
    public List<IndividualPermission> getIndividualPermissionByIndividualId(UUID individualId) {
        try {
            return individualPermissionDao.fetchByIndividualId(individualId);
        } catch (Exception ex) {
            throw new InternalServerError("Exception while retrieving the individual permissions by individual id");
        }
    }
//
//    /**
//     * insert or update individual permission objects
//     * <ul>Logic:<ul/>
//     * <li>Fetch list of individual permission records by individual id using jooq in build dao's</li>
//     * @param individualPermissions DTO to update in the database if present with primary id or insert new record.k
//     */
    public void upsertIndividualPermissions(List<IndividualPermission> individualPermissions) {
        try {
            individualPermissionDao.merge(individualPermissions);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while upsert individual permissions", ex);
        }
    }
//
    public void insertIndividualPermission(IndividualPermission individualPermission){
        try{
            individualPermissionDao.insert(individualPermission);
            log.info("successfully insert the individual permission with primary id :: {}", individualPermission.getId());
        }catch(Exception ex){
           throw new InternalServerError("Exception while inserting the individual permission", ex);
        }
    }
//
//    public List<IndividualPermission> getIndividualPermissionsByIndividualId(UUID individualId){
//        try{
//            return individualPermissionDao.fetchByIndividualId(individualId);
//        }catch (Exception ex){
//            throw new InternalServerError(String.format(
//                    "Exception while fetching the individual permission by individual id :: %s", individualId), ex);
//        }
//    }

}
