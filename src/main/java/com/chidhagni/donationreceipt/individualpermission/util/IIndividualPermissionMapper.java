package com.chidhagni.donationreceipt.individualpermission.util;

//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualPermission;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface IIndividualPermissionMapper {
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "individualId", source = "individualId")
//    @Mapping(target = "permissions", source = "permissions")
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "createdBy", source = "individualId")
//    @Mapping(target = "updatedBy", ignore = true)
//    @Mapping(target =  "updatedOn", ignore = true)
//    @Mapping(target =  "remarks", ignore = true)
//    @Mapping(target = "orgId", source = "organisationId")
//    IndividualPermission createIndividualPermission(UUID individualId, UUID organisationId, List<RoleNode> permissions);
//
//    @Mapping(target = "permissions", source = "permissions")
//    @Mapping(target = "updatedBy", source = "loggedInUserId")
//    @Mapping(target =  "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target =  "id", ignore = true)
//    @Mapping(target =  "individualId", ignore = true)
//    @Mapping(target =  "createdOn", ignore = true)
//    @Mapping(target =  "createdBy", ignore = true)
//    @Mapping(target =  "remarks", ignore = true)
//    @Mapping(target =  "orgId", ignore = true)
//    void updateIndividualPermission(@MappingTarget IndividualPermission individualPermission
//            , List<RoleNode> permissions, UUID loggedInUserId);
}
