package com.chidhagni.donationreceipt.documentSubCategories;

//import com.chidhagni.houzer.db.jooq.tables.daos.DocumentSubCategoriesDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.DocumentSubCategories;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

//import static com.chidhagni.houzer.db.jooq.Tables.DOCUMENT_CATEGORIES;
//import static com.chidhagni.houzer.db.jooq.Tables.DOCUMENT_SUB_CATEGORIES;
//import static com.chidhagni.houzer.db.jooq.tables.Member.MEMBER;

@Service
@Slf4j
@RequiredArgsConstructor
public class DocumentSubCategoryService {
   // private final DSLContext dslContext;
   // private final DocumentSubCategoryDTOToDocumentSubCategoryMapper mapper;
//    private final DocumentSubCategoriesDao documentSubCategoriesDao;
//

//
//    public DocumentSubCategoryDTO create(DocumentSubCategoryDTO documentSubCategoryDTO, UserPrincipal userPrincipal) {
//
//        List<DocumentSubCategories> roleTypesList = dslContext.select()
//                .from(DOCUMENT_SUB_CATEGORIES)
//                .where(
//                        DOCUMENT_SUB_CATEGORIES.DOCUMENT_CATEGORY.eq(documentSubCategoryDTO.getDocumentCategory()).and(
//                                DOCUMENT_SUB_CATEGORIES.DOCUMENT_SUB_CATEGORY.containsIgnoreCase(documentSubCategoryDTO.getDocumentSubCategory()))
//                )
//
//                .fetchInto(DocumentSubCategories.class);
//
//        if (!roleTypesList.isEmpty()) {
//            throw new IllegalArgumentException("Document Sub Category Already Exists");
//        }
//
//        DocumentSubCategories documentSubCategories = mapper.map(documentSubCategoryDTO, userPrincipal);
//        documentSubCategoriesDao.insert(documentSubCategories);
//
//        return mapper.map(documentSubCategories);
//    }
//
//
//    public DocumentSubCategoryDTO getDocumentSubCategoryById(UUID subCategoryId) {
//        DocumentSubCategories documentSubCategories = documentSubCategoriesDao.fetchOneById(subCategoryId);
//
//        if (documentSubCategories == null) {
//            throw new IllegalArgumentException("Invalid document Category");
//        }
//
//        return mapper.map(documentSubCategories);
//    }
//
//    public void deleteDocumentSubCategoryById(UUID documentSubCategoryId, UserPrincipal userPrincipal) {
//        DocumentSubCategories documentSubCategories = documentSubCategoriesDao.fetchOneById(documentSubCategoryId);
//
//        if (documentSubCategories == null) {
//            throw new IllegalArgumentException("Invalid Document SubCategory");
//        }
//
//        mapper.mapForDelete(documentSubCategories, userPrincipal);
//
//        documentSubCategoriesDao.update(documentSubCategories);
//    }
//
//
//    public List<DocumentSubCategoryResponseDTO> getAll() {
//
//        return dslContext.select(
//                        DOCUMENT_SUB_CATEGORIES.ID,
//                        DOCUMENT_CATEGORIES.as("d1").DOCUMENT_CATEGORY.as("documentCategory"),
//                        DOCUMENT_SUB_CATEGORIES.DOCUMENT_SUB_CATEGORY,
//                        DOCUMENT_SUB_CATEGORIES.IS_ACTIVE,
//                        MEMBER.as("m2").LOGIN_EMAIL.as("createdBy"),
//                        MEMBER.as("m3").LOGIN_EMAIL.as("updatedBy"),
//                        DOCUMENT_SUB_CATEGORIES.CREATED_ON,
//                        DOCUMENT_SUB_CATEGORIES.UPDATED_ON
//                )
//                .from(DOCUMENT_SUB_CATEGORIES)
//                .leftOuterJoin(DOCUMENT_CATEGORIES.as("d1")).on(DOCUMENT_SUB_CATEGORIES.DOCUMENT_CATEGORY.eq(DOCUMENT_CATEGORIES.as("d1").ID))
//                .leftOuterJoin(MEMBER.as("m2")).on(DOCUMENT_SUB_CATEGORIES.CREATED_BY.eq(MEMBER.as("m2").ID))
//                .leftOuterJoin(MEMBER.as("m3")).on(DOCUMENT_SUB_CATEGORIES.UPDATED_BY.eq(MEMBER.as("m3").ID))
//                .fetchInto(DocumentSubCategoryResponseDTO.class);
//
//    }

}
