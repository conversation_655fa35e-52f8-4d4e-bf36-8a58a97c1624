package com.chidhagni.donationreceipt.documentSubCategories;

//import com.chidhagni.houzer.db.jooq.tables.pojos.DocumentSubCategories;
import org.springframework.stereotype.Component;

@Component
public class DocumentSubCategoryDTOToDocumentSubCategoryMapper {

//    public DocumentSubCategories map(DocumentSubCategoryDTO documentSubCategoryDTO, UserPrincipal userPrincipal) {
//
//        DocumentSubCategories documentSubCategories = new DocumentSubCategories();
//
//        if (documentSubCategoryDTO.getId() == null) {
//            documentSubCategories.setId(UUID.randomUUID());
//            documentSubCategories.setIsActive(true);
//
//            documentSubCategories.setCreatedBy(userPrincipal.getId());
//            documentSubCategories.setUpdatedBy(userPrincipal.getId());
//            documentSubCategories.setCreatedOn(DateUtils.currentTimeIST());
//            documentSubCategories.setUpdatedOn(DateUtils.currentTimeIST());
//        } else {
//            documentSubCategories.setId(documentSubCategoryDTO.getId());
//            documentSubCategories.setIsActive(documentSubCategoryDTO.getIsActive());
//
//            documentSubCategories.setCreatedBy(documentSubCategoryDTO.getCreatedBy());
//            documentSubCategories.setUpdatedBy(documentSubCategoryDTO.getUpdatedBy());
//            documentSubCategories.setCreatedOn(documentSubCategoryDTO.getCreatedOn());
//            documentSubCategories.setUpdatedOn(documentSubCategoryDTO.getUpdatedOn());
//        }
//
//        documentSubCategories.setDocumentCategory(documentSubCategoryDTO.getDocumentCategory());
//        documentSubCategories.setDocumentSubCategory(documentSubCategoryDTO.getDocumentSubCategory());
//
//        return documentSubCategories;
//    }
//
//    public DocumentSubCategoryDTO map(DocumentSubCategories documentSubCategories) {
//        return DocumentSubCategoryDTO.builder()
//                .id(documentSubCategories.getId())
//                .documentCategory(documentSubCategories.getDocumentCategory())
//                .documentSubCategory(documentSubCategories.getDocumentSubCategory())
//                .isActive(documentSubCategories.getIsActive())
//                .createdBy(documentSubCategories.getCreatedBy())
//                .updatedBy(documentSubCategories.getUpdatedBy())
//                .createdOn(documentSubCategories.getCreatedOn())
//                .updatedOn(documentSubCategories.getUpdatedOn())
//                .build();
//
//    }
//
//    public void mapForDelete(DocumentSubCategories documentSubCategories, UserPrincipal userPrincipal) {
//        documentSubCategories.setIsActive(false);
//        documentSubCategories.setUpdatedOn(DateUtils.currentTimeIST());
//        documentSubCategories.setCreatedBy(userPrincipal.getId());
//    }


}
