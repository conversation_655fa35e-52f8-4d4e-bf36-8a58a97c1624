package com.chidhagni.donationreceipt.documentSubCategories;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/documentSubCategories")
public class DocumentSubCategoryController {

//    private final DocumentSubCategoryService documentSubCategoryService;
//
//    public DocumentSubCategoryController(DocumentSubCategoryService documentSubCategoryService) {
//        this.documentSubCategoryService = documentSubCategoryService;
//    }

//    @PostMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
//    public ResponseEntity<DocumentSubCategoryDTO> create(@RequestBody @Valid DocumentSubCategoryDTO body, @CurrentUser UserPrincipal userPrincipal) throws Exception {
//        DocumentSubCategoryDTO createdDocumentSubCategory = documentSubCategoryService.create(body, userPrincipal);
//
//        return ResponseEntity.created(
//                        UriComponentsBuilder.
//                                fromPath("/documentSubCategories").
//                                buildAndExpand()
//                                .toUri())
//                .body(createdDocumentSubCategory);
//    }
//
//    @GetMapping(value = {"/{subCategoryId}"}, produces = APPLICATION_JSON_VALUE)
//    public ResponseEntity<DocumentSubCategoryDTO> getDocumentSubCategory(@PathVariable UUID subCategoryId) throws JsonProcessingException {
//        return ResponseEntity.ok().body(documentSubCategoryService.getDocumentSubCategoryById(subCategoryId));
//
//    }
//
//    @DeleteMapping(value = {"/{subCategoryId}"}, produces = APPLICATION_JSON_VALUE)
//    public void deleteDocumentSubCategory(@PathVariable UUID subCategoryId, @CurrentUser UserPrincipal userPrincipal) throws JsonProcessingException {
//        documentSubCategoryService.deleteDocumentSubCategoryById(subCategoryId, userPrincipal);
//    }
//
//    @PostMapping("/all")
//    public ResponseEntity<List<DocumentSubCategoryResponseDTO>> getAll() {
//
//        return ResponseEntity.ok().body(documentSubCategoryService.getAll());
//    }
}
