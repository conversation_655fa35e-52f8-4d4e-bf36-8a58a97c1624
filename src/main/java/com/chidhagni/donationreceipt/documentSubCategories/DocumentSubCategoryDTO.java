package com.chidhagni.donationreceipt.documentSubCategories;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
public class DocumentSubCategoryDTO {
    private UUID id;

    private UUID documentCategory;

    private String documentSubCategory;

    private Boolean isActive;

    private UUID createdBy;

    private UUID updatedBy;

    private LocalDateTime createdOn;

    private LocalDateTime updatedOn;
}
