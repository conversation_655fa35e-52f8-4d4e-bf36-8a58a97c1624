package com.chidhagni.donationreceipt.documentSubCategories;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
public class DocumentSubCategoryResponseDTO {
    private UUID id;

    private String documentCategory;

    private String documentSubCategory;

    private Boolean isActive;

    private String createdBy;

    private String updatedBy;

    private LocalDateTime createdOn;

    private LocalDateTime updatedOn;
}
