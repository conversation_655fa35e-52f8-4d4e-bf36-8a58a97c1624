package com.chidhagni.donationreceipt.donationhead;

import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadPaginationRequest;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadRequestDTO;
import com.chidhagni.donationreceipt.donationhead.dto.request.GetAllDonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadCreateResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsByOrgIdResponseDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsDropDown;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/donation-heads")
@RequiredArgsConstructor
@Slf4j
public class DonationHeadController {

    private final DonationHeadService donationHeadService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseEntity<DonationHeadCreateResponseDTO> createDonationHead(
            @Valid @RequestBody DonationHeadRequestDTO requestDTO,
            @CurrentUser UserPrincipal userPrincipal) {
        UUID id = donationHeadService.createDonationHead(requestDTO, userPrincipal.getId());
        return ResponseEntity.ok(DonationHeadCreateResponseDTO.builder()
                .id(id)
                .build());
    }

    @PutMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> updateDonationHead(
            @PathVariable UUID id,
            @Valid @RequestBody DonationHeadRequestDTO requestDTO,
            @CurrentUser UserPrincipal userPrincipal) {
        donationHeadService.updateDonationHead(id, requestDTO, userPrincipal.getId());
        return ResponseEntity.ok().build();
    }

    @PatchMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> activateDonationHead(
            @PathVariable UUID id,
            @CurrentUser UserPrincipal userPrincipal) {
        donationHeadService.activateDonationHead(id, userPrincipal.getId());
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> deactivateDonationHead(
            @PathVariable UUID id,
            @CurrentUser UserPrincipal userPrincipal) {
        donationHeadService.deactivateDonationHead(id, userPrincipal.getId());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/all")
    public ResponseEntity<GetAllDonationHeads> fetchAllDonationHeads(
            @RequestBody(required = false) DonationHeadPaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = DonationHeadPaginationRequest.builder().build();
        }
        GetAllDonationHeads getAllDonationHeads = donationHeadService.fetchAllDonationHeads(paginationRequest,
                userPrincipal.getId());
        return ResponseEntity.ok(getAllDonationHeads);
    }

    @GetMapping("/{id}")
    public ResponseEntity<DonationHeadResponseDTO> getDonationHeadById(@PathVariable UUID id) {
        DonationHeadResponseDTO response = donationHeadService.getDonationHeadById(id);
        return ResponseEntity.ok(response);
    }


    @GetMapping("/by-org/{orgId}")
    public ResponseEntity<DonationHeadsByOrgIdResponseDTO> getDonationHeadsByOrgId(
            @PathVariable UUID orgId) {
        DonationHeadsByOrgIdResponseDTO response = donationHeadService.getDonationHeadsByOrgId(orgId);
        return ResponseEntity.ok(response);
    }



    @GetMapping("/get-donation-heads")
    public ResponseEntity<List<DonationHeadsDropDown>> getAllDonationHeads() {
        List<DonationHeadsDropDown> response = donationHeadService.getDonationHeads();
        return ResponseEntity.ok(response);
    }


}