package com.chidhagni.donationreceipt.donationhead;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadRequestDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsDropDown;
import com.chidhagni.donationreceipt.donationhead.dto.request.GetAllDonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadResponseDTO;

import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadPaginationRequest;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadsByOrgIdResponseDTO;
import com.chidhagni.donationreceipt.donationhead.utils.DonationHeadMapper;

import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.impl.DSL;

import org.jooq.Record;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.Tables.DONATION_HEADS;


@Service
@RequiredArgsConstructor
@Slf4j
public class DonationHeadService {

    private final DonationHeadRepository donationHeadRepository;
    private final DonationHeadMapper donationHeadMapper;
    private final IndividualRoleRepository individualRoleRepository;


    public UUID createDonationHead(DonationHeadRequestDTO requestDTO, UUID userId) {
        checkDonationHeadExistsAndThrow(requestDTO.getOrgId(), requestDTO.getName());
        DonationHeads donationHead =
                donationHeadMapper.donationHeadRequestDtoToDonationHead(requestDTO, userId, UUID.randomUUID());
        donationHeadRepository.saveDonationHead(donationHead);
        log.info("DonationHead created with [id={}]", donationHead.getId());
        return donationHead.getId();
    }

    public void updateDonationHead(UUID id, DonationHeadRequestDTO requestDTO, UUID userId) {
        DonationHeads existingDonationHead = getDonationHeadOrThrow(id);
        DonationHeads updated = donationHeadMapper.updateDonationHeadFromDto(requestDTO, existingDonationHead, userId);
        donationHeadRepository.updateDonationHead(updated);
        log.info("DonationHead updated with [id={}]", existingDonationHead.getId());
    }

    public void activateDonationHead(UUID id, UUID userId) {
        DonationHeads donationHead = getDonationHeadOrThrow(id);
        donationHead.setIsActive(true);
        donationHead.setUpdatedOn(DateUtils.currentTimeIST());
        donationHead.setUpdatedBy(userId);
        donationHeadRepository.updateDonationHead(donationHead);
        log.info("DonationHead activated with [id={}]", id);
    }

    public void deactivateDonationHead(UUID id, UUID userId) {
        DonationHeads donationHead = getDonationHeadOrThrow(id);
        donationHead.setIsActive(false);
        donationHead.setUpdatedOn(DateUtils.currentTimeIST());
        donationHead.setUpdatedBy(userId);
        donationHeadRepository.updateDonationHead(donationHead);
        log.info("DonationHead deactivated with [id={}]", id);
    }

    public GetAllDonationHeads fetchAllDonationHeads(DonationHeadPaginationRequest request, UUID individualId) {
        applyDefaults(request);

        // Build the base search condition
        Condition searchCondition = getSearchingCondition(request);

        // Apply organization filter based on category
        Condition finalCondition = individualRoleRepository.applyOrganizationFilterDonationHead(individualId, searchCondition);

        List<Record> result = donationHeadRepository.fetchDonationHeadByPagination(request, finalCondition);
        List<DonationHeadResponseDTO> donationHeads = result.stream()
                .map(this::mapDbRecordToDonationHeadResponseDTO)
                .collect(Collectors.toList());
        Integer count = donationHeadRepository.fetchDonationHeadCount(finalCondition);
        return GetAllDonationHeads.builder()
                .donationHeads(donationHeads)
                .rowCount(count)
                .build();
    }


    public DonationHeadResponseDTO getDonationHeadById(UUID id) {
        DonationHeads donationHead = getDonationHeadOrThrow(id);
        return donationHeadMapper.donationHeadToResponseDTO(donationHead);
    }

    private void applyDefaults(DonationHeadPaginationRequest request) {
        if (request.getPage() == null) request.setPage(1);
        if (request.getPageSize() == null) request.setPageSize(10);
    }

    private Condition getSearchingCondition(DonationHeadPaginationRequest paginationRequest) {
        String searchByName = paginationRequest.getNameFilter();
        String searchByDescription = paginationRequest.getDescriptionFilter();
        UUID orgId = paginationRequest.getOrgIdFilter();

        String searchKeyword = paginationRequest.getSearchKeyWord();

        if (searchKeyword == null) {
            searchKeyword = "";
        }

        List<Condition> conditions = new ArrayList<>();

        if (searchByName != null && !searchByName.isEmpty()) {
            conditions.add(DONATION_HEADS.NAME.containsIgnoreCase(searchByName));
        }

        if (searchByDescription != null && !searchByDescription.isEmpty()) {
            conditions.add(DONATION_HEADS.DESCRIPTION.containsIgnoreCase(searchByDescription));
        }

        if (orgId != null) {
            conditions.add(DONATION_HEADS.ORG_ID.eq(orgId));
        }

        if (searchKeyword != null && !searchKeyword.isEmpty()) {
            conditions.add(DONATION_HEADS.NAME.containsIgnoreCase(searchKeyword));
        }
        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
    }

    private DonationHeadResponseDTO mapDbRecordToDonationHeadResponseDTO(Record record) {
        return DonationHeadResponseDTO.builder()
                .id(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.ID))
                .name(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.NAME))
                .description(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.DESCRIPTION))
                .orgId(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.ORG_ID))
                .createdOn(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.CREATED_ON))
                .createdBy(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.CREATED_BY))
                .updatedOn(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.UPDATED_ON))
                .updatedBy(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.UPDATED_BY))
                .isActive(record.get(com.chidhagni.donationreceipt.db.jooq.tables.DonationHeads.DONATION_HEADS.IS_ACTIVE))
                .build();
    }

    private void checkDonationHeadExistsAndThrow(UUID orgId, String name) {
        if (donationHeadRepository.existsByOrgIdAndName(orgId, name)) {
            throw new RuntimeException("Donation Head Already exists");
        }
    }

    public DonationHeads getDonationHeadOrThrow(UUID id) {
        DonationHeads donationHead = donationHeadRepository.fetchOneById(id);
        if (donationHead == null) {
            throw new RuntimeException("DonationHead not found for ID: " + id);
        }
        return donationHead;
    }

    public DonationHeadsByOrgIdResponseDTO getDonationHeadsByOrgId(UUID orgId) {
        List<Record> result = donationHeadRepository.findAllByOrgId(orgId);
        List<DonationHeadsByOrgIdResponseDTO.DonationHeadSummary> donationHeads = result.stream()
                .map(record -> new DonationHeadsByOrgIdResponseDTO.DonationHeadSummary(
                        record.get(DONATION_HEADS.ID),
                        record.get(DONATION_HEADS.NAME)))
                .collect(Collectors.toList());
        return new DonationHeadsByOrgIdResponseDTO(donationHeads);
    }


    public List<DonationHeadsDropDown> getDonationHeads()
    {
        return donationHeadRepository.getAllDonationHeads();
    }

}