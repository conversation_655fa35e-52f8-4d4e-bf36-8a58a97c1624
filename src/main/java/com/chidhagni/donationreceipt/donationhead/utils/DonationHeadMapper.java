package com.chidhagni.donationreceipt.donationhead.utils;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonationHeads;
import com.chidhagni.donationreceipt.donationhead.dto.request.DonationHeadRequestDTO;
import com.chidhagni.donationreceipt.donationhead.dto.response.DonationHeadResponseDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.UUID;

@Mapper(componentModel = "spring")
public interface DonationHeadMapper {

    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "requestDTO.name")
    @Mapping(target = "description", source = "requestDTO.description")
    @Mapping(target = "orgId", source = "requestDTO.orgId")
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "createdBy", source = "userId")
    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedOn", ignore = true)
    DonationHeads donationHeadRequestDtoToDonationHead(DonationHeadRequestDTO requestDTO, UUID userId, UUID id);

    @Mapping(target = "name", source = "requestDTO.name")
    @Mapping(target = "description", source = "requestDTO.description")
    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
    @Mapping(target = "updatedBy", source = "userId")
    DonationHeads updateDonationHeadFromDto(DonationHeadRequestDTO requestDTO, @MappingTarget DonationHeads donationHeads, UUID userId);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    @Mapping(target = "description", source = "description")
    @Mapping(target = "orgId", source = "orgId")
    @Mapping(target = "isActive", source = "isActive")
    @Mapping(target = "createdOn", source = "createdOn")
    @Mapping(target = "createdBy", source = "createdBy")
    @Mapping(target = "updatedOn", source = "updatedOn")
    @Mapping(target = "updatedBy", source = "updatedBy")
    DonationHeadResponseDTO donationHeadToResponseDTO(DonationHeads donationHead);
}