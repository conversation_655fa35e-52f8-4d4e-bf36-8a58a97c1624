package com.chidhagni.donationreceipt.selectDropdown;

//import com.chidhagni.houzer.db.jooq.Tables;
//import com.chidhagni.houzer.db.jooq.tables.daos.SettingsDao;
//import com.chidhagni.houzer.entity.shared.enums.EntityCategory;
//import com.chidhagni.houzer.organisation.constants.DefaultOrganisationCategoryEnums;
//import com.chidhagni.houzer.usercategory.DefaultUserCategoryEnums;
//import com.chidhagni.houzer.users.UserStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

//
//import static com.chidhagni.houzer.db.jooq.Tables.*;
//import static com.chidhagni.houzer.db.jooq.tables.Users.USERS;


@Service
@Slf4j
public class SelectDropdownService {
//    private final SettingsDao settingsDao;
//    private final ObjectMapper objectMapper;
//    private final Integer LIMIT = 2000;
//    private final Integer OFFSET = 0;
//
//    @Value("${society.user.category.id}")
//    private UUID societyUserCategoryId;
//
//
//    private final DSLContext dslContext;
//
//    public SelectDropdownService(SettingsDao settingsDao, ObjectMapper objectMapper, DSLContext dslContext) {
//        this.settingsDao = settingsDao;
//        this.objectMapper = objectMapper;
//        this.dslContext = dslContext;
//    }
//
//    public SelectionResponseDTO getDropDown(SelectionResponseDTO.SelectionType selectionType
//            , EntityCategory entityCategory) {
//        List<SelectionResponseDTO.SelectionDTO> selectionDTOList = new ArrayList<>();
//
//        switch (selectionType) {
//
//            case EMPLOYEES:
//
//                /*
//                  We are using this in the users page to create the accounts for the employees,
//                  so we fetch only the employee with REGISTERED as status.
//                  It does not return all the employees.                 *
//                  If the employee is created status will be REGISTERED, when the login is created in the users page status will be ACTIVE.
//                 */
//                selectionDTOList = dslContext
//                        .select(
//                                INDIVIDUAL.ID.as("id"),
//                                DSL.concat(
//                                        INDIVIDUAL.FIRST_NAME,
//                                        DSL.val(" "),
//                                        DSL.coalesce(INDIVIDUAL.LAST_NAME, DSL.val(""))
//                                ).as("name"),
//                                DSL.concat(
//                                        DSL.val("{\"mobileNumber\":\""),
//                                        DSL.coalesce(INDIVIDUAL.MOBILE_NUMBER, DSL.val("")),
//                                        DSL.val("\",\"email\":\""),
//                                        DSL.coalesce(INDIVIDUAL.EMAIL, DSL.val("")),
//                                        DSL.val("\"}")
//                                ).cast(JSONB).as("metaData")
//                        )
//                        .from(Tables.INDIVIDUAL)
//                        .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(INDIVIDUAL.ID))
//                        .leftJoin(ORGANISATION).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
//                        .where(ORGANISATION.CATEGORY.eq(DefaultOrganisationCategoryEnums.EMPLOYEE.name()))
//                        .orderBy(DSL.concat(
//                                INDIVIDUAL.FIRST_NAME,
//                                DSL.val(" "),
//                                DSL.coalesce(INDIVIDUAL.LAST_NAME, DSL.val(""))
//                        ).asc()) // Sorting by concatenated name in ascending order
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case SOCIETY_NAME:
//                int societyNameLimit = LIMIT;
//                int societyNameOffset = OFFSET;
//                while(true) {
//                    List<SelectionResponseDTO.SelectionDTO> batch = settingsDao.ctx()
//                            .select(
//                                    ORGANISATION.ID.as("id"),
//                                    ORGANISATION.NAME.as("name")
//                            )
//                            .from(ORGANISATION)
//                            .where(ORGANISATION.CATEGORY.eq(DefaultOrganisationCategoryEnums.SOCIETY.name()))
//                            .limit(societyNameLimit)
//                            .offset(societyNameOffset)
//                            .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                    selectionDTOList.addAll(batch);
//                    if(batch.size()< societyNameLimit){
//                        break;
//                    }
//                    societyNameOffset += societyNameLimit;
//                }
//                log.info("society name result size :: {}", selectionDTOList.size());
//                break;
//            case LOCATIONS:
//                selectionDTOList = dslContext
//                        .select(
//                                LOCATION_ZONE_MAPPING.LOCATION_ID.as("id"),
//                                LOCATION_ZONE_MAPPING.ZONE_ID.as("zoneId"),
//                                LIST_VALUES.NAME.as("name")
//                        )
//                        .from(LOCATION_ZONE_MAPPING)
//                        .join(LIST_VALUES).on(LIST_VALUES.ID.eq(LOCATION_ZONE_MAPPING.LOCATION_ID))
//                        .orderBy(LIST_VALUES.NAME.asc()) // Sorting by LIST_VALUES.NAME in ascending order
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case INDIVIDUALS :
//                Integer limit = LIMIT;
//                Integer offset = OFFSET;
//                while (true) {
//                    List<SelectionResponseDTO.SelectionDTO> batch = settingsDao.ctx().select(INDIVIDUAL.ID.as("id")
//                                        , DSL.concat(
//                                                INDIVIDUAL.FIRST_NAME,
//                                                DSL.inline(" "),
//                                                DSL.coalesce(INDIVIDUAL.LAST_NAME, DSL.val("")),
//                                                DSL.inline(" "),
//                                                DSL.coalesce(ORGANISATION.CATEGORY, DSL.val(""))
//                                        ).as("name")
//                                ).from(INDIVIDUAL)
//                        .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(INDIVIDUAL.ID))
//                        .leftJoin(ORGANISATION).on(ORGANISATION.ID.eq(INDIVIDUAL_ROLE.ORG_ID))
//                        .orderBy(DSL.concat(INDIVIDUAL.FIRST_NAME, DSL.inline(" "), DSL.coalesce(INDIVIDUAL.LAST_NAME, DSL.val(""), DSL.coalesce(ORGANISATION.CATEGORY, DSL.val("")))))
//                        .offset(offset)
//                        .limit(limit)
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                    selectionDTOList.addAll(batch);
//
//                    if (batch.size() < limit) {
//                        break;
//                    }
//                    offset += limit;
//                }
//                log.info("users result size :: {}", selectionDTOList.size());
//
//                break;
//
//            case LIST_NAME:
//                selectionDTOList = dslContext.select(
//                                LIST_NAMES.NAME.as("name"),
//                                LIST_NAMES.ID.as("id")
//                        )
//                        .from(LIST_NAMES)
//                        .orderBy(LIST_NAMES.NAME.asc())
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            default:
//                log.warn("Unhandled selectionType: {}", selectionType);
//                selectionDTOList = new ArrayList<>(); // Return an empty list or handle it as needed
//                break;
//
//        }
//
//        return SelectionResponseDTO
//                .builder()
//                .entityCategory(entityCategory)
//                .selectionType(selectionType)
//                .data(selectionDTOList)
//                .build();
//
//    }
//
//    public SelectionResponseDTO get(SelectionResponseDTO.SelectionType selectionType, EntityCategory entityCategory) {
//
//        List<SelectionResponseDTO.SelectionDTO> selectionDTOList = new ArrayList<>();
//
//
//        switch (selectionType) {
//
//            case PROFILES:
//                Condition condition = MEMBER.IS_VERIFIED.eq(true);
//
//                if (entityCategory != null) {
//                    condition = condition.and(ENTITY.CATEGORY.eq(entityCategory.toString()));
//                }
//
//
//                selectionDTOList = dslContext
//                        .select(
//                                MEMBER.ID.as("id"),
//                                ENTITY_MEMBER.ENTITY_ID,
//                                ENTITY.CATEGORY.as("entityCategory"),
//                                DSL.concat(
//                                        MEMBER.FIRST_NAME,
//                                        DSL.val(" "),
//                                        DSL.coalesce(MEMBER.LAST_NAME, DSL.val(""))
//                                ).as("name"),
//                                DSL.concat(
//                                        DSL.val("{\"mobileNumber\":\""),
//                                        DSL.coalesce(MEMBER.LOGIN_MOBILE_NUMBER, DSL.val("")),
//                                        DSL.val("\",\"email\":\""),
//                                        DSL.coalesce(MEMBER.LOGIN_EMAIL, DSL.val("")),
//                                        DSL.val("\"}")
//                                ).cast(JSONB).as("metaData")
//                        )
//                        .from(MEMBER)
//                        .join(ENTITY_MEMBER).on(MEMBER.ID.eq(ENTITY_MEMBER.MEMBER_ID))
//                        .join(ENTITY).on(ENTITY.ID.eq(ENTITY_MEMBER.ENTITY_ID))
//                        .where(condition)
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//
//            case EMPLOYEES:
//
//                /*
//                  We are using this in the users page to create the accounts for the employees,
//                  so we fetch only the employee with REGISTERED as status.
//                  It does not return all the employees.                 *
//                  If the employee is created status will be REGISTERED, when the login is created in the users page status will be ACTIVE.
//                 */
//                UUID employeeId = UUID.fromString(DefaultUserCategoryEnums.EMPLOYEE.getValue());
//                selectionDTOList = dslContext
//                        .select(
//                                USERS.ID.as("id"),
//                                DSL.concat(
//                                        USERS.FIRST_NAME,
//                                        DSL.val(" "),
//                                        DSL.coalesce(USERS.LAST_NAME, DSL.val(""))
//                                ).as("name"),
//                                DSL.concat(
//                                        DSL.val("{\"mobileNumber\":\""),
//                                        DSL.coalesce(USERS.MOBILE_NUMBER, DSL.val("")),
//                                        DSL.val("\",\"email\":\""),
//                                        DSL.coalesce(USERS.EMAIL, DSL.val("")),
//                                        DSL.val("\"}")
//                                ).cast(JSONB).as("metaData")
//                        )
//                        .from(Tables.USERS)
//                        .where(USERS.USER_CATEGORY_ID.eq(employeeId)
//                                .and(USERS.STATUS.equalIgnoreCase(UserStatusEnum.REGISTERED.getValue()))
//                                .and(USERS.ROLE_ID.isNull()))
//                        .orderBy(DSL.concat(
//                                USERS.FIRST_NAME,
//                                DSL.val(" "),
//                                DSL.coalesce(USERS.LAST_NAME, DSL.val(""))
//                        ).asc()) // Sorting by concatenated name in ascending order
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
////TODO: After we make the Employee status as Active upon password reset, we need to remove the condition. .and(USERS.ROLE_ID.isNull())
//                break;
//            case ALL_EMPLOYEES:
//                /*
//                  We are using this in the basic-profile page to assign Service Profile to the employees,
//                  It returns all the employees.                 *
//                  If the employee is created status will be REGISTERED, when the login is created in the users page status will be ACTIVE.
//                 */
//
//                selectionDTOList = dslContext
//                        .select(
//                                USERS.ID.as("id"),
//                                DSL.concat(
//                                        USERS.FIRST_NAME,
//                                        DSL.val(" "),
//                                        DSL.coalesce(USERS.LAST_NAME, DSL.val(""))
//                                ).as("name"),
//                                DSL.concat(
//                                        DSL.val("{\"mobileNumber\":\""),
//                                        DSL.coalesce(USERS.MOBILE_NUMBER, DSL.val("")),
//                                        DSL.val("\",\"email\":\""),
//                                        DSL.coalesce(USERS.EMAIL, DSL.val("")),
//                                        DSL.val("\"}")
//                                ).cast(JSONB).as("metaData")
//                        )
//                        .from(Tables.USERS)
//                        .where(USERS.USER_CATEGORY_ID.eq(UUID.fromString(DefaultUserCategoryEnums.EMPLOYEE.getValue())))
//                        .orderBy(DSL.concat(
//                                USERS.FIRST_NAME,
//                                DSL.val(" "),
//                                DSL.coalesce(USERS.LAST_NAME, DSL.val(""))
//                        ).asc()) // Sorting by concatenated name in ascending order
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
////TODO: After we make the Employee status as Active upon password reset, we need to remove the condition. .and(USERS.ROLE_ID.isNull())
//                break;
//            case ROLE_TYPES:
//                selectionDTOList = dslContext.select(
//                                USER_CATEGORY.ID.as("id"),
//                                Tables.USER_CATEGORY.ROLE_TYPE.as("name"),
//                                Tables.USER_CATEGORY.HAS_SUB_TYPES.as("hasSubtypes"),
//                                Tables.USER_CATEGORY.META_DATA.as("metaData"))
//                        .from(Tables.USER_CATEGORY)
//                        .where((Tables.USER_CATEGORY.IS_ACTIVE).eq(true))
//                        .orderBy(Tables.USER_CATEGORY.ROLE_TYPE.asc()) // Sorting by ROLE_TYPE in ascending order
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case EMPLOYEE_SUBCATEGORIES:
//                selectionDTOList = dslContext.select(
//                                Tables.USER_CATEGORY.ID.as("id"),
//                                Tables.USER_CATEGORY.ROLE_TYPE.as("name"),
//                                Tables.USER_CATEGORY.HAS_SUB_TYPES.as("hasSubtypes"),
//                                Tables.USER_CATEGORY.META_DATA.as("metaData"))
//                        .from(Tables.USER_CATEGORY)
//                        .where((USER_CATEGORY.ROLE_TYPE).equalIgnoreCase("Employee"))
//                        .orderBy(Tables.USER_CATEGORY.ROLE_TYPE.asc())
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case ROLES:
//                UUID employeeCategoryId = UUID.fromString(DefaultUserCategoryEnums.EMPLOYEE.getValue());
//
//                selectionDTOList = dslContext
//                        .select(
//                                Tables.ROLE.ID.as("id"),
//                                Tables.ROLE.NAME.as("name")
//                        )
//                        .from(Tables.ROLE)
//                        .join(Tables.USER_CATEGORY)
//                        .on(Tables.ROLE.ROLE_TYPE_ID.eq(Tables.USER_CATEGORY.ID))
//                        .where(Tables.USER_CATEGORY.ID.eq(employeeCategoryId)
//                                .and(Tables.ROLE.IS_ACTIVE.isTrue()))
//                        .orderBy(Tables.ROLE.NAME.asc())
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case SOCIETY_NAME:
//                selectionDTOList = dslContext
//                        .select(
//                                USERS.ID.as("id"),
//                                USERS.META_DATA.as("metaData")
//                        )
//                        .from(Tables.USERS)
//                        .where(USERS.USER_CATEGORY_ID.eq(UUID.fromString(DefaultUserCategoryEnums.SOCIETY.getValue())))
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case LOCATIONS:
//                selectionDTOList = dslContext
//                        .select(
//                                LOCATION_ZONE_MAPPING.LOCATION_ID.as("id"),
//                                LOCATION_ZONE_MAPPING.ZONE_ID.as("zoneId"),
//                                LIST_VALUES.NAME.as("name")
//                        )
//                        .from(LOCATION_ZONE_MAPPING)
//                        .join(LIST_VALUES).on(LIST_VALUES.ID.eq(LOCATION_ZONE_MAPPING.LOCATION_ID))
//                        .orderBy(LIST_VALUES.NAME.asc()) // Sorting by LIST_VALUES.NAME in ascending order
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case SOCIETY_SUB_CATEGORIES:
//                selectionDTOList = dslContext.select(
//                                Tables.USER_CATEGORY.ID.as("id"),
//                                Tables.USER_CATEGORY.ROLE_TYPE.as("name"),
//                                Tables.USER_CATEGORY.HAS_SUB_TYPES.as("hasSubtypes"),
//                                Tables.USER_CATEGORY.META_DATA.as("metaData"))
//                        .from(Tables.USER_CATEGORY)
//                        .where(USER_CATEGORY.ID.eq(societyUserCategoryId))
//                        .orderBy(Tables.USER_CATEGORY.ROLE_TYPE.asc())
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case CATEGORIES:
//                selectionDTOList = dslContext.select(
//                                Tables.USER_CATEGORY.ID.as("id"),
//                                Tables.USER_CATEGORY.ROLE_TYPE.as("name"),
//                                Tables.USER_CATEGORY.IS_ACTIVE.as("isActive"))
//                        .from(Tables.USER_CATEGORY)
//                        .orderBy(Tables.USER_CATEGORY.ROLE_TYPE.asc())
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case USERS_CATEGORIES:
//                selectionDTOList = dslContext.select(
//                                Tables.USER_CATEGORY.ID.as("id"),
//                                Tables.USER_CATEGORY.ROLE_TYPE.as("name"))
//                        .from(Tables.USER_CATEGORY)
//                        .where(USER_CATEGORY.IS_ACTIVE.eq(Boolean.TRUE))
//                        .orderBy(Tables.USER_CATEGORY.ROLE_TYPE.asc())
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            case USERS:
//                selectionDTOList = dslContext.select(
//                                USERS.ID.as("id"),
//                                DSL.concat(
//                                        USERS.FIRST_NAME,
//                                        DSL.val(" "),
//                                        DSL.coalesce(USERS.LAST_NAME, DSL.val("")),
//                                        DSL.val(" "),
//                                        DSL.coalesce(
//                                                DSL.field(
//                                                        dslContext.select(USER_CATEGORY.ROLE_TYPE)
//                                                                .from(Tables.USER_CATEGORY)
//                                                                .where(USERS.USER_CATEGORY_ID.eq(USER_CATEGORY.ID))
//                                                                .asField()
//                                                ),
//                                                DSL.val("")
//                                        )
//                                ).as("name")
//                        )
//                        .from(USERS)
//                        .orderBy(DSL.concat(
//                                USERS.FIRST_NAME,
//                                DSL.val(" "),
//                                DSL.coalesce(USERS.LAST_NAME, DSL.val("")),
//                                DSL.val(" "),
//                                DSL.coalesce(
//                                        DSL.field(
//                                                dslContext.select(USER_CATEGORY.ROLE_TYPE)
//                                                        .from(Tables.USER_CATEGORY)
//                                                        .where(USERS.USER_CATEGORY_ID.eq(USER_CATEGORY.ID))
//                                                        .asField()
//                                        ),
//                                        DSL.val("")
//                                )
//                        ).asc()) // Sorting by the concatenated name in ascending order
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//
//            case LIST_NAME:
//                selectionDTOList = dslContext.select(
//                                LIST_NAMES.NAME.as("name"),
//                                LIST_NAMES.ID.as("id")
//                        )
//                        .from(LIST_NAMES)
//                        .orderBy(LIST_NAMES.NAME.asc())
//                        .fetchInto(SelectionResponseDTO.SelectionDTO.class);
//                break;
//            default:
//                log.warn("Unhandled selectionType: {}", selectionType);
//                selectionDTOList = new ArrayList<>(); // Return an empty list or handle it as needed
//                break;
//
//        }
//
//        return SelectionResponseDTO
//                .builder()
//                .entityCategory(entityCategory)
//                .selectionType(selectionType)
//                .data(selectionDTOList)
//                .build();
//
//    }

}