package com.chidhagni.donationreceipt.selectDropdown;


import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/selectDropdown")
public class SelectDropdownController {

    private final SelectDropdownService selectDropdownService;

    public SelectDropdownController(SelectDropdownService selectDropdownService) {
        this.selectDropdownService = selectDropdownService;
    }

//    @GetMapping
//    public ResponseEntity<SelectionResponseDTO> get(@Valid @RequestParam(value = "selectionType")
//                                                    SelectionResponseDTO.SelectionType selectionType,
//                                                    @Valid @RequestParam(value = "entityCategory", required = false)
//                                                    EntityCategory entityCategory) throws JsonProcessingException {
//
//        SelectionResponseDTO selectionResponseDTO = selectDropdownService.get(selectionType, entityCategory);
//        return ResponseEntity.ok().body(selectionResponseDTO);
//
//    }
//
//    @GetMapping("/individual-organisation-derived")
//    public ResponseEntity<SelectionResponseDTO> getDropDown(@Valid @RequestParam(value = "selectionType")
//                                                    SelectionResponseDTO.SelectionType selectionType,
//                                                    @Valid @RequestParam(value = "entityCategory", required = false)
//                                                    EntityCategory entityCategory) throws JsonProcessingException {
//
//        SelectionResponseDTO selectionResponseDTO = selectDropdownService.getDropDown(selectionType, entityCategory);
//        return ResponseEntity.ok().body(selectionResponseDTO);
//
//    }

}
