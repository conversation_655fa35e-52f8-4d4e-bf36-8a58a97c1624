package com.chidhagni.donationreceipt.selectDropdown;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SelectionResponseDTO {
    private SelectionType selectionType;
    private List<SelectionDTO> data;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SelectionDTO {
        private UUID id;
        private UUID zoneId;
        private UUID userId;
        private UUID entityId;
        private String entityCategory;
        private String name;
        private Boolean hasSubtypes;
        private JsonNode metaData;
        private Boolean isActive;
    }

    public enum SelectionType {
        EMPLOYEES, ALL_EMPLOYEES, PROFILES, ROLE_TYPES, ROLES,SOCIETY_NAME,LIST_NAME,EMPLOYEE_SUBCATEGORIES,
        LOCATIONS,SOCIETY_SUB_CATEGORIES,CATEGORIES, USERS,USERS_CATEGORIES, EMPLOYEE, SOCIETY, SERVICE_PROVIDER, INDIVIDUALS
    }

}
