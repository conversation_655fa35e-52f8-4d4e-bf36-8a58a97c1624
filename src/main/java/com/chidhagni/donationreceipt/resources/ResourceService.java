package com.chidhagni.donationreceipt.resources;


//import com.chidhagni.houzer.db.jooq.tables.pojos.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ResourceService {

//    private final ResourceRepository resourceRepository;
//
//
//    public void createResources(List<ResourceDTO> resourceDTOList, UserPrincipal userId) {
//
//        if (resourceDTOList == null || resourceDTOList.isEmpty()) {
//          throw new IllegalArgumentException("Resource list is empty");
//        }
//        resourceDTOList.stream()
//                .map(resourceDTO -> {
//                    Resource resource = ResourceMapper.mapResourceDTOToResource(resourceDTO, userId);
//                    resourceRepository.createResource(resource);
//                    log.info("Resource Root Created Successfully");
//                    return resourceDTO;
//                })
//                .forEach(resourceDTO -> {
//                    if (resourceDTO.getChildDTO() != null && !resourceDTO.getChildDTO().isEmpty()) {
//                        insertChildResources(resourceDTO.getChildDTO(), resourceDTO.getId(), userId);
//                    }
//                });
//    }
//
//    private void insertChildResources(List<ResourceDTO> childDTOList, UUID parentId, UserPrincipal userId) {
//        childDTOList.stream()
//                .map(childDTO -> {
//                    childDTO.setParentResourceId(parentId);
//                    Resource childResource = ResourceMapper.mapResourceDTOToResource(childDTO, userId);
//                    resourceRepository.createResource(childResource);
//                    log.info("Resource Created Successfully");
//                    return childDTO;
//                })
//                .forEach(childDTO -> {
//                    if (childDTO.getChildDTO() != null && !childDTO.getChildDTO().isEmpty()) {
//                        insertChildResources(childDTO.getChildDTO(), childDTO.getId(), userId);
//                    }
//                });
//    }
//
//
//    public ResourceDTO getResourceTree(UUID parentId) throws JsonProcessingException {
//        return resourceRepository.getCompleteTree(parentId);
//    }


}
