package com.chidhagni.donationreceipt.resources;


//import com.chidhagni.houzer.db.jooq.tables.pojos.Resource;
import org.springframework.stereotype.Component;

@Component
public class ResourceMapper {


//    public static Resource mapResourceDTOToResource(ResourceDTO resourceDTO, UserPrincipal userPrincipal) {
//        return new Resource()
//                .setId(UUID.randomUUID())
//                .setName(resourceDTO.getName())
//                .setDescription(resourceDTO.getDescription())
//                .setType(resourceDTO.getType())
//                .setParentResourceId(resourceDTO.getParentResourceId())
//                .setIsActive(true)
//                .setCreatedBy(userPrincipal.getId())
//                .setUpdatedBy(userPrincipal.getId())
//                .setCreatedOn(DateUtils.currentDatetime())
//                .setUpdatedOn(DateUtils.currentDatetime());
//    }
}
