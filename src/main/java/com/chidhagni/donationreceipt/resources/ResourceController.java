package com.chidhagni.donationreceipt.resources;


import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/resource")
public class ResourceController {


//    private final ResourceService resourceService;
//
//
//    @PostMapping(produces = RESOURCES_CREATE_RES_V1, consumes = RESOURCES_CREATE_REQ_V1)
//    @ResponseStatus(HttpStatus.CREATED)
//    @PreAuthorize("@customMethodSecurity.isAuthenticatedWithPermission('$..[?(@.name == \"Site_Map\")]'," +
//            " @permissionConstants.WRITE_PERMISSION)")
//    public ResponseEntity<String> createResources(@RequestBody List<ResourceDTO> resourceDTOList,
//                                                  @CurrentUser UserPrincipal userPrincipal) {
//        resourceService.createResources(resourceDTOList, userPrincipal);
//        return ResponseEntity.ok("Resources created successfully");
//    }
//
//
//    @GetMapping(value = "/all/{id}", produces = RESOURCES_GET_RES_V1)
//    @PreAuthorize("@customMethodSecurity.isAuthenticatedWithPermission('$[?(@.name == \"Left_Menu\")].children[?(@.name == \"Documents\")]'," +
//            " @permissionConstants.READ_PERMISSION)")
//    @ResponseStatus(HttpStatus.OK)
//    public ResourceDTO getResourceTree(@PathVariable("id") UUID parentId) throws  JsonProcessingException {
//        return resourceService.getResourceTree(parentId);
//    }


}
