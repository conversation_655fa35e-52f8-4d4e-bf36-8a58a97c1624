package com.chidhagni.donationreceipt.services.listvalues;

import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.services.PaginationRequest;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.util.UriComponentsBuilder;

import javax.validation.Valid;
import java.util.UUID;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

@CrossOrigin
@RestController
@RequestMapping("/list-values")
public class ListValuesController {
    private final ListValuesService listValuesService;

    public ListValuesController(ListValuesService listValuesService) {
        this.listValuesService = listValuesService;
    }

    @PostMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
    public ResponseEntity<ListValuesDTO> create(@RequestBody @Valid ListValuesDTO listValuesDTO, @CurrentUser UserPrincipal userPrincipal) throws Exception {

        ListValuesDTO listValuesDTO1 = listValuesService.create(listValuesDTO, userPrincipal);
        return ResponseEntity.created(
                        UriComponentsBuilder
                                .fromPath("/list-values")
                                .buildAndExpand()
                                .toUri()
                )
                .body(listValuesDTO1);
    }

    @PatchMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
    public ResponseEntity<ListValuesDTO> update(@RequestBody @Valid ListValuesDTO listValuesDTO, @CurrentUser UserPrincipal userPrincipal) throws Exception {
        return ResponseEntity.ok().body(listValuesService.updateListValues(listValuesDTO, userPrincipal));
    }


    @GetMapping(value = {"/{id}"}, produces = APPLICATION_JSON_VALUE)
    public ResponseEntity<ListValuesDTO> getListValue(@PathVariable UUID id) throws JsonProcessingException {
        return ResponseEntity.ok().body(listValuesService.getListValueById(id));
    }

    @DeleteMapping(value = {"/{id}"}, produces = APPLICATION_JSON_VALUE)
    public void deleteListValue(@PathVariable UUID id, @CurrentUser UserPrincipal userPrincipal) {
        listValuesService.deleteListValue(id, userPrincipal);
    }

    @PatchMapping(value = {"/update/{id}"}, produces = APPLICATION_JSON_VALUE)
    public void updateListValue(@PathVariable UUID id, @CurrentUser UserPrincipal userPrincipal) {
        listValuesService.updateListValue(id, userPrincipal);
    }

    @PatchMapping(value = {"/{listValueId}"}, produces = APPLICATION_JSON_VALUE)
    public void activateListValue(@PathVariable UUID listValueId, @CurrentUser UserPrincipal userPrincipal) {
        listValuesService.activateListValue(listValueId, userPrincipal);
    }

    @PostMapping("/all")
    public ResponseEntity<GetAllListValuesResponse> getAll(@RequestBody(required = false) PaginationRequest paginationRequest) {
        if (paginationRequest == null) {
            paginationRequest = PaginationRequest.builder().page(1).pageSize(10).searchKeyword("").build();
        }
        return ResponseEntity.ok().body(listValuesService.getAll(paginationRequest));
    }

    @PostMapping("/allValuesByNameId")
    public ResponseEntity<GetAllListValuesResponse> getAllServices(@RequestBody(required = false) PaginationRequest paginationRequest) {
        if (paginationRequest == null) {
            paginationRequest = PaginationRequest.builder().page(1).pageSize(10).searchKeyword("").build();
        }
        return ResponseEntity.ok().body(listValuesService.getAllValues(paginationRequest));
    }

    @GetMapping("/listNameId")
    public ResponseEntity<GetAllListValuesDTO> getAllListValues(@RequestParam UUID id) {
        return ResponseEntity.ok().body(listValuesService.getAllListValuesById(id));
    }

    @PostMapping("/all/{listNameId}")
    public ResponseEntity<GetAllListValuesResponse> getAllListValues(
            @PathVariable UUID listNameId,
            @RequestBody PaginationRequest paginationRequest) {

        GetAllListValuesResponse response = listValuesService.getAllListValuesWithPagination(
                listNameId,
                paginationRequest
        );

        return ResponseEntity.ok(response);
    }

}
