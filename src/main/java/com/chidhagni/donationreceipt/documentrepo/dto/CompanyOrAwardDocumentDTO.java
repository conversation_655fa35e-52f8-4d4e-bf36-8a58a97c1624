package com.chidhagni.donationreceipt.documentrepo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyOrAwardDocumentDTO {

    private UUID id;

    private String location;

    private UUID userId;

    private UUID documentCategory;

    private UUID documentSubCategory;

    private String documentFrom;

    private String documentTo;

    private UUID createdBy;

    private UUID updatedBy;

    private LocalDateTime createdOn;

    private LocalDateTime updatedOn;
}