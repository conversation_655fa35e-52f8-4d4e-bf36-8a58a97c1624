package com.chidhagni.donationreceipt.documentrepo;


import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;


@RestController
@RequestMapping("/api/v1/document")
@RequiredArgsConstructor
public class DocumentRepoController {

    private final DocumentRepoService documentRepoService;
    private final ObjectMapper objectMapper;

    /**
     * Updates an existing document in the repository.
     *
     * <p><b>Usage:</b> Documents -> Edit</p>
     *
     * <p>This endpoint allows authenticated users with `UPDATE_PERMISSION` for the `Documents`
     * section under `Left_Menu` to modify an existing document.</p>
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Validates that the file is not null before proceeding.</li>
     *   <li>Parses the `documentDTO` JSON string into a `DocumentRepoDTO` object.</li>
     *   <li>Delegates to `documentRepoService` to update the document metadata and replace the file.</li>
     *   <li>Returns the UUID of the updated document.</li>
     * </ul>
     *
     *
     * @param id             The id of the document to be updated.
     * @param documentDTO    The JSON string containing updated document details.
     * @param file           The new file associated with the document. Cannot be null.
     * @param userPrincipal  The authenticated user performing the update.
     * @return A `ResponseEntity` containing the UUID of the updated document.
     * @throws IOException If an error occurs while processing the file or parsing the JSON data.
     * @throws IllegalArgumentException If the provided file is null.
     * * @throws AccessDeniedException If the user does not have `UPDATE_PERMISSION` for `Documents`.
     * <AUTHOR>
     */
//    @PutMapping(path = {"/{id}"}, consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = DOCUMENT_REPO_UPDATE_RES_V1)
//    @PreAuthorize("@customMethodSecurity.isAuthenticatedWithPermission('$[?(@.name == \"Left_Menu\")].children[?(@.name == \"Documents\")]'," +
//            " @permissionConstants.UPDATE_PERMISSION)")
//    public ResponseEntity<UUID> updateDocument(@PathVariable("id") UUID id
//            , @RequestParam("documentRepoDTO") String documentDTO
//            , @RequestParam("file") MultipartFile file
//            , @CurrentUser UserPrincipal userPrincipal) throws IOException {
//        if (file == null) {
//            throw new IllegalArgumentException("Files cannot be empty");
//        }
//        DocumentRepoDTO documentRepoDTO = objectMapper.readValue(documentDTO, DocumentRepoDTO.class);
//        return new ResponseEntity<>(
//                documentRepoService.updateDocumentsRepo(id, file, documentRepoDTO, userPrincipal), HttpStatus.OK);
//    }
//
//    /**
//     * Uploads a new document to the repository.
//     *
//     * <p><b>Usage:</b> Documents -> Add Document</p>
//     *
//     * <p>This endpoint allows authenticated users with `WRITE_PERMISSION` for the `Documents`
//     * section under `Left_Menu` to create and upload a new document.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul></ul>
//     *   <li>Validates that the file is not null before proceeding.</li>
//     *   <li>Parses the `documentRepoDTO` JSON string into a `DocumentRepoDTO` object.</li>
//     *   <li>Delegates to `documentRepoService` to store the document and its metadata.</li>
//     *   <li>Returns the UUID of the newly created document.</li>
//     * </ul>
//     *
//     *
//     * @param file           The file to be uploaded. Cannot be null.
//     * @param documentRepo   The JSON string containing document details.
//     * @param userPrincipal  The authenticated user performing the upload.
//     * @return A `ResponseEntity` containing the UUID of the newly created document.
//     * @throws IOException If an error occurs while processing the file or parsing the JSON data.
//     * @throws IllegalArgumentException If the provided file is null.
//     * * @throws AccessDeniedException If the user does not have `WRITE_PERMISSION` for `Documents`.
//     * <AUTHOR>
//     */
//    @PostMapping(path = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = DOCUMENTS_CREATE_RES_V1)
//    @PreAuthorize("@customMethodSecurity.isAuthenticatedWithPermission('$[?(@.name == \"Left_Menu\")].children[?(@.name == \"Documents\")]'," +
//            " @permissionConstants.WRITE_PERMISSION)")
//    public ResponseEntity<UUID> createDocument(@RequestParam("file") MultipartFile file
//            , @RequestParam("documentRepoDTO") String documentRepo, @CurrentUser UserPrincipal userPrincipal) throws IOException {
//        if (file == null) {
//            throw new IllegalArgumentException("Files cannot be empty");
//        }
//        DocumentRepoDTO documentRepoDTO = objectMapper.readValue(documentRepo, DocumentRepoDTO.class);
//        return new ResponseEntity<>(documentRepoService.upload(file, documentRepoDTO, userPrincipal), HttpStatus.CREATED);
//    }
//
//
//    /**
//     * Retrieves a document from the repository by its ID.
//     *
//     * <p><b>Usage:</b> Documents -> Edit</p>
//     *
//     * <p>This endpoint allows authenticated users with `READ_PERMISSION` for the `Documents`
//     * section under `Left_Menu` to retrieve a document's details by its unique identifier.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Verifies if the user has the necessary permissions using a custom security method.</li>
//     *   <li>Delegates to `documentRepoService` to fetch the document details.</li>
//     *   <li>Returns the document metadata wrapped in a `GetDocumentRepoResponses` object.</li>
//     * </ul>
//     *
//     *
//     * @param id The UUID of the document to be retrieved.
//     * @return A `GetDocumentRepoResponses` object containing the document details.
//     * * @throws AccessDeniedException If the user does not have `READ_PERMISSION` for `Documents`.
//     * <AUTHOR>
//     */
//    @GetMapping(value = "/{id}",produces=DOCUMENT_REPO_GET_RES_V1)
//    @PreAuthorize("@customMethodSecurity.isAuthenticatedWithPermission('$[?(@.name == \"Left_Menu\")].children[?(@.name == \"Documents\")]'," +
//            " @permissionConstants.READ_PERMISSION)")
//    @ResponseStatus(HttpStatus.OK)
//    public GetDocumentRepoResponses getById(@PathVariable UUID id) {
//        return documentRepoService.getById(id);
//
//    }
//
//    /**
//     * Retrieves a paginated list of all documents for admin
//     *
//     * <p><b>Usage:</b> Documents Landing Page for Admin</p>
//     *
//     * <p>This endpoint allows authenticated users with `READ_PERMISSION` for the `Documents`
//     * section under `Left_Menu` to fetch a paginated list of documents.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Checks if a pagination request is provided; if not, initializes a default pagination request.</li>
//     *   <li>Delegates to `documentRepoService` to retrieve the list of documents.</li>
//     *   <li>Returns a paginated response containing document metadata.</li>
//     * </ul>
//     *
//     *
//     * @param paginationRequest (Optional) The pagination request object containing page and size parameters.
//     * @return A `GetAllDocumentRepoResponses` object containing paginated document details.
//     * * @throws AccessDeniedException If the user does not have `READ_PERMISSION` for `Documents`.
//     * <AUTHOR>
//     */
//    @PostMapping(path = {"/admin/all"}, produces = DOCUMENT_REPO_GET_ALL_ADMIN_RES_V1, consumes = DOCUMENT_REPO_GET_ALL_ADMIN_REQ_V1)
//    @PreAuthorize("@customMethodSecurity.isAuthenticatedWithPermission('$[?(@.name == \"Left_Menu\")].children[?(@.name == \"Documents\")]'," +
//            " @permissionConstants.READ_PERMISSION)")
//    @ResponseStatus(HttpStatus.OK)
//    public GetAllDocumentRepoResponses getAllDocumentRepoResponsesAdmin(@RequestBody(required = false) PaginationRequest paginationRequest) {
//        if (paginationRequest == null) {
//            paginationRequest = PaginationRequest.builder().build();
//        }
//        return documentRepoService.getAllDocumentRepoResponsesAdmin(paginationRequest);
//    }
//
//
//
//    /**
//     * Retrieves a paginated list of all documents.
//     *
//     * <p><b>Usage:</b> Documents Landing Page for Society,Employee,Service Provider</p>
//     *
//     * <p>This endpoint allows authenticated users with `READ_PERMISSION` for the `Documents`
//     * section under `Left_Menu` to fetch a paginated list of documents.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Checks if a pagination request is provided; if not, initializes a default pagination request.</li>
//     *   <li>Delegates to `documentRepoService` to retrieve the list of documents.</li>
//     *   <li>Returns a paginated response containing document metadata.</li>
//     * </ul>
//     *
//     *
//     * @param paginationRequest (Optional) The pagination request object containing page and size parameters.
//     * @param userPrincipal The authenticated user requesting the document list.
//     * @return A `GetAllDocumentRepoResponses` object containing paginated document details.
//     * * @throws AccessDeniedException If the user does not have `READ_PERMISSION` for `Documents`.
//     * <AUTHOR>
//     */
//    @PostMapping(produces = DOCUMENT_REPO_GET_ALL_RES_V1, consumes = DOCUMENT_REPO_GET_ALL_REQ_V1)
//    @PreAuthorize("@customMethodSecurity.isAuthenticatedWithPermission('$[?(@.name == \"Left_Menu\")].children[?(@.name == \"Documents\")]'," +
//            " @permissionConstants.READ_PERMISSION)")
//    @ResponseStatus(HttpStatus.OK)
//    public GetAllDocumentRepoResponses getAllDocumentRepoResponses(@RequestBody(required = false) PaginationRequest paginationRequest,
//                                                                   @CurrentUser UserPrincipal userPrincipal) {
//        if (paginationRequest == null) {
//            paginationRequest = PaginationRequest.builder().build();
//        }
//        return documentRepoService.getAllDocumentRepoResponses(paginationRequest, userPrincipal);
//
//    }
//
//
//
//    @PostMapping("/allDocuments")
//    public ResponseEntity<List<String>> getAllDocumentLocations(
//            @RequestBody DocumentRepoDetailsDTO documentRepoDetailsDTO, @RequestParam("isActive") Boolean isActive
//            , @CurrentUser UserPrincipal userPrincipal) {
//        return ResponseEntity.ok(
//                documentRepoService.getAllDocumentPathByOrgId(documentRepoDetailsDTO, isActive, userPrincipal));
//    }
//
//    @GetMapping("/projects/images")
//    public ResponseEntity<List<DocumentDataDTO>> getAllProjectsDataByServiceNameId(
//            @NotNull @RequestParam("serviceNameId") UUID serviceNameId
//            , @NotNull @RequestParam("individualId") UUID individualId
//            , @CurrentUser UserPrincipal userPrincipal) throws JsonProcessingException {
//        return ResponseEntity.ok(documentRepoService.getAllProjectsDataByServiceNameId(serviceNameId, individualId));
//    }
}
