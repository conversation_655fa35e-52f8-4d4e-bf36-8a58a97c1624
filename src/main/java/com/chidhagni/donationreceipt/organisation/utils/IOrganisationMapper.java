package com.chidhagni.donationreceipt.organisation.utils;

//import com.chidhagni.houzer.db.jooq.tables.pojos.DocumentRepo;
//import com.chidhagni.houzer.documents.DocumentDTO;
import org.mapstruct.*;

@Mapper(componentModel = "spring")
public interface IOrganisationMapper {
//    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
//    void mapNotNullProperties(@MappingTarget ChsProfileDTO chsProfileDTO, CHSProfileDTORequest chsProfileDTORequest);
//
//    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDateInIST().toString())")
//    @Mapping(target = "createdBy", source = "loggedInUserId")
//    void mapNotNullPropertiesSpProfileRequestToDTO(@MappingTarget SpProfileDTOV2 spProfileDTOV2
//            , SpProfileDTORequest spProfileDTORequest, UUID loggedInUserId);
//
//    @Mapping(target = "categoryId", source = "documentDTO.documentCategory")
//    @Mapping(target = "subCategoryId", source = "documentDTO.documentSubCategory")
//    @Mapping(target = "path", ignore = true)
//    @Mapping(target = "remarks", ignore = true)
//    @Mapping(target = "senderDetails", source = "senderDetails")
//    @Mapping(target = "recipientDetails", source = "receiverDetails")
//    @Mapping(target = "fileDate", expression = "java(java.time.LocalDateTime.now())")
//    @Mapping(target = "createdBy", ignore = true)
//    @Mapping(target = "updatedBy", ignore = true)
//    @Mapping(target = "createdOn", ignore = true)
//    @Mapping(target = "updatedOn", ignore = true)
//    @Mapping(target = "tags", expression
//       = "java(com.chidhagni.houzer.documentrepo.dto.request.TagsDTO.builder().documentName(documentSections).build())")
//    DocumentRepoDTO mapDocumentDtoToRepoDTO(DocumentDTO documentDTO, String documentSections
//            , participantDetailsDTO senderDetails, List<participantDetailsDTO> receiverDetails);
//
//    @Mapping(target = "categoryId", source = "documentRepoDetailsDTO.documentCategory")
//    @Mapping(target = "subCategoryId", source = "documentRepoDetailsDTO.documentSubCategory")
//    @Mapping(target = "path", ignore = true)
//    @Mapping(target = "remarks", ignore = true)
//    @Mapping(target = "senderDetails", source = "senderDetails")
//    @Mapping(target = "recipientDetails", source = "receiverDetails")
//    @Mapping(target = "fileDate", expression = "java(java.time.LocalDateTime.now())")
//    @Mapping(target = "createdBy", ignore = true)
//    @Mapping(target = "updatedBy", ignore = true)
//    @Mapping(target = "createdOn", ignore = true)
//    @Mapping(target = "updatedOn", ignore = true)
//    @Mapping(target = "tags", expression
//            = "java(com.chidhagni.houzer.documentrepo.dto.request.TagsDTO.builder().documentName(documentSections).build())")
//    DocumentRepoDTO mapDocumentRepoDetailsDtoToRepoDTO(DocumentRepoDetailsDTO documentRepoDetailsDTO, String documentSections
//            , participantDetailsDTO senderDetails, List<participantDetailsDTO> receiverDetails);
//
//
//    @Mapping(target = "location", source = "path")
//    @Mapping(target = "documentCategory", source = "category")
//    @Mapping(target = "documentSubCategory", source = "subCategory")
//    @Mapping(target = "userId", ignore = true)
//    @Mapping(target = "documentFrom", ignore = true)
//    @Mapping(target = "documentTo", ignore = true)
//    DocumentDTO mapDocumentRepoDocumentDTO(DocumentRepo documentRepo);

}
