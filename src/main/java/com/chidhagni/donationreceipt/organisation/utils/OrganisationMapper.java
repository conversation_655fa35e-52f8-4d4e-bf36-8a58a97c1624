package com.chidhagni.donationreceipt.organisation.utils;

//import com.chidhagni.houzer.db.jooq.tables.daos.OrganisationDao;
//import com.chidhagni.houzer.db.jooq.tables.daos.UsersDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.*;
//import com.chidhagni.houzer.documentrepo.dto.CompanyOrAwardDocumentDTO;
//import com.chidhagni.houzer.documentrepo.dto.request.participantDetailsDTO;
//import com.chidhagni.houzer.entity.profile.architect.AreaOfExpertiseDTO;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Organisation;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Individual;
//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualPermission;
//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualRole;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Organisation;
//import com.chidhagni.houzer.individual.IndividualMetaDataDTO;
//import com.chidhagni.houzer.individual.IndividualRepository;
//import com.chidhagni.houzer.individual.dto.request.IndividualCHSShortFormDTO;
//import com.chidhagni.houzer.individual.dto.request.IndividualSPShortFormDTO;
//import com.chidhagni.houzer.individual.dto.request.IndividualShortFormDTO;
//import com.chidhagni.houzer.individualPasswordResetAudit.dto.ResetStatus;
//import com.chidhagni.houzer.individualsignup.dto.SocietyReadinessReport;
//import com.chidhagni.houzer.individualverificationaudit.constants.ContactType;
//import com.chidhagni.houzer.individualverificationaudit.constants.VerificationStatusEnum;
//import com.chidhagni.houzer.member.SystemCodes;
//import com.chidhagni.houzer.member.microsite.MicroSiteDTO;
//import com.chidhagni.houzer.member.readiness.society.ReadinessDTO;
//import com.chidhagni.houzer.member.readiness.society.ReadinessReport;
//import com.chidhagni.houzer.organisation.constants.DefaultOrganisationCategoryEnums;
//import com.chidhagni.houzer.organisation.dto.*;
//import com.chidhagni.houzer.individualsignup.dto.SocietyReadinessReport;
//import com.chidhagni.houzer.individualsignup.dto.SocietyReadinessReport;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

//import static com.chidhagni.houzer.db.jooq.Tables.ORGANISATION;

@Component
@Slf4j
@RequiredArgsConstructor
public class OrganisationMapper {

//    private final OrganisationDao organisationDao;
//    private final ObjectMapper objectMapper;
//    private final IndividualRepository individualRepository;
//    private final UsersDao usersDao;
//
//
//    public ChsProfileDTO fetchSocietyMetaData(UUID orgId) {
//        String json = organisationDao.ctx()
//                .select(ORGANISATION.META_DATA)
//                .from(ORGANISATION)
//                .where(ORGANISATION.ID.eq(orgId))
//                .fetchOne(ORGANISATION.META_DATA).toString();
//
//        if (json == null) {
//            throw new IllegalArgumentException("No metadata found for organisation ID: " + orgId);
//        }
//
//        return deserializeJsonToChsProfile(json);
//    }
//
//    private ChsProfileDTO deserializeJsonToChsProfile(String json) {
//        try {
//            ObjectMapper mapper = new ObjectMapper()
//                    .registerModule(new JavaTimeModule())
//                    .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
//
//            return mapper.readValue(json, ChsProfileDTO.class);
//
//        } catch (Exception e) {
//            throw new RuntimeException("Failed to parse JSON into ChsProfileDTO", e);
//        }
//    }
//
//
//
//    public SpProfileDTO fetchServiceProviderMetaData(UUID orgId) {
//        SpProfileDTO spProfileDTO = organisationDao.ctx().select(
//                        ORGANISATION.META_DATA
//                )
//                .from(ORGANISATION)
//                .where(ORGANISATION.ID.eq(orgId))
//                .fetchOneInto(SpProfileDTO.class);
//        if (spProfileDTO == null) {
//            throw new IllegalArgumentException("No metadata found for organisation ID: " + orgId);
//        }
//        return spProfileDTO;
//
//    }
//
//    public EmployeeProfileDTO fetchEmployeeMetaData(UUID orgId) {
//        EmployeeProfileDTO record = organisationDao.ctx().select(
//                        ORGANISATION.META_DATA
//                )
//                .from(ORGANISATION)
//                .where(ORGANISATION.ID.eq(orgId))
//                .fetchOneInto(EmployeeProfileDTO.class);
//
//        if (record == null) {
//            throw new IllegalArgumentException("No metadata found for organisation ID: " + orgId);
//        }
//
//        return record;
//    }
//
//
//    public GetOrganisationMetaDataForInvoices mapFromEmployeeProfile(EmployeeProfileDTO employeeProfileDTO) {
//        GetOrganisationMetaDataForInvoices response = new GetOrganisationMetaDataForInvoices();
//        response.setAccountNumber(employeeProfileDTO.getAccountNumber());
//        response.setAccountHolderName(employeeProfileDTO.getAccountHolderName());
//        response.setBankName(employeeProfileDTO.getBankName());
//        response.setIfscCode(employeeProfileDTO.getIfscCode());
//        response.setBranch(employeeProfileDTO.getBranch());
//        response.setPanNo(employeeProfileDTO.getPanNo());
//        response.setGstNo(employeeProfileDTO.getGstNo());
//        response.setStateName(employeeProfileDTO.getStateName());
//        response.setAddress(employeeProfileDTO.getClientAddress());
//        return response;
//    }
//
//    public GetOrganisationMetaDataForInvoices mapFromSocietyProfile(ChsProfileDTO chsProfileDTO) {
//        GetOrganisationMetaDataForInvoices response = new GetOrganisationMetaDataForInvoices();
//        response.setAddress(chsProfileDTO.getSocietyAddress());
//        response.setGstNo(chsProfileDTO.getGstNo());
//        response.setPanNo(chsProfileDTO.getPanNo());
//        response.setStateName(chsProfileDTO.getStateName());
//        response.setBranch(chsProfileDTO.getBranch());
//        response.setIfscCode(chsProfileDTO.getIfscCode());
//        response.setBankName(chsProfileDTO.getBankName());
//        response.setAccountNumber(chsProfileDTO.getAccountNumber());
//        return response;
//    }
//
//
//    public GetOrganisationMetaDataForInvoices mapFromServiceProvider(SpProfileDTO serviceProviderDTO) {
//        List<ServiceProviderAddress> spAddresses = serviceProviderDTO.getSpAddresses();
//        String address = null;
//        if(spAddresses != null && !CollectionUtils.isEmpty(spAddresses)){
//            address = spAddresses.get(0).getAddress();
//        }
//        GetOrganisationMetaDataForInvoices response = new GetOrganisationMetaDataForInvoices();
//        response.setAddress(address);
//        response.setGstNo(serviceProviderDTO.getGstNo());
//        response.setPanNo(serviceProviderDTO.getPanNo());
//        response.setStateName(serviceProviderDTO.getStateName());
//        response.setAccountNumber(serviceProviderDTO.getAccountNumber());
//        response.setBankName(serviceProviderDTO.getBankName());
//        response.setIfscCode(serviceProviderDTO.getIfscCode());
//        response.setBranch(serviceProviderDTO.getBranch());
//        return response;
//    }
//
//    public Individual getIndividualFromSocietyCommitteeList(SocietyCommitteeMemberInformation societyIndividual
//            , UUID loggedInUserId,UUID loggedInSocietyMemberId) {
//        IndividualMetaDataDTO metadata = IndividualMetaDataDTO.builder()
//                .designation(societyIndividual.getDesignation())
//                .fromDate(societyIndividual.getFromDate())
//                .toDate(societyIndividual.getToDate())
//                .alternateNumber(societyIndividual.getAlternateNumber())
//                .loggedInSocietyMemberIndividualId(loggedInSocietyMemberId)
//                .build();
//        Individual individual = new Individual();
//        individual.setId(societyIndividual.getId());
//        setIndividualFirstNameAndLastName(individual, societyIndividual.getName());
//        individual.setMetaData(metadata);
//        individual.setMobileNumber(societyIndividual.getContactNumber());
//        individual.setEmail(societyIndividual.getEmail());
//        individual.setCreatedBy(loggedInUserId);
//        individual.setIsActive(Boolean.TRUE);
//        individual.setCreatedOn(DateUtils.currentTimeIST());
//        individual.setUpdatedOn(DateUtils.currentTimeIST());
//        return individual;
//    }
//
//    public IndividualRole getIndividualRole(UUID individualId, UUID orgId, UUID roleId, UUID loggedInUserId) {
//        UUID individualRoleId = UUID.randomUUID();
//        IndividualRole individualRole = new IndividualRole();
//        individualRole.setId(individualRoleId);
//        individualRole.setIndividualId(individualId);
//        individualRole.setRoleId(roleId);
//        individualRole.setOrgId(orgId);
//        individualRole.setCreatedBy(loggedInUserId);
//        individualRole.setCreatedOn(DateUtils.currentTimeIST());
//        individualRole.setUpdatedOn(DateUtils.currentTimeIST());
//        return individualRole;
//    }
//
//    public IndividualPermission getIndividualPermission(UUID individualId, UUID orgId, List<RoleNode> permissions
//            , UUID loggedInUserId) {
//        UUID individualPermissionId = UUID.randomUUID();
//        IndividualPermission individualPermission = new IndividualPermission();
//        individualPermission.setId(individualPermissionId);
//        individualPermission.setIndividualId(individualId);
//        individualPermission.setPermissions(permissions);
//        individualPermission.setCreatedBy(loggedInUserId);
//        individualPermission.setCreatedOn(DateUtils.currentTimeIST());
//        individualPermission.setOrgId(orgId);
//        return individualPermission;
//    }
//
//    public void setIndividualFirstNameAndLastName(Individual individual, String individualName) {
//        String[] nameParts = individualName.split("\\s+");
//
//        if (nameParts.length >= 2) {
//            // Set first word as FirstName
//            individual.setFirstName(nameParts[0]);
//
//            // Set the rest of the words as LastName
//            StringBuilder lastNameBuilder = new StringBuilder();
//            for (int i = 1; i < nameParts.length; i++) {
//                lastNameBuilder.append(nameParts[i]);
//                if (i < nameParts.length - 1) {
//                    lastNameBuilder.append(" ");
//                }
//            }
//            individual.setLastName(lastNameBuilder.toString());
//        } else if (nameParts.length == 1) {
//            // If only one word is present, set it as FirstName
//            individual.setFirstName(nameParts[0]);
//            individual.setLastName(""); // Set an empty string for LastName
//        } else {
//            // Handle case where no name is provided
//            throw new IllegalArgumentException("Name Length Can be Zero");
//        }
//    }
//
//    public Organisation mapIndividualCHSShortFormDTOToOrganisation(IndividualShortFormDTO individualCHSShortFormDTO,
//                                                                   UserPrincipal userPrincipal, Individual individual,
//                                                                   ReadinessDTO readinessDTO) {
//        ChsProfileDTO chsProfileDTO=new ChsProfileDTO();
//
//        String firstName = individualCHSShortFormDTO.getFirstName();
//        String lastName = individualCHSShortFormDTO.getLastName();
//
//        String fullName = ((firstName != null ? firstName.trim() : "") + " " +
//                (lastName != null ? lastName.trim() : "")).trim();
//        chsProfileDTO.setSystemCode(individual.getMetaData().getSystemCode());
//        chsProfileDTO.setLoginEmail(individualCHSShortFormDTO.getEmail());
//        if(individualCHSShortFormDTO instanceof  IndividualCHSShortFormDTO)
//        {
//            IndividualCHSShortFormDTO chsShortFormDTO=(IndividualCHSShortFormDTO)individualCHSShortFormDTO;
//            chsProfileDTO.setGoogleMapLocation(chsShortFormDTO.getGoogleLocation());
//        }
//
//        chsProfileDTO.setName(individualCHSShortFormDTO.getOrgName());
//        chsProfileDTO.setMobileNumber(individualCHSShortFormDTO.getMobileNumber());
//
//        chsProfileDTO.setDesignation(individualCHSShortFormDTO.getDesignation());
//        chsProfileDTO.setOtherDesignation(individualCHSShortFormDTO.getOtherDesignation());
//
//        chsProfileDTO.setSocietyMemberName(fullName);
//        chsProfileDTO.setSocietyMemberDesignation(individualCHSShortFormDTO.getDesignation());
//        chsProfileDTO.setSocietyMemberContactNumber(individualCHSShortFormDTO.getMobileNumber());
//        chsProfileDTO.setLoginEmail(individualCHSShortFormDTO.getEmail());
//
//
//        chsProfileDTO.setCreatedOn(DateUtils.currentTimeIST());
//        chsProfileDTO.setCreatedBy(userPrincipal.getId());
//        chsProfileDTO.setUpdatedBy(userPrincipal.getId());
//        chsProfileDTO.setUpdatedOn(DateUtils.currentTimeIST());
//
//        chsProfileDTO.setAssignedTo(userPrincipal.getId());
//
//        // TODO we need to handle this user id when we add functionality of multiple individuals login within same org
//        chsProfileDTO.setUserId(individual.getId());
//
//
//        JSONB chsMetaJson = null;
//        try {
//            chsMetaJson = JSONB.valueOf(objectMapper.writeValueAsString(chsProfileDTO));
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//
//        JSONB chsReadiness = null;
//        try {
//            chsReadiness = JSONB.valueOf(objectMapper.writeValueAsString(readinessDTO));
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//        Organisation organisation=new Organisation();
//        organisation.setId(UUID.randomUUID());
//        organisation.setName(individualCHSShortFormDTO.getOrgName());
//        organisation.setMetaData(chsMetaJson);
//        organisation.setContextualData(chsReadiness);
//        organisation.setCategory(DefaultOrganisationCategoryEnums.SOCIETY.name());
//        organisation.setUpdatedBy(userPrincipal.getId());
//        organisation.setCreatedBy(userPrincipal.getId());
//        organisation.setUpdatedOn(DateUtils.currentTimeIST());
//        organisation.setCreatedOn(DateUtils.currentTimeIST());
//
//        return organisation;
//    }
//
//    public Organisation mapIndividualSPShortFormDTOToOrganisation(IndividualShortFormDTO individualShortFormDTO,
//                                                                  UserPrincipal userPrincipal, Individual individual,
//                                                                  JSONB microSiteJson)
//            throws JsonProcessingException {
//        SpProfileDTO spProfileDTO=new SpProfileDTO();
//
//        String firstName = individualShortFormDTO.getFirstName();
//        String lastName = individualShortFormDTO.getLastName();
//
//        String fullName = ((firstName != null ? firstName.trim() : "") + " " +
//                (lastName != null ? lastName.trim() : "")).trim();
//
//        spProfileDTO.setEmail(individualShortFormDTO.getEmail());
//        spProfileDTO.setIndividualName(fullName.isEmpty() ? null : fullName);
//        spProfileDTO.setCompanyName(individualShortFormDTO.getOrgName());
//        spProfileDTO.setMobileNumber(individualShortFormDTO.getMobileNumber());
//        if (individualShortFormDTO instanceof IndividualSPShortFormDTO) {
//            IndividualSPShortFormDTO spDTO = (IndividualSPShortFormDTO) individualShortFormDTO;
//            spProfileDTO.setServicesProvided(spDTO.getServicesProvided());
//
//        }
//
//        spProfileDTO.setDesignation(individualShortFormDTO.getDesignation());
//        spProfileDTO.setOtherDesignation(individualShortFormDTO.getOtherDesignation());
//
//        spProfileDTO.setSystemCode(CommonOperations.generateSystemCode(SystemCodes.SP));
//        // TODO we need to handle this user id when we add functionality of multiple individuals login within same org
//        spProfileDTO.setUserId(individual.getId());
//        spProfileDTO.setAssignedTo(userPrincipal.getId());
//        spProfileDTO.setCreatedBy(userPrincipal.getId());
//        spProfileDTO.setUpdatedBy(userPrincipal.getId());
//        spProfileDTO.setCreatedOn(DateUtils.currentTimeIST());
//        spProfileDTO.setUpdatedOn(DateUtils.currentTimeIST());
//
//        JSONB spMetaDataJson = null;
//        try {
//            spMetaDataJson = JSONB.valueOf(objectMapper.writeValueAsString(spProfileDTO));
//        } catch (JsonProcessingException e) {
//            throw new RuntimeException(e);
//        }
//
//        Organisation organisation=new Organisation();
//        organisation.setId(UUID.randomUUID());
//        organisation.setName(individualShortFormDTO.getOrgName());
//        organisation.setMetaData(spMetaDataJson);
//        organisation.setContextualData(microSiteJson);
//        organisation.setCategory(DefaultOrganisationCategoryEnums.SERVICE_PROVIDER.name());
//        organisation.setUpdatedBy(userPrincipal.getId());
//        organisation.setCreatedBy(userPrincipal.getId());
//        organisation.setUpdatedOn(DateUtils.currentTimeIST());
//        organisation.setCreatedOn(DateUtils.currentTimeIST());
//
//        return organisation;
//    }
//
//
//    public ReadinessDTO createContextualDTO(IndividualShortFormDTO individualCHSShortFormDTO)
//    {
//        ReadinessDTO readinessDTO=new ReadinessDTO();
//
//        ReadinessReport readinessReport=new ReadinessReport();
//
//        String firstName = individualCHSShortFormDTO.getFirstName();
//        String lastName = individualCHSShortFormDTO.getLastName();
//
//        String fullName = ((firstName != null ? firstName.trim() : "") + " " +
//                (lastName != null ? lastName.trim() : "")).trim();
//
//        readinessReport.setRole(DefaultOrganisationCategoryEnums.SOCIETY.name());
//        ReadinessReport.NeedAssessment needAssessment=new ReadinessReport.NeedAssessment();
//        needAssessment.setName(fullName);
//        needAssessment.setSocietyName(individualCHSShortFormDTO.getOrgName());
//        readinessReport.setNeedAssessment(needAssessment);
//
//        ReadinessReport.FinancialClosure financialClosure=new ReadinessReport.FinancialClosure();
//        financialClosure.setEmailId(individualCHSShortFormDTO.getEmail());
//        financialClosure.setContactNumber(individualCHSShortFormDTO.getMobileNumber());
//        financialClosure.setHaveSecuredFinancialClosure(null);
//        readinessReport.setFinancialClosure(financialClosure);
//
//        readinessDTO.setReadinessReport(readinessReport);
//        return readinessDTO;
//    }
//    public void setContextualData(UUID loggedInIndividualId, Organisation organisation, SocietyReadinessReport societyReadinessReport) throws JsonProcessingException {
//        JSONB contextualData = organisation.getContextualData();
//        if(contextualData == null){
//            contextualData =
//                    JSONB.valueOf(objectMapper.writeValueAsString(
//                            SocietyContextualData.builder().userId(loggedInIndividualId)
//                                    .readinessReport(societyReadinessReport)
//                                    .build()));
//            organisation.setContextualData(contextualData);
//            return;
//        }
//        SocietyContextualData societyContextualData
//                = objectMapper.readValue(contextualData.toString(), SocietyContextualData.class);
//        societyContextualData.setUserId(loggedInIndividualId);
//        societyContextualData.setReadinessReport(societyReadinessReport);
//        organisation.setContextualData(JSONB.valueOf(objectMapper.writeValueAsString(societyContextualData)));
//   }
//
//    public void mapLoggedInIDtoUserId(ChsProfileDTO chsProfileDTO, UUID loggedInSocietyMemberIndividualId) {
//        chsProfileDTO.setUserId(loggedInSocietyMemberIndividualId);
//    }
//
//    public void mapLoggedInIdToUserIdToServiceProfileDTO(GetSpProfileDTOResponse getSpProfileDTOResponse
//            , UUID loggedInSocietyMemberIndividualId) {
//        getSpProfileDTOResponse.setUserId(loggedInSocietyMemberIndividualId);
//    }
//
//    public Individual getIndividualFromListOfServiceProviders(ServiceProviderIndividuals serviceProvider
//            , UUID loggedInUserId, UUID loggedInSocietyMemberId) {
//        IndividualMetaDataDTO metadata = IndividualMetaDataDTO.builder()
//                .otherDesignation(serviceProvider.getDesignation())
//                .fromDate(serviceProvider.getFromDate())
//                .toDate(serviceProvider.getToDate())
//                .alternateNumber(serviceProvider.getAlternateMobileNumber())
//                .loggedInSocietyMemberIndividualId(loggedInSocietyMemberId)
//                .build();
//        Individual individual = new Individual();
//        individual.setId(serviceProvider.getId());
//        setIndividualFirstNameAndLastName(individual, serviceProvider.getName());
//        individual.setMetaData(metadata);
//        individual.setMobileNumber(serviceProvider.getMobileNumber());
//        individual.setEmail(serviceProvider.getEmail());
//        individual.setCreatedBy(loggedInUserId);
//        individual.setIsActive(Boolean.TRUE);
//        individual.setCreatedOn(DateUtils.currentTimeIST());
//        individual.setUpdatedOn(DateUtils.currentTimeIST());
//        return individual;
//    }
//
//
//    public participantDetailsDTO getParticipantDetails(UUID orgId, DefaultOrganisationCategoryEnums type){
//        return participantDetailsDTO.builder()
//                .organisationId(orgId)
//                .individualId(null)
//                .type(type)
//                .build();
//    }
//
//    public List<CompanyOrAwardDocumentDTO> getListOfDocumentsDTO(List<DocumentRepo> documentRepoList, UUID categoryId
//            , UUID subCategoryId, UUID orgId, UUID loggedInUserId){
//        return documentRepoList.stream()
//                .filter(Objects::nonNull)
//                .map(documentRepo -> {
//                    CompanyOrAwardDocumentDTO documentDTOEntity = new CompanyOrAwardDocumentDTO();
//                        documentDTOEntity.setId(documentRepo.getId());
//                        documentDTOEntity.setLocation(documentRepo.getPath());
//                        documentDTOEntity.setDocumentCategory(categoryId);
//                        documentDTOEntity.setDocumentSubCategory(subCategoryId);
//                        documentDTOEntity.setUserId(orgId);
//                        documentDTOEntity.setCreatedBy(loggedInUserId);
//                        return documentDTOEntity;
//                }).collect(Collectors.toList());
//    }
//
//
//    public MicroSiteDTO getOrgContextualData(Organisation organisationEntity) throws JsonProcessingException {
//
//        MicroSiteDTO microSiteDTO = new MicroSiteDTO();
//
//        if (organisationEntity.getContextualData() != null
//                && !Objects.equals(organisationEntity.getContextualData().toString(), "null")) {
//            microSiteDTO = objectMapper.readValue(organisationEntity.getContextualData().toString(), MicroSiteDTO.class);
//        }
//        return microSiteDTO;
//    }
//
//    public String mapAreaOfExpertise(AreaOfExpertiseDTO areaOfExpertiseDTO) {
//        JSONObject jsonObj = new JSONObject();
//        jsonObj.put("healthCare", areaOfExpertiseDTO.getHealthCare());
//        jsonObj.put("townShip", areaOfExpertiseDTO.getTownShip());
//        jsonObj.put("commercial", areaOfExpertiseDTO.getCommercial());
//        jsonObj.put("hospitality", areaOfExpertiseDTO.getHospitality());
//        jsonObj.put("education", areaOfExpertiseDTO.getEducation());
//        jsonObj.put("leisure", areaOfExpertiseDTO.getLeisure());
//        jsonObj.put("other", areaOfExpertiseDTO.getOther());
//        return jsonObj.toString();
//    }
//
//    public void mapNotNullContextualData(SocietyContextualData societyContextualData, CHSProfileDTORequest chsProfileDTO){
//        SocietyReadinessReport readinessReport = societyContextualData.getReadinessReport();
//        if(readinessReport == null){
//            log.info("No readiness to update");
//            return;
//        }
//        if(chsProfileDTO.getName() != null && readinessReport.getNeedAssessment() != null){
//            readinessReport.getNeedAssessment().setSocietyName(chsProfileDTO.getName());
//        }
//        if(chsProfileDTO.getSocietyMemberName() != null && readinessReport.getNeedAssessment() != null){
//            readinessReport.getNeedAssessment().setName(chsProfileDTO.getSocietyMemberName());
//        }
//        if(chsProfileDTO.getSocietyMemberContactNumber() != null && readinessReport.getFinancialClosure() != null){
//            readinessReport.getFinancialClosure().setContactNumber(chsProfileDTO.getSocietyMemberContactNumber());
//        }
//    }
//
//
//    public List<Individual> mapFromJsonToIndividuals(List<Organisation> organisationList) {
//        List<Individual> individualsList = new ArrayList<>();
//        Set<String> usedEmails = new HashSet<>();
//
//        for (Organisation organisation : organisationList) {
//            JSONB exisitingJsonb = organisation.getMetaData();
//            SpProfileDTO spProfile;
//            try {
//                spProfile = objectMapper.readValue(exisitingJsonb.data(), SpProfileDTO.class);
//            } catch (JsonProcessingException e) {
//                throw new RuntimeException(e);
//            }
//
//            if (spProfile.getServiceProviderList() != null) {
//                for (ServiceProviderList serviceProvider : spProfile.getServiceProviderList()) {
//                    String originalEmail = serviceProvider.getEmail();
//                    String finalEmail = originalEmail;
//
//                    boolean emailExistsInDB = individualRepository.getByEmail(originalEmail) != null;
//                    boolean emailAlreadyUsedInBatch = usedEmails.contains(originalEmail);
//
//                    if (emailExistsInDB || emailAlreadyUsedInBatch) {
//                        finalEmail = generateTempCompanyEmail();
//                    }
//
//                    usedEmails.add(finalEmail);
//
//                    Individual individual = mapServiceProviderToIndividual(
//                            serviceProvider,
//                            spProfile.getUserId(),
//                            finalEmail,spProfile
//                    );
//
//                    individualsList.add(individual);
//                }
//            }
//        }
//        return individualsList;
//    }
//
//    private Individual mapServiceProviderToIndividual(ServiceProviderList serviceProvider, UUID loggedInUserId,String email,SpProfileDTO spProfileDTO) {
//        Individual individual = new Individual();
//        individual.setId(serviceProvider.getId());
//        individual.setFirstName(serviceProvider.getName());
//        individual.setEmail(email);
//        individual.setMobileNumber(serviceProvider.getMobileNumber());
//        IndividualMetaDataDTO individualMetaDataDTO = new IndividualMetaDataDTO();
//        individualMetaDataDTO.setToDate(serviceProvider.getToDate());
//        individualMetaDataDTO.setFromDate(serviceProvider.getFromDate());
//        individualMetaDataDTO.setAlternateNumber(serviceProvider.getAlternateMobileNumber());
//        individualMetaDataDTO.setDesignation(serviceProvider.getDesignation());
//        individualMetaDataDTO.setLoggedInSocietyMemberIndividualId(loggedInUserId);
//        individual.setMetaData(individualMetaDataDTO);
//        individual.setCreatedOn(DateUtils.currentTimeIST());
//        individual.setUpdatedOn(DateUtils.currentTimeIST());
//        individual.setCreatedBy(spProfileDTO.getCreatedBy());
//        individual.setUpdatedBy(spProfileDTO.getUpdatedBy());
//        individual.setIsActive(Boolean.TRUE);
//        return individual;
//    }
//
//
//    private String generateTempCompanyEmail() {
//        return "service.provider.temp." + UUID.randomUUID().toString().substring(0, 8) + "@houzer.co.in";
//    }
//
//    public List<IndividualVerificationAudit> mapIndividualToIndividualList(List<Individual> individualsList) {
//        List<IndividualVerificationAudit> individualVerificationAuditList=new ArrayList<>();
//        for(Individual individual:individualsList)
//        {
//            IndividualVerificationAudit individualVerificationAudit=mapIndividualToIndividualVerification(individual);
//            individualVerificationAuditList.add(individualVerificationAudit);
//        }
//        return individualVerificationAuditList;
//    }
//
//    private IndividualVerificationAudit mapIndividualToIndividualVerification(Individual individual) {
//        IndividualVerificationAudit individualVerificationAudit=new IndividualVerificationAudit();
//        individualVerificationAudit.setId(UUID.randomUUID());
//        individualVerificationAudit.setContactType(ContactType.EMAIL.name());
//        individualVerificationAudit.setContactValue(individual.getEmail());
//        individualVerificationAudit.setIsActive(Boolean.TRUE);
//        individualVerificationAudit.setVerificationStatus(VerificationStatusEnum.PENDING.name());
//        individualVerificationAudit.setRole(DefaultOrganisationCategoryEnums.SERVICE_PROVIDER.name());
//        individualVerificationAudit.setCreatedBy(individual.getCreatedBy());
//        individualVerificationAudit.setUpdatedBy(individual.getUpdatedBy());
//        individualVerificationAudit.setCreatedOn(individual.getCreatedOn());
//        individualVerificationAudit.setUpdatedOn(individual.getUpdatedOn());
//        return individualVerificationAudit;
//    }
//
//    public List<IndividualPasswordResetAudit> mapIndividualToIndividualReset(List<Individual> individualsList) {
//        List<IndividualPasswordResetAudit> individualPasswordResetAuditList=new ArrayList<>();
//        for(Individual individual:individualsList)
//        {
//            IndividualPasswordResetAudit individualPasswordResetAudit=mapIndividualToIndividualPassword(individual);
//            individualPasswordResetAuditList.add(individualPasswordResetAudit);
//        }
//        return individualPasswordResetAuditList;
//    }
//
//    private IndividualPasswordResetAudit mapIndividualToIndividualPassword(Individual individual) {
//        IndividualPasswordResetAudit individualPasswordResetAudit=new IndividualPasswordResetAudit();
//        individualPasswordResetAudit.setId(UUID.randomUUID());
//        individualPasswordResetAudit.setIndividualId(individual.getId());
//        individualPasswordResetAudit.setEmail(individual.getEmail());
//        individualPasswordResetAudit.setResetStatus(ResetStatus.INACTIVE.name());
//        individualPasswordResetAudit.setIsActive(Boolean.TRUE);
//        individualPasswordResetAudit.setCreatedBy(individual.getCreatedBy());
//        individualPasswordResetAudit.setUpdatedBy(individual.getUpdatedBy());
//        return individualPasswordResetAudit;
//    }

}
