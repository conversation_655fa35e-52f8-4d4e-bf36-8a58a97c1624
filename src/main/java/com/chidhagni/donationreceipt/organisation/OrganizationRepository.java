package com.chidhagni.donationreceipt.organisation;

//import com.chidhagni.houzer.db.jooq.tables.daos.OrganisationDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualRole;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Organisation;
//import com.chidhagni.houzer.db.jooq.tables.pojos.UserServiceProfile;
//import com.chidhagni.houzer.services.userserviceprofile.UserServicesDataGroupsEnum;
import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualVerificationAuditDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.OrganisationDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;

import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.organisation.dto.response.GetOrganisationDropDown;
import com.chidhagni.donationreceipt.organisation.dto.response.OrganisationNameDTO;

import com.chidhagni.donationreceipt.organisation.dto.request.OrganisationPaginationRequest;
import com.chidhagni.donationreceipt.organisation.dto.request.TenantsDTO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;


import java.time.LocalDateTime;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;
import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.when;
//import static com.chidhagni.houzer.db.jooq.tables.Individual.INDIVIDUAL;
//import static com.chidhagni.houzer.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
//import static com.chidhagni.houzer.db.jooq.tables.Organisation.ORGANISATION;
//import static com.chidhagni.houzer.db.jooq.tables.OrganisationLeadAssignment.ORGANISATION_LEAD_ASSIGNMENT;
//import static com.chidhagni.houzer.db.jooq.tables.UserServiceProfile.USER_SERVICE_PROFILE;


@Repository
@RequiredArgsConstructor
@Slf4j
public class OrganizationRepository {
    //    private final ObjectMapper objectMapper;
//    private final IndividualRoleRepository individualRoleRepository;
    private final OrganisationDao organisationDao;
    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;

    private final IndividualVerificationAuditDao individualVerificationAuditDao;
    private final IndividualRoleRepository individualRoleRepository;

    //    @Value("${houzer.employee.company.name}")
//    private String organisationName;
//    @Value("${chidhagni.employee.company.name}")
//    private String devOrganisationName;
//    @Value("${sp.address.primary}")
//    private String spPrimaryAddressTypeId;
//    private final OrganisationMapper organisationMapper;
//
//
//    private final DSLContext dsl;
//
    public Optional<String> getNameOrCompanyNameById(UUID id) {
        try {
            return organisationDao.ctx().select(ORGANISATION.NAME
                    )
                    .from(ORGANISATION)
                    .where(ORGANISATION.ID.eq(id))
                    .fetchOptional("name", String.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching organization name by organisation id ", ex);
        }
    }


    public Organisation getById(UUID id) {
        Organisation organisation = null;
        try {
            organisation = organisationDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching Organisation by it's id", ex);
        }
        return organisation;
    }
//
//    /**
//     * Queries the database to fetch organizations by category.
//     *
//     * <p>This method retrieves organizations from the database based on their category:
//     * <ul>
//     *   <li>For **Society**, returns ID and Name.</li>
//     *   <li>For **Service Provider**, returns ID and metadata.</li>
//     * </ul>
//     * </p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Checks if the provided category is `SOCIETY` or `SERVICE_PROVIDER`.</li>
//     *   <li>Executes a database query to fetch organizations based on the category.</li>
//     *   <li>Maps the query results to `OrganizationDTO`.</li>
//     * </ul>
//     *
//     * <p><b>Notes:</b></p>
//     * <ul>
//     *   <li>Throws an exception if an unsupported category is provided.</li>
//     * </ul>
//     *
//     * @param category The category of organizations to fetch.
//     * @return A list of `OrganizationDTO` objects.
//     * @throws IllegalArgumentException If the category is unsupported.
//     */
//    public List<OrganizationDTO> getOrganizationsByCategory(DefaultOrganisationCategoryEnums category) {
//        if (category == SOCIETY) {
//            return organisationDao.ctx().select(ORGANISATION.ID, ORGANISATION.NAME)
//                    .from(ORGANISATION)
//                    .where(ORGANISATION.CATEGORY.eq(category.name()))
//                    .orderBy(ORGANISATION.NAME)
//                    .fetch()
//                    .map(organisations -> OrganizationDTO.builder()
//                            .id(organisations.get(ORGANISATION.ID))
//                            .name(organisations.get(ORGANISATION.NAME))
//                            .build()
//                    );
//        } else if (category == DefaultOrganisationCategoryEnums.SERVICE_PROVIDER) {
//            return organisationDao.ctx().select(ORGANISATION.ID, ORGANISATION.META_DATA)
//                    .from(ORGANISATION)
//                    .where(ORGANISATION.CATEGORY.eq(category.name()))
//                    .fetch()
//                    .map(organisations -> OrganizationDTO.builder()
//                            .id(organisations.get(ORGANISATION.ID))
//                            .metaData(organisations.get(ORGANISATION.META_DATA).toString())
//                            .build()
//                    );
//        } else {
//            throw new IllegalArgumentException("Unsupported category: " + category);
//        }
//    }
//
//
//    public List<UUID> getOrganizationsWithServiceType(UUID serviceTypeId) {
//        try {
//            return organisationDao.ctx().select(ORGANISATION.ID)
//                    .from(ORGANISATION)
//                    .where(
//                            field("jsonb_exists(meta_data, 'servicesProvided')").eq(true)
//                                    .and(field("meta_data->'servicesProvided'").contains(serviceTypeId.toString()))
//                    )
//                    .fetchInto(UUID.class);
//
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching organisations with specific services provided");
//        }
//    }
//
//    /**
//     * Queries the database to fetch service providers assigned to broadcast service requisitions.
//     *
//     * <p>This method retrieves service provider details such as name, email, and mobile number
//     * by joining multiple tables.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Fetches service providers based on individual IDs.</li>
//     *   <li>Applies search filters for name, email, and mobile number.</li>
//     *   <li>Joins `ORGANISATION` and `INDIVIDUAL_ROLE` tables to get organization details.</li>
//     *   <li>Fetches metadata fields like company name.</li>
//     *   <li>Applies pagination to limit the number of results.</li>
//     * </ul>
//     *
//     * @param individualIds The list of individual IDs to filter.
//     * @param searchCondition The search condition for filtering results.
//     * @param broadcastPaginationRequest The pagination request parameters.
//     * @return A list of `BroadcastOrganisationsResponse` objects.
//     */
//    public List<BroadcastOrganisationsResponse> getBroadcastingServiceProviders(List<UUID> individualIds
//            , Condition searchCondition, BroadcastPaginationRequest broadcastPaginationRequest) {
//        try {
//            Integer pageNo = (broadcastPaginationRequest.getPage() - 1) * broadcastPaginationRequest.getPageSize();
//            return organisationDao.ctx().select(
//                            INDIVIDUAL.ID.as("id"),
//                            INDIVIDUAL.FIRST_NAME.as("name"),
//                            INDIVIDUAL.EMAIL.as("email"),
//                            INDIVIDUAL.MOBILE_NUMBER.as("mobileNumber"),
//                            ORGANISATION.ID.as("organisationId"),
//                            field("ORGANISATION.meta_data->>'companyName'", String.class).as("companyName")
//                    ).from(INDIVIDUAL)
//                    .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(INDIVIDUAL.ID))
//                    .leftJoin(ORGANISATION).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
//                    .leftJoin(ORGANISATION_LEAD_ASSIGNMENT).on(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID
//                            .eq(ORGANISATION.ID))
//                    .where(INDIVIDUAL.ID.in(individualIds).and(searchCondition))
//                    .limit(broadcastPaginationRequest.getPageSize())
//                    .offset(pageNo)
//                    .fetchInto(BroadcastOrganisationsResponse.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching individuals and company name with their id's");
//        }
//
//    }
//
//    /**
//     * Queries the database to fetch count of service providers assigned to broadcast service requisitions.
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Fetches service providers based on individual IDs.</li>
//     *   <li>Applies search filters for name, email, and mobile number.</li>
//     *   <li>Joins `ORGANISATION` and `INDIVIDUAL_ROLE` tables to get organization details.</li>
//     *   <li>Fetches metadata fields like company name.</li>
//     * </ul>
//     *
//     * @param individualIds The list of individual IDs to filter.
//     * @param searchCondition The search condition for filtering results.
//     * @return Integer denoting no. of `BroadcastOrganisationsResponse` objects.
//     */
//    public Integer getBroadcastServiceRequisitionsCount(List<UUID> individualIds, Condition searchCondition) {
//        try {
//            return organisationDao.ctx().selectCount()
//                    .from(INDIVIDUAL)
//                    .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(INDIVIDUAL.ID))
//                    .leftJoin(ORGANISATION).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
//                    .leftJoin(ORGANISATION_LEAD_ASSIGNMENT).on(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID
//                            .eq(ORGANISATION.ID))
//                    .where(INDIVIDUAL.ID.in(individualIds).and(searchCondition))
//                    .fetchOne(0, Integer.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching individuals and company name records count with their id's");
//        }
//    }
//
//    /**
//     * Queries the database to fetch organization metadata by ID.
//     *
//     * <p>This method retrieves metadata fields from the `meta_data` JSONB column in the
//     * `ORGANISATION` table and maps them to a DTO.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Executes a database query to extract metadata fields from the `meta_data` JSONB column.</li>
//     *   <li>Maps the extracted fields to an `OrganisationMetaDataDTO` object.</li>
//     * </ul>
//     *
//     * <p><b>Notes:</b></p>
//     * <ul>
//     *   <li>Returns an empty DTO if no metadata is found.</li>
//     *   <li>Throws an `InternalServerError` if an error occurs while fetching metadata.</li>
//     * </ul>
//     *
//     * @param id The unique identifier of the organization.
//     * @return An `OrganisationMetaDataDTO` object containing extracted metadata.
//     * @throws InternalServerError If any error occurs during database access.
//     */
//    public OrganisationMetaDataDTO fetchOrganisationMetaDataById(UUID id) {
//        OrganisationMetaDataDTO organisationMetaDataDTO = new OrganisationMetaDataDTO();
//
//        try {
//            Record dbResults = dsl.select(
//                            DSL.field("name").as("societyName") ,
//                            DSL.field("meta_data ->> 'zone'", String.class).as("zone"),
//                            DSL.field("meta_data->> 'address'", String.class).as("address"),
//                            DSL.field("meta_data ->> 'locationId'", String.class).as("locationId"),
//                            DSL.field("meta_data ->> 'location'", String.class).as("location"),
//                            DSL.field("meta_data ->> 'societyAddress'", String.class).as("societyAddress"),
//                            DSL.field("meta_data ->> 'ward'", String.class).as("ward"),
//                            DSL.field("meta_data ->> 'type'", String.class).as("type"),
//                            DSL.field("meta_data ->> 'roadWidth'", String.class).as("roadWidth"),
//                            DSL.field("meta_data ->> 'grossPlotArea'", String.class).as("plotArea"),
//                            DSL.field("meta_data ->> 'authority'", String.class).as("authority"),
//                            DSL.field(
//                                    "(SELECT elem->>'locationId' FROM jsonb_array_elements" +
//                                            "(meta_data -> 'spAddresses') AS elem " +
//                                            "WHERE elem->>'addressType' = {0} LIMIT 1)",
//                                    String.class, spPrimaryAddressTypeId
//                            ).as("spPrimaryLocationId")
//                    )
//                    .from(ORGANISATION)
//                    .where(ORGANISATION.ID.eq(id))
//                    .fetchOne();
//
//            if (dbResults != null) {
//                organisationMetaDataDTO.setSocietyName(dbResults.get("societyName", String.class));
//                organisationMetaDataDTO.setZone(dbResults.get("zone", String.class));
//                organisationMetaDataDTO.setAddress(dbResults.get("address", String.class));
//                organisationMetaDataDTO.setLocationId(dbResults.get("locationId", String.class));
//                organisationMetaDataDTO.setLocation(dbResults.get("location", String.class));
//                organisationMetaDataDTO.setSocietyAddress(dbResults.get("ward", String.class));
//                organisationMetaDataDTO.setWard(dbResults.get("ward", String.class));
//                organisationMetaDataDTO.setType(dbResults.get("type", String.class));
//                organisationMetaDataDTO.setRoadWidth(dbResults.get("roadWidth", String.class));
//                organisationMetaDataDTO.setPlotArea(dbResults.get("plotArea", String.class));
//                organisationMetaDataDTO.setAuthority(dbResults.get("authority", String.class));
//                organisationMetaDataDTO.setSpPrimaryLocationId(dbResults.get("spPrimaryLocationId", String.class));
//            }
//
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching organisation meta data by its id", ex);
//        }
//        return organisationMetaDataDTO;
//    }
//
//
//    public DefaultOrganisationCategoryEnums getOrganisationCategoryForIndividual(UUID loggedInUserId) {
//        List<IndividualRole> individualRoleList = individualRoleRepository.getByIndivdiualId(loggedInUserId);
//        if (CollectionUtils.isEmpty(individualRoleList)) {
//            throw new IllegalArgumentException("IndividualRole doesn't exist ");
//        }
//
//        IndividualRole individualRole = individualRoleList.get(0);
//        UUID orgId = individualRole.getOrgId();
//        if (orgId == null) {
//            throw new IllegalArgumentException("No organisation present with fetched id");
//        }
//
//        Organisation organisation = organisationDao.fetchOneById(orgId);
//        String category = organisation.getCategory();
//
//        // Convert the category string to an enum value
//        try {
//            return DefaultOrganisationCategoryEnums.valueOf(category.toUpperCase());
//        } catch (IllegalArgumentException e) {
//            throw new IllegalArgumentException("Invalid organisation category: " + category, e);
//        }
//    }
//
//
    public Organisation fetchOrganizationByIndividualId(UUID individualId) {
        // Fetch individual roles associated with the given individualId
        List<IndividualRole> individualRole = individualRoleRepository.getByIndivdiualId(individualId);

        if (individualRole != null && !individualRole.isEmpty()) {
            // Extract the first orgId from the roles
            UUID orgId = individualRole.get(0).getOrgId();

            if (orgId != null) {
                // Fetch and return the Organization entity using the orgId
                return organisationDao.fetchOneById(orgId);
            }
        }
        return null; // Return null if no organization is found
    }

    /**
     * Queries the database to fetch organization names by category.
     *
     * <p>This method retrieves organizations filtered by the given category type,
     * excluding specific employee organizations.</p>
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Executes a database query to retrieve organization names and IDs.</li>
     *   <li>Filters records where `CATEGORY` matches the provided category.</li>
     *   <li>Excludes organizations where:
     *     <ul>
     *       <li>`CATEGORY = EMPLOYEE`</li>
     *       <li>`NAME = devOrganisationName`</li>
     *     </ul>
     *   </li>
     *   <li>Maps the result into a list of `OrganisationNameDTO` objects.</li>
     * </ul>
     *
     * <p><b>Notes:</b></p>
     * <ul>
     *   <li>Throws an `InternalServerError` if an exception occurs.</li>
     *   <li>Returns an empty list if no records match the filtering conditions.</li>
     * </ul>
     *
     * @param category The category type of organizations to fetch.
     * @return A list of `OrganisationNameDTO` objects.
     * @throws InternalServerError If an error occurs during database access.
     */
    public List<OrganisationNameDTO> fetchOrganizationNameByCategoryType(OrganisationEnums category) {
        try {
            return organisationDao.ctx()
                    .select(ORGANISATION.ID.as("id"), ORGANISATION.NAME.as("name"))
                    .from(ORGANISATION)
                    .where(ORGANISATION.CATEGORY.eq(category.name()))
                    .and(ORGANISATION.CATEGORY.ne(OrganisationEnums.SUPER_ADMIN.name()))
                    .fetchInto(OrganisationNameDTO.class);
        } catch (Exception e) {
            throw new InternalServerError("Exception occurred while fetching organisation names by category");
        }
    }
    //
//    /**
//     * Queries the database to fetch unique organization categories.
//     *
//     * <p>This method retrieves distinct organization categories by grouping them to avoid duplicates.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Executes a database query to fetch organization categories.</li>
//     *   <li>Groups results by `CATEGORY` to retrieve unique values.</li>
//     *   <li>Maps the result into a list of `OrganisationCategoryDTO` objects.</li>
//     * </ul>
//     *
//     * <p><b>Notes:</b></p>
//     * <ul>
//     *   <li>Throws an `InternalServerError` if an exception occurs.</li>
//     *   <li>Returns an empty list if no records match the filtering conditions.</li>
//     * </ul>
//     *
//     * @return A list of `OrganisationCategoryDTO` objects.
//     * @throws InternalServerError If an error occurs during database access.
//     */
//    public List<OrganisationCategoryDTO> fetchOrganisationCategories() {
//        try {
//            return organisationDao.ctx()
//                    .select(ORGANISATION.CATEGORY.as("category"))
//                    .from(ORGANISATION)
//                    .groupBy(ORGANISATION.CATEGORY)
//                    .fetchInto(OrganisationCategoryDTO.class);
//        } catch (Exception e) {
//            throw new InternalServerError("Exception occurred while fetching organisation categories");
//        }
//    }
//
    public boolean isOrganisationExist(UUID id) {
        try {
            return organisationDao.ctx().fetchExists(organisationDao.ctx().select(ORGANISATION.ID).from(ORGANISATION)
                    .where(ORGANISATION.ID.eq(id)));
        } catch (Exception ex) {
            throw new IllegalArgumentException("Exception occurred while checking the organisation exist by id");
        }
    }
    //
//    public UUID fetchDevOrganisationId() {
//        try {
//            return organisationDao.ctx().select(ORGANISATION.ID).from(ORGANISATION)
//                    .where(ORGANISATION.NAME.eq(devOrganisationName))
//                    .and(ORGANISATION.CATEGORY.eq(DefaultOrganisationCategoryEnums.EMPLOYEE.name()))
//                    .fetchOneInto(UUID.class);
//        } catch (Exception ex) {
//            throw new IllegalArgumentException(
//                    "Exception occurred while checking the development team company organisation id");
//        }
//    }
//
//    public UUID fetchClientOrganisationId() {
//        try {
//            return organisationDao.ctx().select(ORGANISATION.ID).from(ORGANISATION).where(ORGANISATION.NAME
//                            .eq(organisationName))
//                    .and(ORGANISATION.CATEGORY.eq(DefaultOrganisationCategoryEnums.EMPLOYEE.name()))
//                    .fetchOneInto(UUID.class);
//        } catch (Exception ex) {
//            throw new IllegalArgumentException("Exception occurred while fetching the client organisation details", ex);
//        }
//    }
//
//    /**
//     * Queries the database to fetch metadata for an organization.
//     *
//     * <p>This method retrieves metadata based on the organization’s category:
//     * <ul>
//     *   <li>For Society, retrieves `ChsProfileDTO`.</li>
//     *   <li>For Service Provider, retrieves `SpProfileDTO`.</li>
//     *   <li>For Employee, retrieves `EmployeeProfileDTO`.</li>
//     * </ul>
//     * </p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Executes a query to fetch the organization's category.</li>
//     *   <li>If no category is found, throws `IllegalArgumentException`.</li>
//     *   <li>Retrieves metadata based on the category:
//     *     <ul>
//     *       <li>If `SOCIETY`, fetches society metadata and maps it.</li>
//     *       <li>If `SERVICE_PROVIDER`, fetches service provider metadata and maps it.</li>
//     *       <li>If `EMPLOYEE`, fetches employee metadata and maps it.</li>
//     *     </ul>
//     *   </li>
//     *   <li>Returns the mapped metadata response.</li>
//     * </ul>
//     *
//     * <p><b>Where Conditions:</b></p>
//     * <ul>
//     *   <li>Filters the `ORGANISATION` table by `ID` to retrieve the category.</li>
//     * </ul>
//     *
//     * <p><b>Notes:</b></p>
//     * <ul>
//     *   <li>Throws `IllegalArgumentException` if the organization is not found.</li>
//     *   <li>Throws `InternalServerError` if an error occurs during data retrieval.</li>
//     * </ul>
//     *
//     * @param orgId The unique identifier of the organization.
//     * @return A `GetOrganisationMetaDataForInvoices` object containing metadata.
//     * @throws InternalServerError If an error occurs during database access.
//     */
//    public GetOrganisationMetaDataForInvoices getOrganisationMetaData(UUID orgId) {
//        try {
//            DefaultOrganisationCategoryEnums category = dsl.select(
//                            ORGANISATION.CATEGORY
//                    ).from(ORGANISATION)
//                    .where(ORGANISATION.ID.eq(orgId))
//                    .fetchOneInto(DefaultOrganisationCategoryEnums.class);
//
//            if (category == null) {
//                throw new IllegalArgumentException("Organisation not found for ID: " + orgId);
//            }
//
//            switch (category) {
//                case SOCIETY:
//                    ChsProfileDTO chsProfileDTO= organisationMapper.fetchSocietyMetaData(orgId);
//                    return organisationMapper.mapFromSocietyProfile(chsProfileDTO);
//                case SERVICE_PROVIDER:
//                   SpProfileDTO spProfileDTO= organisationMapper.fetchServiceProviderMetaData(orgId);
//                    return organisationMapper.mapFromServiceProvider(spProfileDTO);
//                case EMPLOYEE:
//                    EmployeeProfileDTO employeeProfileDTO= organisationMapper.fetchEmployeeMetaData(orgId);
//                    return organisationMapper.mapFromEmployeeProfile(employeeProfileDTO);
//                default:
//                    throw new IllegalArgumentException("Invalid category: " + category);
//            }
//        }
//        catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching organisation meta data", ex);
//        }
//    }
//
    public String getOrganisationName(UUID id)
    {
        return organisationDao.ctx().select(ORGANISATION.NAME).from(ORGANISATION)
                .where(ORGANISATION.ID.eq(id))
                .fetchOneInto(String.class);
    }
//
//    public Integer updateOrganisationMetaData(UUID orgId, ChsProfileDTO chsProfileDTO, UUID loggedInUserId){
//        try{
//            JSONB individualMetaDataOfSociety = JSONB.valueOf(objectMapper.writeValueAsString(chsProfileDTO));
//            return organisationDao.ctx().update(ORGANISATION)
//                    .set(ORGANISATION.NAME, chsProfileDTO.getName())
//                    .set(ORGANISATION.META_DATA, individualMetaDataOfSociety)
//                    .set(ORGANISATION.UPDATED_BY, loggedInUserId)
//                    .set(ORGANISATION.UPDATED_ON, DateUtils.currentTimeIST())
//                    .where(ORGANISATION.ID.eq(orgId))
//                    .execute();
//        }catch (Exception ex){
//            throw new InternalServerError("Exception occurred while updating organisation meta data", ex);
//        }
//    }
//
//    public String getSystemCode(UUID id)
//    {
//        return organisationDao.ctx().select(field("meta_data->>'systemCode'", String.class))
//                .from(ORGANISATION)
//                .where(ORGANISATION.ID.eq(id))
//                .fetchOneInto(String.class);
//    }
//
//    public Integer updateSpOrganisationMetaData(UUID orgId, SpProfileDTOV2 spProfileDTO, UUID loggedInUserId){
//        try{
//            JSONB organisationMetaDataOfServiceProvider = JSONB.valueOf(objectMapper.writeValueAsString(spProfileDTO));
//            return organisationDao.ctx().update(ORGANISATION)
//                    .set(ORGANISATION.NAME, spProfileDTO.getCompanyName())
//                    .set(ORGANISATION.META_DATA, organisationMetaDataOfServiceProvider)
//                    .set(ORGANISATION.UPDATED_BY, loggedInUserId)
//                    .set(ORGANISATION.UPDATED_ON, DateUtils.currentTimeIST())
//                    .where(ORGANISATION.ID.eq(orgId))
//                    .execute();
//        }catch (Exception ex){
//            throw new InternalServerError("Exception occurred while updating sp organisation meta data", ex);
//        }
//    }
    public void insertOrganisation(Organisation organisation){
        try{
            organisationDao.insert(organisation);
        }
        catch (Exception ex){
            throw new InternalServerError("Exception while inserting the organisation",ex);
        }
    }

    public void updateOrganisation(Organisation organisation) {
        try {
            organisationDao.update(organisation);
            log.info("Successfully updated the organisation");
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while updating organisation meta data", ex);
        }
    }


//    public LocalDateTime getOrganisationCreatedOn(UUID orgId){
//        try{
//            return organisationDao.ctx().select(ORGANISATION.CREATED_ON)
//                    .from(ORGANISATION)
//                    .where(ORGANISATION.ID.eq(orgId))
//                    .fetchOneInto(LocalDateTime.class);
//        }catch (Exception ex){
//            throw new InternalServerError("Exception occurred while fetching organisation created on date", ex);
//        }
//    }
//
//    public UserServiceProfile getSingleUserServiceProfile(UUID serviceNameId, UUID individualId) {
//        try{
//            UserServiceProfile userServiceProfile = organisationDao.ctx()
//                    .select(USER_SERVICE_PROFILE.fields()).from(USER_SERVICE_PROFILE).
//                    where(USER_SERVICE_PROFILE.SERVICE_NAME_ID.eq(serviceNameId)
//                      .and(USER_SERVICE_PROFILE.USER_SERVICES_DATA_GROUP.eq(UserServicesDataGroupsEnum.PROJECTS.name()))
//                      .and(USER_SERVICE_PROFILE.INDIVIDUAL_ID.eq(individualId))).fetchOneInto(UserServiceProfile.class);
//
//            if(userServiceProfile == null){
//            log.info("user service profile not founded with individual id, so searching from created by and updated by");
//                userServiceProfile = organisationDao.ctx()
//                        .select(USER_SERVICE_PROFILE.fields()).from(USER_SERVICE_PROFILE).
//                        where(USER_SERVICE_PROFILE.SERVICE_NAME_ID.eq(serviceNameId)
//                                .and(USER_SERVICE_PROFILE.USER_SERVICES_DATA_GROUP
//                                        .eq(UserServicesDataGroupsEnum.PROJECTS.name()))
//                                .and(USER_SERVICE_PROFILE.CREATED_BY.eq(individualId)))
//                        .fetchOneInto(UserServiceProfile.class);
//            }
//
//            if (userServiceProfile == null) {
//                throw new IllegalArgumentException("Not founded user service profile");
//            }
//            if (userServiceProfile.getMetadata() == null) {
//                userServiceProfile.setMetadata(JSONB.valueOf("[]"));
//            }
//            if (userServiceProfile.getMetadata().toString().equals("{}")) {
//                userServiceProfile.setMetadata(JSONB.valueOf("[]"));
//            }
//            return userServiceProfile;
//        }catch (Exception ex){
//            throw new InternalServerError("Exception occurred while fetching organisation created on date", ex);
//        }
//    }
//
//    public Integer updateOrganisationContextualData(UUID orgId, SocietyContextualData contextualData, UUID loggedInUserId){
//        try{
//            JSONB contextualOfSociety = JSONB.valueOf(objectMapper.writeValueAsString(contextualData));
//            return organisationDao.ctx().update(ORGANISATION)
//                    .set(ORGANISATION.CONTEXTUAL_DATA, contextualOfSociety)
//                    .set(ORGANISATION.UPDATED_BY, loggedInUserId)
//                    .set(ORGANISATION.UPDATED_ON, DateUtils.currentTimeIST())
//                    .where(ORGANISATION.ID.eq(orgId))
//                    .execute();
//        }catch (Exception ex){
//            throw new InternalServerError("Exception occurred while updating organisation meta data", ex);
//        }
//    }
//
//
//
//    public List<Organisation> getAllUsersRecords(Integer batchSize, Integer lastProcessedRow) {
//        return organisationDao.ctx().select().from(ORGANISATION).where(ORGANISATION.CATEGORY.eq("SERVICE_PROVIDER"))
//                .orderBy(ORGANISATION.UPDATED_ON.asc())
//                .limit(batchSize).offset(lastProcessedRow).fetchInto(Organisation.class);
//    }
//
//
//    public List<Organisation> getAllUsersInTimeIntervals(Integer timeInterval) {
//        LocalDateTime now = LocalDateTime.now();
//        LocalDateTime windowStartingTime = now.minusMinutes(timeInterval);
//        return organisationDao.ctx().select().from(ORGANISATION)
//                .where(ORGANISATION.UPDATED_ON.between(windowStartingTime, now))
//                .orderBy(ORGANISATION.UPDATED_ON.asc())
//                .fetchInto(Organisation.class);
//    }
//
//    public List<UUID> getListOfIndividualIdByOrganisationId(List<Organisation> organisations) {
//        try {
//            // Fetch the IDs of the organisations from the provided list
//            List<UUID> orgIds = organisations.stream()
//                    .map(Organisation::getId) // Assuming getId() returns UUID for Organisation
//                    .collect(Collectors.toList());
//
//            // Query the database to fetch individual IDs based on the organisation IDs
//            return organisationDao.ctx()
//                    .select(ORGANISATION.ID)   // Select the ID column
//                    .from(ORGANISATION)         // From the ORGANISATION table
//                    .where(ORGANISATION.ID.in(orgIds))  // Match the provided organisation IDs
//                    .fetch()                    // Fetch the result
//                    .into(UUID.class);         // Map the result into List<UUID>
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching individual id by organisation id", ex);
//        }
//    }


    public List<TenantsDTO> getAllTenants(Condition finalCondition, List<SortField<?>> sortFields,
                                          OrganisationPaginationRequest organisationPaginationRequest) {
        try {
            Individual createdBy = INDIVIDUAL.as("createdBy");
            Individual updatedBy = INDIVIDUAL.as("updatedBy");

            // Define fields using DSL.name to refer to aliased fields
            Field<UUID> createdById = createdBy.field(DSL.name("id"), UUID.class);
            Field<UUID> updatedById = updatedBy.field(DSL.name("id"), UUID.class);

            Field<String> createdByEmail = createdBy.field(DSL.name("email"), String.class);
            Field<String> updatedByEmail = updatedBy.field(DSL.name("email"), String.class);

            Integer offset = (organisationPaginationRequest.getPage() - 1) * organisationPaginationRequest.getPageSize();
            Integer pageSize = organisationPaginationRequest.getPageSize();

            // Main query for tenant data
            List<TenantsDTO> tenantsDTOList = organisationDao.ctx()
                    .select(
                            ORGANISATION.ID.as("orgId"),
                            ORGANISATION.NAME.as("trustName"),
                            field("ORGANISATION.meta_data ->> 'orgEmail'", String.class).as("orgEmail"),
                            INDIVIDUAL.NAME.as("tenantAdminName"),
                            INDIVIDUAL.EMAIL.as("tenantAdminEmail"),
                            INDIVIDUAL.MOBILE_NUMBER.as("tenantAdminMobileNumber"),
                            ORGANISATION.IS_ACTIVE.as("isActiveOrg"),
                            ORGANISATION.CREATED_ON.as("createdOn"),
                            ORGANISATION.UPDATED_ON.as("updatedOn"),
                            createdByEmail.as("createdBy"),
                            updatedByEmail.as("updatedBy"),
                            DSL.val("VERIFIED").as("verificationStatus")
                    )
                    .from(ORGANISATION)
                    .join(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                    .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .leftJoin(createdBy).on(ORGANISATION.CREATED_BY.eq(createdById))
                    .leftJoin(updatedBy).on(ORGANISATION.UPDATED_BY.eq(updatedById))
                    .where(INDIVIDUAL_ROLE.ROLE_ID.eq(tenantAdminRoleId))
                    .and(finalCondition)
                    .orderBy(ORGANISATION.CREATED_ON)
                    .fetchInto(TenantsDTO.class);

            List<TenantsDTO> unverifiedFromIndividualVerification = individualVerificationAuditDao.ctx()
                    .select(
                            INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID.as("orgId"),
                            field("INDIVIDUAL_VERIFICATION_AUDIT.meta_data ->> 'trustName'", String.class).as("trustName"),
                            field("INDIVIDUAL_VERIFICATION_AUDIT.meta_data ->> 'orgEmail'", String.class).as("orgEmail"),
                            field("INDIVIDUAL_VERIFICATION_AUDIT.meta_data ->> 'individualName'", String.class).as("tenantAdminName"),
                            INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_VALUE.as("tenantAdminEmail"),
                            field("INDIVIDUAL_VERIFICATION_AUDIT.meta_data ->> 'contactNumber'", String.class).as("tenantAdminMobileNumber"),
                            DSL.val(false, Boolean.class).as("isActiveOrg"),
                            INDIVIDUAL_VERIFICATION_AUDIT.CREATED_ON.as("createdOn"),
                            INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS.as("verificationStatus"),
                            createdByEmail.as("createdBy")
                    )
                    .from(INDIVIDUAL_VERIFICATION_AUDIT)
                    .leftJoin(createdBy).on(INDIVIDUAL_VERIFICATION_AUDIT.CREATED_BY.eq(createdById))
                    .where(INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID.isNull())
                    .and(INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS.eq(VerificationStatusEnum.PENDING.name()))
                    .fetchInto(TenantsDTO.class);

            List<TenantsDTO> combinedList = new ArrayList<>(tenantsDTOList);
            combinedList.addAll(unverifiedFromIndividualVerification);

            int start = Math.min(offset, combinedList.size());
            int end = Math.min(offset + pageSize, combinedList.size());
            return combinedList.subList(start, end);

        } catch (Exception ex) {
            throw new IllegalArgumentException("Exception while fetching all tenants by admin", ex);
        }
    }

    public Integer getAllTenantsCount(Condition finalCondition) {
        try {
            // Count of verified tenants
            int verifiedCount = organisationDao.ctx()
                    .selectCount()
                    .from(ORGANISATION)
                    .join(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                    .join(INDIVIDUAL).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .where(INDIVIDUAL_ROLE.ROLE_ID.eq(tenantAdminRoleId))
                    .and(finalCondition != null ? finalCondition : DSL.noCondition())
                    .fetchOne(0, int.class);

            // Count of unverified tenants
            int unverifiedCount = individualVerificationAuditDao.ctx()
                    .selectCount()
                    .from(INDIVIDUAL_VERIFICATION_AUDIT)
                    .where(INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID.isNull())
                    .and(INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS.eq(VerificationStatusEnum.PENDING.name()))
                    .fetchOne(0, int.class);

            return verifiedCount + unverifiedCount;
        } catch (Exception ex) {
            throw new IllegalArgumentException("Exception while fetching tenant count", ex);
        }
    }


    public List<Organisation> findAllByCreatedOnBetween(LocalDateTime startOfDay, LocalDateTime endOfDay) {

        return organisationDao.ctx().select()
                .from(ORGANISATION)
                .where(ORGANISATION.CREATED_ON.between(startOfDay, endOfDay))
                .fetchInto(Organisation.class);
    }

    public List<GetOrganisationDropDown> getOrganisationDropDowns() {

        return organisationDao.ctx().select(ORGANISATION.ID.as("id"),ORGANISATION.NAME.as("orgName"))
                .from(ORGANISATION)
                .where(ORGANISATION.CATEGORY.eq(OrganisationEnums.DONOR.name()))
                .fetchInto(GetOrganisationDropDown.class);
    }

}


