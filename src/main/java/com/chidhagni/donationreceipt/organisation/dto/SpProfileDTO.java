package com.chidhagni.donationreceipt.organisation.dto;
//
//import com.chidhagni.houzer.documents.DocumentDTO;
//import com.chidhagni.houzer.users.ServiceProviderAddress;
//import com.chidhagni.houzer.users.ServiceProviderList;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class SpProfileDTO {

    private UUID userId;
    private UUID[] servicesProvided;
    private String systemCode;
    private UUID referenceType;
    private String otherReference;
    private String individualName;
    private String companyName;
    private UUID companyType;

//    @Builder.Default
//    private List<ServiceProviderList> serviceProviderList = List.of();

    private UUID designation;
    private String otherDesignation;

    private String email;

    private String mobileNumber;
    private String alternateMobileNumber;
    private String websiteUrl;
    private String socialMediaPresence;
    private UUID portalsRegistered;
//    @Builder.Default
//    private List<ServiceProviderAddress> spAddresses= List.of();
    private String gstNo;
    private String panNo;
    private String tanNo;
    private String cinNo;

    private String teamSize;
    private UUID noOfSalesTeamMembers;
    private List<UUID> typeOfClientServed;
    private String anyOtherServiceProvided;
    private String anyOtherPortalRegistered;
    private UUID yearsOfExperienceId;
    private String yearsOfExperience;
    private String lastThreeYearsTurnOver;
    private String completedProjectsOrCases;
    private String onGoingProjectsOrCases;

    private String accountNumber;
    private String ifscCode;
    private String branch;
    private String bankName;

//    private List<DocumentDTO> awardImages;
//    private List<DocumentDTO> companyImages;
    private UUID assignedTo;
    private UUID leadStatus;
    private UUID leadPriority;
    private String remarks;

    private UUID packageType;

    private Boolean isListingEmpanelled;
    private Boolean isMicrositeEmpanelled;
    private Boolean isStrategicPartner;

    @JsonIgnore
    private LocalDateTime createdOn;
    @JsonIgnore
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;

    private String stateName;

    //new -fields
    private String curatedOn;
    private UUID curatedBy;
    private List<UUID> areaOfOperation;
    private UUID referralName;
    @Builder.Default
    private String doYouHaveGst = "";
    @Builder.Default
    private String createdByName = "";
}
