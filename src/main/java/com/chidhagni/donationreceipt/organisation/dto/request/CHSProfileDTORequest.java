package com.chidhagni.donationreceipt.organisation.dto.request;

import com.chidhagni.donationreceipt.organisation.constants.GSTEnums;
import com.chidhagni.donationreceipt.organisation.constants.ReadinessEnums;
import com.chidhagni.donationreceipt.organisation.constants.RegisteredFor;
import com.chidhagni.donationreceipt.organisation.constants.RequisitionEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CHSProfileDTORequest {

    //SOCIETY Information
    private UUID userId;
    private String systemCode;
    private String societyAddress;
    private String googleMapLocation;
    private String name;
    private LocalDate enrolledDate;
    //enums
    private List<RegisteredFor> registeredFor;
    private RequisitionEnums requisition;
    private ReadinessEnums readiness;


    //business information
    private String gstNo;
    private GSTEnums doYouHaveGstNo;
    private String panNo;
    private String stateName;
    private String accountNumber;
    private String ifscCode;
    private String branch;
    private String bankName;


    //LAND DETAILS
    private String grossPlotArea;
    private String builtUpAreaResidential;
    private String builtUpAreaCommercial;
    private String freeholdLeasehold;
    private String noOfResidence;
    private String noOfCommercial;


    //FSI
    private String buildingAge;
    private String fsiConsumedFsi;
    private String fsi_AvailableFsi;
    private String fsi_PermissibleFsi;
    private String heightRestriction;
    private String scheme;
    private String dpRestrictions;
    private String litigationsOrEncroachment;


    //REQUIREMENTS
    private String requirements_ExtraArea;
    private String requirements_Rent;
    private String requirements_Corpus;
    private String notes;
    private String leadGivenTo;



    //CONTACT/REFERENCE
    private String secretaryName;
    private String secretaryContactNo;
    private String chairmanName;
    private String chairmanContactNo;
    private String emailId;
    private String referenceSocietyName;
    private String referenceContactPerson;
    private String referenceContactNumber;





    //OTHER DETAILS
    private String documentsAvailable;
    private String redevelopment;
    private String professionalDetails;




    //ASSIGNED TO
    private UUID assignedTo;
    private UUID leadPriority;
    private UUID leadStatus;
    private UUID sourceGroup;
    private UUID subSourceGroup;
    private String remarks;

    //SOCIETY COMMITTE MEMBER INFORMATION
    private List<SocietyCommitteeMemberInformation> societyCommitteeMemberInformationList;


    //SOCIETY MEMEBER INFORMATION -PREPOPULATED
    private String societyMemberName;
    private UUID societyMemberDesignation;
    private String societyMemberContactNumber;
    private String loginEmail;
    private String alternateNumber;
    private String fromDate;
    private String toDate;


    private String roadWidth;
    private String authority;
    private String mobileNumber;
    private String plotCTSNo;
    private String teamMember;
    private String location;
    private String ward;
    private String zone;
    private String type;
    private String date;
    private String pinCode;
    private String owner;
    private String reference;
    private UUID designation;
    private String otherDesignation;
    private UUID locationId;

    private UUID referenceType;
    private String referralName;
    private String referralNumber;
    private String referralEmail;
    private String referralCompanyName;

    private String curatedOn;
    private UUID curatedBy;


}
