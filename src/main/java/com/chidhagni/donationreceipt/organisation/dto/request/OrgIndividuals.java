package com.chidhagni.donationreceipt.organisation.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrgIndividuals {


    private UUID id;//from frontend a randomly generated id is sent
    private UUID individualId;
    private String name;
    private String email;
    private String mobileNumber;
    private UUID roleId;
    private String isVerified;
    private Boolean isActive;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;

}