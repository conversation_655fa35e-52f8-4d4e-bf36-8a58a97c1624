package com.chidhagni.donationreceipt.organisation.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocietyCommitteeMemberInformation {

    private UUID id;
    private String name;
    private UUID designation;
    private String contactNumber;
    private String alternateNumber;
    private String email;
    private String fromDate;
    private String toDate;
    private Boolean isActive;
}
