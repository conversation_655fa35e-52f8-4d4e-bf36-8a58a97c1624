package com.chidhagni.donationreceipt.organisation.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
public class BroadcastPaginationRequest {

    @Builder.Default
    private Integer page = 1;

    @Builder.Default
    private Integer pageSize = 10;

    @Builder.Default
    private String companyNameFilter = "";

    @Builder.Default
    private List<UUID> locationFilter = new ArrayList<>();

    @Builder.Default
    private List<UUID> assignedToFilter = new ArrayList<>();

    @Builder.Default
    private List<UUID>  leadStatusFilter= new ArrayList<>();

    @Builder.Default
    private List<UUID>  leadPriorityFilter= new ArrayList<>();

    private boolean isListingEmpanelled;

    private boolean isMicroSiteEmpanelled;

    private boolean isStrategicPartner;

}