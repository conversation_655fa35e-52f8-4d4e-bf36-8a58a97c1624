package com.chidhagni.donationreceipt.organisation.dto;

import com.chidhagni.donationreceipt.documentrepo.dto.CompanyOrAwardDocumentDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SpProfileDTOV2 {
    //basic-info
    private UUID userId;
    private String individualName;// refactor the variable name
    private UUID designation;
    private String otherDesignation;
    private String email;
    private String mobileNumber;
    private String alternateMobileNumber;

    // reference section
    private UUID referenceType;
    private UUID referralName;
    private String referralNumber;
    private String referralEmail;
    private String otherReference;

    //service-provider -info
    private String companyName;
    private UUID companyType;
    private UUID[] servicesProvided;
    private String systemCode;
    private String websiteUrl;
    private String socialMediaPresence;
    private String teamSize;
    private UUID noOfSalesTeamMembers;
    private List<UUID> typeOfClientServed;
    private String anyOtherServiceProvided;
    private String anyOtherPortalRegistered;
    private UUID yearsOfExperienceId;
    private String yearsOfExperience;
    private String lastThreeYearsTurnOver;
    private String completedProjectsOrCases;
    private String onGoingProjectsOrCases;

    //bssiness info
    private UUID portalsRegistered;
    @Builder.Default
    private String doYouHaveGst = "";
    private String gstNo;
    private String panNo;
    private String tanNo;
    private String cinNo;
    @Builder.Default
    private List<Address> spAddresses = new ArrayList<>();
    private List<UUID> areaOfOperation = new ArrayList<>();


    //bank details
    private String accountNumber;
    private String ifscCode;
    private String branch;
    private String bankName;

    //TODo use document repo instead of documents
    private List<CompanyOrAwardDocumentDTO> awardImages;
    private List<CompanyOrAwardDocumentDTO> companyImages;

    //status and assignment details
    private UUID assignedTo;
    private UUID leadStatus;
    private UUID leadPriority;
    private String remarks;

    private UUID packageType;

    private Boolean isListingEmpanelled;
    private Boolean isMicrositeEmpanelled;
    private Boolean isStrategicPartner;

    @Builder.Default
    private String createdOn = null;
    @Builder.Default
    private String updatedOn = null;
    private UUID createdBy;
    private UUID updatedBy;
    private String stateName;


    //Service - Provider Inf - Prepopulated
    private String spName;
    private UUID spDesignation;
    private String loginEmail;
    private String spMobileNumber;
    private String spAlternateMobileNumber;
    private String fromDate;
    private String toDate;

    @Builder.Default
    private String curatedOn = null;
    @Builder.Default
    private UUID curatedBy = null;
    @Builder.Default
    private String createdByName = "";
}
