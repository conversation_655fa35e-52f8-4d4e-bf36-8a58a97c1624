package com.chidhagni.donationreceipt.organisation.dto.response;

import com.chidhagni.donationreceipt.documentrepo.dto.CompanyOrAwardDocumentDTO;
import com.chidhagni.donationreceipt.organisation.dto.Address;
import com.chidhagni.donationreceipt.organisation.dto.ServiceProviderIndividuals;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSpProfileDTOResponse {
    private UUID userId;
    private String individualName;
    private UUID designation;
    private String otherDesignation;
    private String email;
    private String mobileNumber;
    private String alternateMobileNumber;

    private UUID referenceType;
    private UUID referralName;
    private String referralNumber;
    private String referralEmail;
    private String otherReference;

    private String companyName;
    private UUID companyType;
    private UUID[] servicesProvided;
    private String systemCode;
    private String websiteUrl;
    private String socialMediaPresence;
    private String teamSize;
    private UUID noOfSalesTeamMembers;
    private List<UUID> typeOfClientServed;
    private String anyOtherServiceProvided;
    private String anyOtherPortalRegistered;
    private UUID yearsOfExperienceId;
    private String yearsOfExperience;
    private String lastThreeYearsTurnOver;
    private String completedProjectsOrCases;
    private String onGoingProjectsOrCases;

    private UUID portalsRegistered;
    @Builder.Default
    private String doYouHaveGst = "";
    private String gstNo;
    private String panNo;
    private String tanNo;
    private String cinNo;
    @Builder.Default
    private List<Address> spAddresses = new ArrayList<>();
    private List<UUID> areaOfOperation = new ArrayList<>();

    private String accountNumber;
    private String ifscCode;
    private String branch;
    private String bankName;

    private List<CompanyOrAwardDocumentDTO> awardImages;
    private List<CompanyOrAwardDocumentDTO> companyImages;

    private UUID assignedTo;
    private UUID leadStatus;
    private UUID leadPriority;
    private String remarks;

    private UUID packageType;

    private Boolean isListingEmpanelled;
    private Boolean isMicrositeEmpanelled;
    private Boolean isStrategicPartner;

    @Builder.Default
    private String createdOn = null;
    @Builder.Default
    private String updatedOn = null;
    private UUID createdBy;
    private UUID updatedBy;
    private String stateName;

    private String spName;
    private UUID spDesignation;
    private String loginEmail;
    private String spMobileNumber;
    private String spAlternateMobileNumber;
    private String fromDate;
    private String toDate;

    @Builder.Default
    private String curatedOn = null;
    @Builder.Default
    private UUID curatedBy = null;
    @Builder.Default
    private String createdByName = "";
    @Builder.Default
    private List<ServiceProviderIndividuals> serviceProviders = List.of();
}