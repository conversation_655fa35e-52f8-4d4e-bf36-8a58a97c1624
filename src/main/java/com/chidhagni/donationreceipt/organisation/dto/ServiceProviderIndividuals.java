package com.chidhagni.donationreceipt.organisation.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ServiceProviderIndividuals {
    private UUID id;
    private String name;
    private String mobileNumber;
    private String alternateMobileNumber;
    private String email;
    private String designation;
    private Boolean isActive;
    private String fromDate;
    private String toDate;
}
