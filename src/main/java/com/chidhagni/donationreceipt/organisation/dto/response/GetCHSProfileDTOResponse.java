package com.chidhagni.donationreceipt.organisation.dto.response;


import com.chidhagni.donationreceipt.organisation.dto.ChsProfileDTO;
import com.chidhagni.donationreceipt.organisation.dto.request.SocietyCommitteeMemberInformation;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetCHSProfileDTOResponse {

    private ChsProfileDTO ChsProfileDTO;
    private List<SocietyCommitteeMemberInformation> societyCommitteeMemberInformation;
}
