package com.chidhagni.donationreceipt.organisation.dto;

import com.chidhagni.donationreceipt.organisation.constants.GSTEnums;
import com.chidhagni.donationreceipt.organisation.constants.ReadinessEnums;
import com.chidhagni.donationreceipt.organisation.constants.RegisteredFor;
import com.chidhagni.donationreceipt.organisation.constants.RequisitionEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
// TODO REMOVE THIS COMMENT CODE AFTER EXECUTING THE ENDPOINTS - /api/v1/excel-data-migration/inserting-into-society-address
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChsProfileDTO {


    //SOCIETY Information
    private UUID userId;
    private String systemCode;
    private String societyAddress;
    private String googleMapLocation;
    private String name;
    private LocalDate enrolledDate;
    //enums
    private List<RegisteredFor> registeredFor;
    private RequisitionEnums requisition;
    private ReadinessEnums readiness;
    private boolean consentAgreement;


    //business information
    private String gstNo;
    private String panNo;
    private String stateName;
    private GSTEnums doYouHaveGstNo;
    private String accountNumber;
    private String ifscCode;
    private String branch;
    private String bankName;


    //LAND DETAILS
    private String grossPlotArea;
    private String builtUpAreaResidential;
    private String builtUpAreaCommercial;
    private String noOfResidence;
    private String noOfCommercial;


    //FSI
    private String buildingAge;
    private String fsiConsumedFsi;
    private String fsi_AvailableFsi;
    private String fsi_PermissibleFsi;
    private String heightRestriction;
    private String scheme;
    private String dpRestrictions;
    private String litigationsOrEncroachment;


    //REQUIREMENTS
    private String requirements_ExtraArea;
    private String requirements_Rent;
    private String requirements_Corpus;
    private String notes;
    private String leadGivenTo;




    //ASSIGNED TO
    private UUID assignedTo;
    private UUID leadPriority;
    private UUID leadStatus;
    private String remarks;



    //SOCIETY MEMEBER INFORMATION -PREPOPULATED
    private String societyMemberName;
    private UUID societyMemberDesignation;
    private String societyMemberContactNumber;
    private String loginEmail;
    private String alternateNumber;
    private String fromDate;
    private String toDate;


    private String roadWidth;
    private String authority;
    private String plotCTSNo;
    private String teamMember;
    private String location;
    private String zone;
    private String pinCode;
    private UUID locationId;

    private UUID referenceType;
    private String referralName;
    private String referralNumber;
    private String referralEmail;
    private String referralCompanyName;


    private LocalDateTime createdOn;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime updatedOn;


    @Builder.Default
    private String curatedOn = null;
    @Builder.Default
    private UUID curatedBy = null;



    //Additional Fields
    private String coreTeamMember;
    private String numberOfFloors;
    private String numberOfWings;
    private String lift;
    private String gasPipeline;
    private String security;
    private String gym;
    private String cctv;
    private String intercom;
    private String fireSafety;
    private String gardenChildrenPlay;
    private String rainWaterHarvesting;
    private String acLobby;
    private String swimmingPool;
    private String carParking;
    private String communityHallClubhouse;
    private String sewageTreatmentPlant;
    private String indoorGames;
    private String servantQuarters;
    private String powerBackUp;
    private String commercial;
    private String plotSizeInSqMts;
    private String reraNumber;



    //deprecated fields - verify with society chs longform in production
    private String freeholdLeasehold;
    private String secretaryName;
    private String secretaryContactNo;
    private String chairmanName;
    private String chairmanContactNo;
    private String emailId;
    private String referenceSocietyName;
    private String referenceContactPerson;
    private String referenceContactNumber;
    private String documentsAvailable;
    private String redevelopment;
    private String professionalDetails;
    private UUID sourceGroup;
    private UUID subSourceGroup;
    private String mobileNumber;
    private String ward;
    private String type;
    private String date;
    private String owner;
    private String reference;
    private UUID designation;
    private String otherDesignation;


}

