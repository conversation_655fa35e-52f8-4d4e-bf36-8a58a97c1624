package com.chidhagni.donationreceipt.organisation.dto.request;

//import com.chidhagni.houzer.documents.DocumentDTO;
import com.chidhagni.donationreceipt.organisation.dto.Address;
import com.chidhagni.donationreceipt.organisation.dto.ServiceProviderIndividuals;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpProfileDTORequest {
    //basic-info
    private UUID userId;
    private String individualName;// refactor the variable name
    private UUID designation;
    private String otherDesignation;
    private String email;
    private String mobileNumber;
    private String alternateMobileNumber;
    private List<ServiceProviderIndividuals> serviceProviderIndividuals;

    // reference section
    private UUID referenceType;
    private UUID referralName;
    private String referralNumber;
    private String referralEmail;
    private String otherReference;

    //service-provider -info
    private String companyName;
    private UUID companyType;
    private UUID[] servicesProvided;
    private String systemCode;
    private String websiteUrl;
    private String socialMediaPresence;
    private String teamSize;
    private UUID noOfSalesTeamMembers;
    private List<UUID> typeOfClientServed;
    private String anyOtherServiceProvided;
    private String anyOtherPortalRegistered;
    private UUID yearsOfExperienceId;
    private String yearsOfExperience;
    private String lastThreeYearsTurnOver;
    private String completedProjectsOrCases;
    private String onGoingProjectsOrCases;

    //bssiness info
    private UUID portalsRegistered;
    private String doYouHaveGst = "";
    private String gstNo;
    private String panNo;
    private String tanNo;
    private String cinNo;
    private List<Address> spAddresses;
    private List<UUID> areaOfOperation;

    //bank details
    private String accountNumber;
    private String ifscCode;
    private String branch;
    private String bankName;

//    private List<DocumentDTO> awardImages;
//    private List<DocumentDTO> companyImages;

    //status and assignment details
    private UUID assignedTo;
    private UUID leadStatus;
    private UUID leadPriority;
    private String remarks;

    private UUID packageType;

    private Boolean isListingEmpanelled;
    private Boolean isMicrositeEmpanelled;
    private Boolean isStrategicPartner;

    private UUID createdBy;
    private UUID updatedBy;
    private String stateName;
    private String createdByName = "";


    //Service - Provider Inf - Prepopulated
    private String spName;
    private UUID spDesignation;
    private String loginEmail;
    private String spMobileNumber;
    private String spAlternateMobileNumber;
    private String fromDate;
    private String toDate;

    private String curatedOn = null;
    private UUID curatedBy = null;

}
