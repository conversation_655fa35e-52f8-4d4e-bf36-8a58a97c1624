package com.chidhagni.donationreceipt.individual.util;


//import com.chidhagni.donation_receipt.db.jooq.tables.pojos.Individual;
//import com.chidhagni.donation_receipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;

import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.individual.IndividualRepository;
import com.chidhagni.donationreceipt.individualPasswordResetAudit.IndividualPasswordResetRepository;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.organisation.utils.OrganisationMapper;
import com.chidhagni.donationreceipt.roles.RolesRepository;
import com.chidhagni.donationreceipt.services.listvalues.ListValuesRepository;
import com.chidhagni.utils.CommonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class IndividualHelperMethods {

    private final IndividualRepository individualRepository;
    private final RolesRepository rolesRepository;
    private final ListValuesRepository listValuesRepository;
    private final NotificationManager notificationManager;
    private final IndividualPasswordResetRepository individualPasswordResetRepository;
    private final OrganisationMapper organisationMapper;
    private final OrganizationRepository organizationRepository;
    private final ObjectMapper objectMapper;
    private final CommonUtils commonUtils;

//    public Roles getPermissions(UUID roleId) {
//        return rolesRepository.getRoleById(roleId);
//    }

    public void isIndividualAlreadyExistByEmail(String email) {
        if (email == null) {
            throw new IllegalArgumentException("Email Cannot Be Null");
        }
        Individual individualByEmail = individualRepository.getByEmail(email);
        if (individualByEmail != null) {
            throw new IllegalArgumentException(String.format("Individual Already Exist With Given Email :: %s", email));
        }
    }

//    public void sendResetPasswordNotification(Individual individual, boolean isServiceProvider,
//                                              IndividualPasswordResetAudit individualPasswordResetAudit) throws MessagingException {
//
//        String resetPasswordLink = commonUtils.generateAndSavePasswordResetCode(individualPasswordResetAudit);
//        String organisationType = isServiceProvider ? DefaultOrganisationCategoryEnums.SERVICE_PROVIDER.name()
//                : DefaultOrganisationCategoryEnums.SOCIETY.name();
//
//        //notificationManager.sendSuccessRegistrationWithRestLinkV1(individual, resetPasswordLink, organisationType);
//    }

}
