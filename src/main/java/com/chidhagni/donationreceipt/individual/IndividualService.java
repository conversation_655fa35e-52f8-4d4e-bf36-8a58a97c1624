package com.chidhagni.donationreceipt.individual;

//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualDao;
//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualPermissionDao;
//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualRoleDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.*;
//import com.chidhagni.donationreceipt.individualpermission.IndividualRolesToIndividualPermissionService;

//import com.chidhagni.houzer.services.userserviceprofile.UserServiceProfileService;

import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPermissionDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.*;
import com.chidhagni.donationreceipt.individual.dto.request.*;
import com.chidhagni.donationreceipt.individual.dto.response.GetAllIndividualsResponse;
import com.chidhagni.donationreceipt.individual.dto.response.IndividualResponseDTO;
import com.chidhagni.donationreceipt.individual.dto.response.IndividualResponseWithRolesDTO;
import com.chidhagni.donationreceipt.individual.util.IndividualMapper;
import com.chidhagni.donationreceipt.individual.util.IndividualMapperI;
import com.chidhagni.donationreceipt.individualpermission.IndividualRolesToIndividualPermissionService;
import com.chidhagni.donationreceipt.individualrole.IndividualRoleRepository;
import com.chidhagni.donationreceipt.individualsignup.utils.IndividualSignUpMapper;
import com.chidhagni.donationreceipt.individualsignup.utils.SignUpMapper;
import com.chidhagni.donationreceipt.individualverificationaudit.IndividualVerificationAuditRepository;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import com.chidhagni.donationreceipt.individualverificationaudit.utils.IndividualVerificationMapper;
import com.chidhagni.donationreceipt.mail.EmailHelperService;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.organisation.dto.request.OrgIndividuals;
import com.chidhagni.donationreceipt.organisation.dto.response.DonorsResponse;
import com.chidhagni.donationreceipt.organisation.dto.response.GetAllDonors;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;

//import static com.chidhagni.houzer.db.jooq.Tables.*;
//import static com.chidhagni.houzer.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndividualService {



    private final IndividualRepository individualRepository;

    private final IndividualRolesToIndividualPermissionService individualRolesToIndividualPermissionService;

    private final IndividualRoleRepository individualRoleRepository;

    private final OrganizationRepository organizationRepository;
    private final IndividualPermissionDao individualPermissionDao;
    private final IndividualRoleDao individualRoleDao;
    private final IndividualDao individualDao;

    @Value("${donor.type.entity}")
    private UUID entityDonorType;

    /**
     * Retrieves an individual along with role, organisation and permissions by their unique identifier.
     *
     * @param individualId
     * @return
     */
    public IndividualDTO getByIndividualId(UUID individualId) {
        Individual individual = individualRepository.getById(individualId);
        IndividualMapper.checkIfIndividualIsNull(individual);
        UUID roleId = null;
        List<IndividualRole> individualRoleList = individualRepository.getIndividualRoleByIndividualId(individualId);
        IndividualMapper.checkIfIndividualRoleListIsEmpty(individualRoleList, individualId);
        roleId = individualRoleList.get(0).getRoleId();
        List<RoleNode> permissionsDTOList =
                individualRolesToIndividualPermissionService.fetchPermissionsByIndividualId(individualId);
        List<IndividualRole> individualRoleRepositoryList = this.individualRoleRepository.getByIndivdiualId(individualId);
        UUID orgId = individualRoleRepositoryList.get(0).getOrgId();
        Organisation organizationEntity = organizationRepository.getById(orgId);
        return IndividualMapper.map(individual, roleId, organizationEntity, permissionsDTOList);
    }


    //    private static final Logger log = LoggerFactory.getLogger(IndividualService.class);
    private final SignUpMapper signUpMapper;
    private final IndividualSignUpMapper individualSignUpMapper;
    private final EmailHelperService emailHelperService;
    private final NotificationManager notificationManager;
    private final IndividualVerificationAuditRepository individualVerificationAuditRepository;
    private final IndividualVerificationMapper individualVerificationMapper;
    //    private final RolesRepository rolesRepository;
//    private final RolesService rolesService;
//    private final OrganizationRepository organizationRepository;
    private static Integer pageSize = 10;
    private static Integer pageNo = 1;

    //    private final IndividualRoleDao individualRoleDao;
//    private final IndividualDao individualDao;
//    private final IndividualPermissionDao individualPermissionDao;
//    private final IndividualRolesToIndividualPermissionService individualRolesToIndividualPermissionService;
//    private final IndividualRoleRepository individualRoleRepository;
//    private final CommonUtils commonUtils;
//
//    private final OrganisationLeadAssignmentRepository organisationLeadAssignmentRepository;
//    private final IndividualPasswordResetRepository individualPasswordResetRepository;
//    private final IndividualHelperMethods individualHelperMethods;
//    private final UserServiceProfileService userServiceProfileService;
//
//    @Value("${society.roles.id}")
//    private UUID societyId;
//
//    @Value("${service.provider.roles.id}")
//    private UUID serviceProviderId;
//
//
//    /**
//     * Retrieves a paginated list of individuals along with their associated roles.
//     *
//     * <p>This method applies default pagination settings, constructs filtering and sorting conditions,
//     * fetches individual records, and maps them with their respective roles.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Applies default pagination if not provided.</li>
//     *   <li>Constructs filtering conditions based on search parameters.</li>
//     *   <li>Applies sorting conditions.</li>
//     *   <li>Fetches individual records from the repository.</li>
//     *   <li>Maps individual records with associated roles.</li>
//     *   <li>Returns the final response with the total row count.</li>
//     * </ul>
//     *
//     * @param paginationRequest The pagination request containing search and sorting parameters.
//     * @return A `GetAllIndividualsResponse` containing a paginated list of individuals with roles.
//     */
    public GetAllIndividualsResponse getAllIndividuals(PaginationRequest paginationRequest, UserPrincipal userPrincipal) {
        applyDefaults(paginationRequest);
        Condition finalCondition = getSearchingCondition(paginationRequest);
        List<SortField<?>> sortFields = new ArrayList<>();
        //  getSortingCondition(sortFields, paginationRequest);
        List<IndividualResponseDTO> allIndividuals = individualRepository.getAllIndividuals(finalCondition, sortFields, paginationRequest, userPrincipal);
        List<IndividualResponseWithRolesDTO> responseWithParentRolesList = getResponseWithParentRolesList(allIndividuals);

        Integer count = individualRepository.getIndividualsCount(finalCondition, userPrincipal);
        return GetAllIndividualsResponse.builder()
                .individualResponseDTOS(responseWithParentRolesList)
                .rowCount(count)
                .build();
    }

    //
//    /**
//     * Maps a list of individuals to include their associated parent roles.
//     *
//     * <p>This method retrieves the organization name or company name for each individual
//     * and maps the response accordingly.</p>
//     *
//     * @param allIndividuals List of individual records.
//     * @return A list of `IndividualResponseWithRolesDTO` objects containing individual details with roles.
//     */
    public List<IndividualResponseWithRolesDTO> getResponseWithParentRolesList(List<IndividualResponseDTO> allIndividuals) {
        return allIndividuals.stream()
                .map(individualResponseDTO -> {
                    String nameOrCompanyName = organizationRepository
                            .getNameOrCompanyNameById(individualResponseDTO.getOrganisationId())
                            .orElse("");
                    return IndividualMapper.toRolesResponseDto(individualResponseDTO, nameOrCompanyName);
                })
                .collect(Collectors.toList());
    }

    //
//
//    /**
//     * Applies default pagination values if not provided for page, page size.
//     *
//     * <p>Ensures that page number and page size have valid values to avoid unintended behavior.</p>
//     *
//     * @param paginationRequest The pagination request object.
//     */
    public void applyDefaults(PaginationRequest paginationRequest) {
        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() < 1) {
            paginationRequest.setPageSize(pageSize);
        }
        if (paginationRequest.getPage() == null || paginationRequest.getPage() < 1) {
            paginationRequest.setPage(pageNo);
        }
    }

    //
//    /**
//     * Constructs sorting conditions based on the provided pagination request.
//     *
//     * <p>Determines the sorting order based on name, email, mobile number, and creation date.</p>
//     *
//     * @param sortFields        The list of sorting conditions.
//     * @param paginationRequest The pagination request containing sorting parameters.
//     */
//    public void getSortingCondition(List<SortField<?>> sortFields, PaginationRequest paginationRequest) {
//        sortFields.add(INDIVIDUAL.CREATED_ON.desc());
//        if (paginationRequest.isSortByNameDesc()) {
//            sortFields.add(INDIVIDUAL.FIRST_NAME.desc());
//        }
//        if (paginationRequest.isSortByNameAsc()) {
//            if (sortFields.contains(INDIVIDUAL.FIRST_NAME.desc())) {
//                sortFields.remove(INDIVIDUAL.FIRST_NAME.desc());
//            }
//            sortFields.add(INDIVIDUAL.FIRST_NAME.asc());
//        }
//        if (paginationRequest.isSortByNameDesc()) {
//            sortFields.add(INDIVIDUAL.EMAIL.desc());
//        }
//        if (paginationRequest.isSortByEmailAsc()) {
//            if (sortFields.contains(INDIVIDUAL.EMAIL.desc())) {
//                sortFields.remove(INDIVIDUAL.EMAIL.desc());
//            }
//            sortFields.add(INDIVIDUAL.EMAIL.asc());
//        }
//        if (paginationRequest.isSortByMobileDesc()) {
//            sortFields.add(INDIVIDUAL.MOBILE_NUMBER.desc());
//        }
//        if (paginationRequest.isSortByMobileAsc()) {
//            if (sortFields.contains(INDIVIDUAL.MOBILE_NUMBER.desc())) {
//                sortFields.remove(INDIVIDUAL.MOBILE_NUMBER.desc());
//            }
//            sortFields.add(INDIVIDUAL.MOBILE_NUMBER.asc());
//        }
//        if (paginationRequest.isSortByCreatedDateAsc()) {
//            sortFields.remove(INDIVIDUAL.CREATED_ON.desc());
//            sortFields.add(INDIVIDUAL.CREATED_ON.asc());
//        }
//    }
//
//    /**
//     * Constructs search conditions for filtering individuals.
//     *
//     * <p>Applies search filters for name, email, mobile, and role.</p>
//     *
//     * @param paginationRequest The pagination request containing search parameters.
//     * @return A `Condition` object representing the filtering criteria.
//     */
    public Condition getSearchingCondition(PaginationRequest paginationRequest) {
        String searchByName = paginationRequest.getNameFilter();
        String searchByEmail = paginationRequest.getEmailFilter();
        String searchByMobile = paginationRequest.getMobileFilter();
        UUID role = paginationRequest.getRoleFilter();
        UUID orgId = paginationRequest.getOrgId();
        log.info("given search by name, email, mobile, role are ::{}, {}, {}, {}", searchByName, searchByEmail, searchByMobile, role);

        List<Condition> conditions = new ArrayList<>();

        if (searchByName != null && !searchByName.isEmpty()) {
            conditions.add(INDIVIDUAL.NAME.containsIgnoreCase(searchByName));
        }

        if (searchByEmail != null && !searchByEmail.isEmpty()) {
            conditions.add(INDIVIDUAL.EMAIL.containsIgnoreCase(searchByEmail));
        }

        if (searchByMobile != null && !searchByMobile.isEmpty()) {
            conditions.add(INDIVIDUAL.MOBILE_NUMBER.containsIgnoreCase(searchByMobile));
        }
        if (role != null) {
            conditions.add(INDIVIDUAL_ROLE.ROLE_ID.eq(role));
        }

        if (orgId != null) {
            conditions.add(
                    INDIVIDUAL.ID.in(
                            DSL.select(INDIVIDUAL_ROLE.INDIVIDUAL_ID)
                                    .from(INDIVIDUAL_ROLE)
                                    .where(INDIVIDUAL_ROLE.ORG_ID.eq(orgId))
                    )
            );
        }
        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
    }

    //
//    /**
//     * update the role and permissions of an individual.
//     *
//     * @param individualId
//     * @param individualRequest
//     */
    public void updateRoleAndPermissionsInIndividual(UUID individualId, IndividualRequest individualRequest) {

        List<IndividualRole> individualRoleList = individualRepository.getIndividualRoleByIndividualId(individualId);
        IndividualMapper.checkIfIndividualRoleListIsEmpty(individualRoleList, individualId);
        IndividualRole individualRole = individualRoleList.get(0);
        individualRole.setRoleId(individualRequest.getRoleId());
        individualRoleDao.update(individualRole);
        log.info("Individual Role Update Successfully");

        List<IndividualPermission> individualPermissionList = individualRepository.getIndividualPermissionsByIndividualId(individualId);
        IndividualMapper.checkIfIndividualPermissionListIsEmpty(individualPermissionList, individualId);
        IndividualPermission individualPermission = individualPermissionList.get(0);
        individualPermission.setPermissions(individualRequest.getPermissionsDTOList());
        individualPermissionDao.update(individualPermission);
    }
//

    //
//    /**
//     * Deactivates an individual by updating their status.
//     *
//     * <p>Marks the individual as inactive (`isActive = false`).</p>
//     *
//     * @param individualId The unique identifier of the individual.
//     */
    public void deActivateIndividual(UUID individualId) {
        Individual individual = individualRepository.getById(individualId);
        IndividualMapper.checkIfIndividualIsNull(individual);
        log.info("Individual is available with given id :: {} ", individualId);
        individual.setIsActive(false);
        individualDao.update(individual);
        log.info("Individual with id :: {} has been inactivated", individualId);
    }

    //
//    /**
//     * Activates an individual by updating their status.
//     *
//     * <p>Marks the individual as active (`isActive = true`).</p>
//     *
//     * @param individualId The unique identifier of the individual.
//     */
    public void activateIndividual(UUID individualId) {
        Individual individual = individualRepository.getById(individualId);
        IndividualMapper.checkIfIndividualIsNull(individual);
        log.info("Individual available with given id :: {} ", individualId);
        individual.setIsActive(true);
        individualDao.update(individual);
        log.info("Individual with id :: {} has been  inactivated", individualId);
    }
//
//    /**
//     * Retrieves a list of all houzer employees.
//     * @return List of `EmployeeDTO` objects.
//     */
//    public List<EmployeeDTO> getAllEmployee() {
//        return individualRepository.getEmployees();
//    }
//
//    /**
//     * Retrieves a list of all individuals associated with an organization.
//     *
//     * @param orgId
//     * @return List of `IndividualsDTO` objects.
//     */
//    public List<IndividualsDTO> getAllIndividualByOrgId(UUID orgId) {
//        return individualRepository.getIndividualByOrganizations(orgId);
//    }
//
//    /**
//     * Retrieves hierarchy of roles
//     *
//     * @param roleId
//     * @return GetRolesListResponse dto
//     */
//    public GetRolesListResponse getRolesListResponse(UUID roleId) {
//        UUID parentRoleId = rolesRepository.getParentRoleID(roleId);
//        return rolesService.getRolesByParentRoleId(parentRoleId);
//    }
//
//
//
//    public UUID createShortFormForSP(IndividualSPShortFormDTO individualSPShortFormDTO, ContactType contactType,
//                                     UserPrincipal userPrincipal) throws MessagingException, JsonProcessingException {
//        return createShortForm(individualSPShortFormDTO, contactType, userPrincipal, true);
//    }
//
//    public UUID createShortFormForCHS(IndividualCHSShortFormDTO individualCHSShortFormDTO, ContactType contactType,
//                                      UserPrincipal userPrincipal) throws MessagingException, JsonProcessingException {
//        return createShortForm(individualCHSShortFormDTO, contactType, userPrincipal, false);
//    }
//
//
//    public UUID createShortForm(IndividualShortFormDTO individualShortFormDTO, ContactType contactType,
//                                UserPrincipal userPrincipal, boolean isServiceProvider)
//            throws MessagingException, JsonProcessingException {
//
//        log.info("Validating the mobile number, otp, designation, and location");
//        checkingValidations(individualShortFormDTO, contactType, isServiceProvider);
//
//        log.info("Inserting individual and organisation data");
//        Individual individual = IndividualMapper.mapIndividualShortFormDTOToIndividual(individualShortFormDTO,
//                userPrincipal,isServiceProvider);
//        Individual createdIndividual = individualRepository.insertIndividual(individual);
//        log.info("Created Individual: {}", createdIndividual.getId());
//
//        Organisation organisation = individualHelperMethods.insertIndividualAndOrganisation(createdIndividual,
//                individualShortFormDTO, userPrincipal, isServiceProvider);
//        organizationRepository.insertOrganisation(organisation);
//        log.info("Created Organisation: {}", organisation.getId());
//
//
//        UUID[] servicesProvided = null;
//        if (individualShortFormDTO instanceof IndividualSPShortFormDTO) {
//            servicesProvided = ((IndividualSPShortFormDTO) individualShortFormDTO).getServicesProvided();
//        }
//
//        mappingAndInsertingIntoIndividualDerivedEntities(individual, organisation, userPrincipal, isServiceProvider,servicesProvided);
//        log.info("Mapped Inserted into individual Derived tables");
//        mappingAndInsertingOrganisationDerivedEntities(individual, organisation, userPrincipal);
//        log.info("Mapped Inserted into Organisation Derived tables");
//
//        IndividualPasswordResetAudit individualPasswordResetAudit = IndividualMapper.map(individual, userPrincipal);
//        individualPasswordResetRepository.create(individualPasswordResetAudit);
//        individualHelperMethods.sendResetPasswordNotification(individual,isServiceProvider,individualPasswordResetAudit);
//        log.info("Creating individual password reset and sending notification");
//        return individual.getId();
//    }
//
//
//    private void checkingValidations(IndividualShortFormDTO dto, ContactType contactType, boolean isServiceProvider) {
//        commonUtils.checkEmailMobileNumberAndOtpVerificationValidation(contactType, dto.getEmail(), dto.getMobileNumber());
//        UUID locationId=null;
//        if (dto instanceof IndividualSPShortFormDTO) {
//            locationId = ((IndividualSPShortFormDTO) dto).getLocationId();
//        }
//        if (isServiceProvider) {
//            individualHelperMethods.validateDesignationAndLocation(dto.getDesignation(), locationId);
//        } else {
//            individualHelperMethods.validateDesignationAndLocation(dto.getDesignation(), null);
//        }
//        }
//
//  public GetAllIndividualsResponse getAllIndividualsForEmployee(PaginationRequest paginationRequest
//            , UserPrincipal userPrincipal) {
//        UUID loggedInUserId = userPrincipal.getId();
//        applyDefaults(paginationRequest);
//        Condition finalCondition = getSearchingConditionForEmployee(paginationRequest, loggedInUserId);
//        List<SortField<?>> sortFields = new ArrayList<>();
//        getSortingCondition(sortFields, paginationRequest);
//        List<IndividualResponseDTO> allIndividuals = individualRepository
//                .getAllIndividualsByEmployee(finalCondition, sortFields, paginationRequest);
//
//        List<IndividualResponseWithRolesDTO> responseWithParentRolesList = getResponseWithParentRolesList(allIndividuals);
//
//        Integer count = individualRepository.getIndividualsCountByEmployee(finalCondition);
//        return GetAllIndividualsResponse.builder()
//                .individualResponseDTOS(responseWithParentRolesList)
//                .rowCount(count)
//                .build();
//    }
//
//   public Condition getSearchingConditionForEmployee(PaginationRequest paginationRequest, UUID loggedInUserId) {
//        String searchByName = paginationRequest.getNameFilter();
//        String searchByEmail = paginationRequest.getEmailFilter();
//        String searchByMobile = paginationRequest.getMobileFilter();
//        UUID role = paginationRequest.getRoleFilter();
//        UUID assignedToEmployeeId = paginationRequest.getAssignedToEmployeeId();
//        LocalDate fromDate = paginationRequest.getFromDate();
//        LocalDate toDate = paginationRequest.getToDate();
//        String searchBySocietyName = paginationRequest.getSearchBySocietyName();
//        String searchBySocietyAddress = paginationRequest.getSearchBySocietyAddress();
//        List<UUID> searchByLocation = paginationRequest.getSearchByLocation();
//        List<String> searchBySocietyType = paginationRequest.getSearchBySocietyType();
//        List<UUID> searchByLeadPriority = paginationRequest.getSearchByLeadPriority();
//        List<UUID> searchByLeadStatus = paginationRequest.getSearchByLeadStatus();
//        List<UUID> searchByCuratedBy = paginationRequest.getSearchByCuratedBy();
//        LocalDate searchByCuratedOnFrom = paginationRequest.getSearchByCuratedOnFrom();
//        LocalDate searchByCuratedOnTo = paginationRequest.getSearchByCuratedOnTo();
//        List<UUID> assignedToGivenEmployeeId = paginationRequest.getAssignedToGivenEmployeeId();
//        String searchKeyword = paginationRequest.getSearchKeyWord();
//        String searchByCompanyName = paginationRequest.getSearchByCompanyName();
//        List<UUID> searchByServicesProvided = paginationRequest.getSearchByServicesProvided();
//        log.info("given search by name, email, mobile, role are::{}, {}, {}, {}", searchByName, searchByEmail, searchByMobile, role);
//
//        List<Condition> conditions = new ArrayList<>();
//
//        if (searchByName != null && !searchByName.isEmpty()) {
//        conditions.add(INDIVIDUAL.FIRST_NAME.containsIgnoreCase(searchByName)
//                    .or(INDIVIDUAL.LAST_NAME.containsIgnoreCase(searchByName)));
//        }
//
//        if (searchByEmail != null && !searchByEmail.isEmpty()) {
//            conditions.add(INDIVIDUAL.EMAIL.containsIgnoreCase(searchByEmail));
//        }
//
//        if (searchByMobile != null && !searchByMobile.isEmpty()) {
//            conditions.add(INDIVIDUAL.MOBILE_NUMBER.containsIgnoreCase(searchByMobile));
//        }
//
//        if (role != null) {
//            conditions.add(INDIVIDUAL_ROLE.ROLE_ID.eq(role));
//        }
//
//        if (paginationRequest.isCreatedByMe()) {
//            conditions.add(INDIVIDUAL.CREATED_BY.eq(loggedInUserId));
//        } else if (paginationRequest.isAssignedToMe()) {
//            conditions.add(DSL.exists(
//                    DSL.select().from(ORGANISATION_LEAD_ASSIGNMENT)
//                            .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(INDIVIDUAL_ROLE.ORG_ID)
//                                    .and(ORGANISATION_LEAD_ASSIGNMENT.ASSIGNED_TO.eq(loggedInUserId))
//                            )
//            ));
//        }
//        else if(assignedToEmployeeId != null){
//            Set<UUID> allSubordinates = commonUtils.getAllSubordinates(assignedToEmployeeId);
//            allSubordinates.add(assignedToEmployeeId);
//            conditions.add(ORGANISATION_LEAD_ASSIGNMENT.ASSIGNED_TO.in(allSubordinates));
//        }
//        else {
//            Set<UUID> allSubordinates = commonUtils.getAllSubordinates(loggedInUserId);
//            allSubordinates.add(loggedInUserId);
//            conditions.add(DSL.exists(
//                    DSL.select().from(ORGANISATION_LEAD_ASSIGNMENT)
//                            .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(INDIVIDUAL_ROLE.ORG_ID)
//                                    .and(ORGANISATION_LEAD_ASSIGNMENT.ASSIGNED_TO.in(allSubordinates))
//                            )
//            ));
//        }
//
//        if(fromDate != null && toDate != null){
//            conditions.add(INDIVIDUAL.CREATED_ON.between(fromDate.atStartOfDay(), toDate.atTime(LocalTime.MAX)));
//        }
//
//        if(searchBySocietyName != null && !searchBySocietyName.isEmpty()){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and(ORGANISATION.NAME.containsIgnoreCase(searchBySocietyName)))));
//        }
//        if(searchBySocietyAddress != null && !searchBySocietyAddress.isEmpty()){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and(DSL.field("ORGANISATION.meta_data->>'societyAddress'", String.class)
//                                    .containsIgnoreCase(searchBySocietyAddress)))));
//        }
//        if(searchByLocation != null && !CollectionUtils.isEmpty(searchByLocation)){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and((DSL.field("ORGANISATION.meta_data->>'locationId'", String.class)
//                                    .cast(UUID.class))
//                                    .in(searchByLocation)))));
//        }
//        if(searchBySocietyType != null && !CollectionUtils.isEmpty(searchBySocietyType)){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and((DSL.field("ORGANISATION.meta_data->>'type'", String.class))
//                                    .in(searchBySocietyType)))));
//        }
//        if(searchByLeadStatus != null && !CollectionUtils.isEmpty(searchByLeadStatus)){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and(((DSL.field("ORGANISATION.meta_data->>'leadStatus'", String.class))
//                                    .cast(UUID.class))
//                                    .in(searchByLeadStatus)))));
//        }
//        if(searchByLeadPriority != null && !CollectionUtils.isEmpty(searchByLeadPriority)){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and(((DSL.field("ORGANISATION.meta_data->>'leadPriority'", String.class))
//                                    .cast(UUID.class))
//                                    .in(searchByLeadPriority)))));
//        }
//        if(searchByCuratedBy != null && !CollectionUtils.isEmpty(searchByCuratedBy)){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and(((DSL.field("ORGANISATION.meta_data->>'curatedBy'", String.class))
//                                    .cast(UUID.class))
//                                    .in(searchByCuratedBy)))));
//        }
//        if(searchByCuratedOnFrom != null && searchByCuratedOnTo != null){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and(((DSL.field("ORGANISATION.meta_data->>'curatedOn'", String.class))
//                                    .cast(LocalDateTime.class))
//                                    .between(searchByCuratedOnFrom.atStartOfDay()
//                                            , searchByCuratedOnTo.atTime(LocalTime.MAX))))));
//        }
//        if(assignedToGivenEmployeeId != null && !CollectionUtils.isEmpty(assignedToGivenEmployeeId)){
//            conditions.add(ORGANISATION_LEAD_ASSIGNMENT.ASSIGNED_TO.in(assignedToGivenEmployeeId));
//        }
//        if (searchKeyword != null && !searchKeyword.isEmpty()) {
//            conditions.add(
//                    INDIVIDUAL.FIRST_NAME.containsIgnoreCase(searchKeyword)
//                            .or(INDIVIDUAL.LAST_NAME.containsIgnoreCase(searchKeyword))
//                            .or(INDIVIDUAL.EMAIL.containsIgnoreCase(searchKeyword))
//                            .or(DSL.exists(
//                                    DSL.selectOne()
//                                            .from(ORGANISATION)
//                                            .where(ORGANISATION.ID.eq(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID)
//                                                    .and(ORGANISATION.NAME.containsIgnoreCase(searchKeyword)))
//                            ))
//            );
//        }
//        if(searchByCompanyName != null && !searchByCompanyName.isEmpty()){
//            conditions.add(DSL.exists(DSL.selectOne().from(ORGANISATION)
//                    .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                            .and(ORGANISATION.NAME.containsIgnoreCase(searchByCompanyName)))));
//        }
//        if (searchByServicesProvided != null && !searchByServicesProvided.isEmpty()) {
//            conditions.add(DSL.exists(
//                    DSL.selectOne().from(ORGANISATION)
//                            .where(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(ORGANISATION.ID)
//                        .and(DSL.condition("ORGANISATION.meta_data->'servicesProvided' ??| ARRAY[?]::text[]",
//                                DSL.val(searchByServicesProvided.stream().map(UUID::toString).toArray(String[]::new))))
//                            )
//            ));
//        }
//        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
//    }
//      public void isIndividualAlreadyExistByEmail(String email){
//        Individual individualByEmail = individualRepository.getByEmail(email);
//        if(email == null){
//            throw new IllegalArgumentException("Email Cannot Be Null");
//        }
//        if(individualByEmail != null){
//            throw new IllegalArgumentException(String.format("Individual Already Exist With Given Email :: %s", email));
//        }
//    }
//
//    public GetAllIndividualsForContacts getAllIndividualsForContactGroups(ContactsGroupPaginationRequest paginationRequest){
//        applyDefaults(paginationRequest);
//        Condition finalCondition = getSearchingConditionForContacts(paginationRequest);
//        return individualRepository.getAllIndividualsForContacts(finalCondition, paginationRequest);
//    }
//
//    public void applyDefaults(ContactsGroupPaginationRequest paginationRequest) {
//        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() < 1) {
//            paginationRequest.setPageSize(pageSize);
//        }
//        if (paginationRequest.getPage() == null || paginationRequest.getPage() < 1) {
//            paginationRequest.setPage(pageNo);
//        }
//    }
//
//    public Condition getSearchingConditionForContacts(ContactsGroupPaginationRequest paginationRequest){
//        List<Condition> conditions = new ArrayList<>();
//        String searchKeyword = paginationRequest.getSearchKeyword();
//        String category = paginationRequest.getCategory();
//        String orgName = paginationRequest.getSearchByOrgName();
//        List<UUID> serviceTypeIds = paginationRequest.getServiceTypeIds();
//        List<UUID> location = paginationRequest.getLocation();
//        List<UUID> assignedTo = paginationRequest.getAssignedTo();
//        List<UUID> leadStatus = paginationRequest.getLeadStatus();
//        List<UUID> leadPriority = paginationRequest.getLeadPriority();
//        List<UUID> designation = paginationRequest.getDesignation();
//        if(searchKeyword != null && !searchKeyword.isEmpty()){
//            conditions.add(INDIVIDUAL.FIRST_NAME.containsIgnoreCase(searchKeyword)
//                    .or(INDIVIDUAL.LAST_NAME.containsIgnoreCase(searchKeyword))
//                    .or(INDIVIDUAL.EMAIL.containsIgnoreCase(searchKeyword))
//                    .or(INDIVIDUAL.MOBILE_NUMBER.containsIgnoreCase(searchKeyword)
//                    .or(ORGANISATION.NAME.containsIgnoreCase(searchKeyword))));
//        }
//        if(category != null && !category.isEmpty()){
//            conditions.add(ORGANISATION.CATEGORY.eq(category));
//        }
//        if(serviceTypeIds != null && !CollectionUtils.isEmpty(serviceTypeIds)){
//            conditions.add(DSL.condition("ORGANISATION.meta_data -> 'servicesProvided' ??| ARRAY[?] :: text[]",
//                    DSL.val(serviceTypeIds.stream().map(UUID::toString).toArray(String[]::new))));
//        }
//        if(category.equalsIgnoreCase(DefaultOrganisationCategoryEnums.SOCIETY.toString()) && location != null
//                && !CollectionUtils.isEmpty(location)){
//            conditions.add(DSL.field("ORGANISATION.meta_data ->> 'locationId'", String.class)
//                    .cast(UUID.class).in(location));
//        }
//        if(category.equalsIgnoreCase(DefaultOrganisationCategoryEnums.SERVICE_PROVIDER.toString()) && location != null
//                && !CollectionUtils.isEmpty(location)){
//            String[] locationIds = location.stream().map(UUID::toString).toArray(String[]::new);
//            conditions.add(
//                    DSL.condition("EXISTS (select 1 from jsonb_array_elements({0} -> 'spAddresses') " +
//                            "AS elem WHERE elem ->> 'locationId' = ANY ({1} :: varchar[]))"
//                            , ORGANISATION.META_DATA, DSL.val(locationIds, SQLDataType.VARCHAR.getArrayDataType()))
//            );
//        }
//        if(category.equalsIgnoreCase(DefaultOrganisationCategoryEnums.EMPLOYEE.name()) && location != null
//                && !CollectionUtils.isEmpty(location)){
//            conditions.add(DSL.field("INDIVIDUAL.meta_data ->> 'workLocation'", String.class).cast(UUID.class)
//                    .in(location));
//        }
//        if(category.equalsIgnoreCase(DefaultOrganisationCategoryEnums.EMPLOYEE.name())
//                && designation != null && !CollectionUtils.isEmpty(designation)){
//            conditions.add(DSL.field("INDIVIDUAL.meta_data ->> 'designation'", String.class).cast(UUID.class)
//                    .in(designation));
//        }
//        if(assignedTo != null && !CollectionUtils.isEmpty(assignedTo)){
//            conditions.add(DSL.field("ORGANISATION.meta_data->> 'assignedTo'", String.class).cast(UUID.class)
//                    .in(assignedTo));
//        }
//        if(leadStatus != null && !CollectionUtils.isEmpty(leadStatus)){
//            conditions.add(DSL.field("ORGANISATION.meta_data->> 'leadStatus'", String.class).cast(UUID.class)
//                    .in(leadStatus));
//        }
//        if(leadStatus != null && !CollectionUtils.isEmpty(leadPriority)){
//            conditions.add(DSL.field("ORGANISATION.meta_data->> 'leadPriority'", String.class).cast(UUID.class)
//                    .in(leadPriority));
//        }
//        if(!category.equalsIgnoreCase(DefaultOrganisationCategoryEnums.EMPLOYEE.name())
//                && designation != null && !CollectionUtils.isEmpty(designation)){
//            conditions.add(DSL.field("ORGANISATION.meta_data->> 'designation'", String.class).cast(UUID.class)
//                    .in(designation));
//        }
//        if(paginationRequest.isListingEmpanelled()){
//            conditions.add(DSL.field("ORGANISATION.meta_data->> 'isListingEmpanelled'", String.class)
//                    .cast(Boolean.class).eq(Boolean.TRUE));
//        }
//        if(paginationRequest.isMicroSiteEmpanelled()){
//            conditions.add(DSL.field("ORGANISATION.meta_data->> 'isMicrositeEmpanelled'", String.class)
//                    .cast(Boolean.class).eq(Boolean.TRUE));
//        }
//        if(paginationRequest.isStrategicPartner()){
//            conditions.add(DSL.field("ORGANISATION.meta_data->> 'isStrategicPartner'", String.class)
//                    .cast(Boolean.class).eq(Boolean.TRUE));
//        }
//        if(orgName != null && !StringUtils.isEmpty(orgName)){
//            conditions.add(ORGANISATION.NAME.containsIgnoreCase(orgName));
//        }
//        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
//    }
//
//    private void mappingAndInsertingIntoIndividualDerivedEntities(Individual individual, Organisation organisation,
//                                                                  UserPrincipal userPrincipal,
//                                                                  boolean isServiceProvider,
//                                                                  UUID[] servicesProvided) throws JsonProcessingException {
//        UUID roleId = isServiceProvider ? serviceProviderId : societyId;
//        Roles roleEntity = individualHelperMethods.getPermissions(roleId);
//
//        IndividualRole mappedIndividualRole = IndividualMapper.mapIndOrgToIndRole(individual.getId(),
//                organisation.getId(), roleId, userPrincipal.getId());
//        individualRoleRepository.insertIndividualRole(mappedIndividualRole);
//        log.info("Created Individual Role: {}", mappedIndividualRole.getId());
//        IndividualPermission individualPermission = IndividualMapper.mapIndRoleToIndPermissions(individual.getId(),
//                organisation.getId(), roleEntity.getPermissions(), userPrincipal.getId());
//        individualPermissionDao.insert(individualPermission);
//        log.info("Created Individual Permission: {}", individualPermission.getId());
//
//        if (servicesProvided != null) {
//            userServiceProfileService.create(individual);
//            userServiceProfileService.userServicesAddOrRemove(servicesProvided, individual.getId());
//            log.info("Mapped and Created Into User Service Profile Table");
//        }
//
//    }
//
//    private void mappingAndInsertingOrganisationDerivedEntities(Individual individual, Organisation organisation,
//                                                                UserPrincipal userPrincipal) {
//        OrganisationLeadAssignment organisationLeadAssignment = IndividualMapper.organisationLeadAssignment(individual,
//                organisation.getId(), userPrincipal.getId());
//        organisationLeadAssignmentRepository.insertOrganisationLeadAssignment(organisationLeadAssignment);
//        log.info("Created Organisation Lead Assignment: {}", organisationLeadAssignment.getId());
//    }

    public void sendActivationLinkToIndividuals(OrgIndividuals individualDTO, UUID orgId) throws MessagingException {

        Organisation organisation = organizationRepository.getById(orgId);
        List<IndividualVerificationAudit> existingAudits = individualVerificationAuditRepository.getByEmail(individualDTO.getEmail());

        if (!existingAudits.isEmpty()) {
            IndividualVerificationAudit existingAudit = existingAudits.get(0);

            if (existingAudit.getVerificationStatus().equals(VerificationStatusEnum.VERIFIED.name())) {
                log.warn("Email already verified. Please login.");
            } else if (existingAudit.getVerificationStatus().equals(VerificationStatusEnum.PENDING.name()) &&
                    existingAudit.getActivationLinkExpiresAt().isAfter(LocalDateTime.now())) {
                log.warn("Activation link already sent. Please check your email.");
            } else {
                log.info("Activation link expired. Resending activation link.");
                String activationLink = emailHelperService.generateActivationLink(individualDTO.getEmail(), existingAudit.getRoleId());
                notificationManager.sendActivationLink(individualDTO.getEmail(), activationLink, organisation.getName());
                return;
            }
        }
        IndividualVerificationAudit newAudit = individualVerificationMapper.createIndividualVerificationAudit(individualDTO, organisation);
        log.info("Inserting new individual verification record.");
        individualVerificationAuditRepository.insertIndividualVerificationAudit(newAudit);

        log.info("Generating activation link.");
        String activationLink = emailHelperService.generateActivationLink(individualDTO.getEmail(), newAudit.getRoleId());

        log.info("Sending activation link to registered email.");
        notificationManager.sendActivationLink(individualDTO.getEmail(), activationLink, organisation.getName());
    }

    public void editIndividual(OrgIndividuals orgIndividuals, UserPrincipal userPrincipal) {
        Individual individual = individualRepository.getById(orgIndividuals.getIndividualId());
        if (orgIndividuals.getName() != null) {
            individual.setName(orgIndividuals.getName());
        }
        if (orgIndividuals.getMobileNumber() != null) {
            individual.setMobileNumber(orgIndividuals.getMobileNumber());
        }
        if (orgIndividuals.getIsActive() != null) {
            individual.setIsActive(orgIndividuals.getIsActive());
        }
        individual.setUpdatedBy(userPrincipal.getId());
        individual.setUpdatedOn(DateUtils.currentTimeIST());
        if (orgIndividuals.getRoleId() != null) {
            List<IndividualRole> individualRole = individualRoleRepository.getByIndivdiualId(individual.getId());
            individualRole.get(0).setRoleId(orgIndividuals.getRoleId());
            individualRoleRepository.upsertIndividualRoles(individualRole);
        }

        individualRepository.update(individual);
    }


    public GetAllDonors getAllIndividualsDonor(PaginationRequest paginationRequest, UserPrincipal userPrincipal) {
        applyDefaults(paginationRequest);
        Condition finalCondition = getSearchingCondition(paginationRequest);
        List<SortField<?>> sortFields = new ArrayList<>();
        List<DonorsResponse> allIndividualsDonors = individualRepository.getAllIndividualsDonors(finalCondition, sortFields,
                paginationRequest, userPrincipal);

        Integer count = individualRepository.getIndividualsCountDonor(finalCondition, userPrincipal);
        return GetAllDonors.builder()
                .donorsResponses(allIndividualsDonors)
                .count(count)
                .build();
    }

}
