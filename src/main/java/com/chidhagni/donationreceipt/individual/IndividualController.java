package com.chidhagni.donationreceipt.individual;

import com.chidhagni.donationreceipt.individual.dto.request.*;
import com.chidhagni.donationreceipt.individual.dto.response.GetAllIndividualsResponse;
import com.chidhagni.donationreceipt.organisation.dto.response.GetAllDonors;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.mail.MessagingException;
import java.util.UUID;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import static com.chidhagni.donationreceipt.individual.constants.IndividualMetaData.INDIVIDUAL_GET_RES_V1;


@RestController
@RequestMapping("/api/v1/individual")
@RequiredArgsConstructor
public class IndividualController {
    private final IndividualService individualService;

    @PostMapping(value = "/all")
    public ResponseEntity<GetAllIndividualsResponse> getAllIndividuals(
            @RequestBody(required = false) PaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = PaginationRequest.builder().build();
        }
        return new ResponseEntity<>(individualService.getAllIndividuals(paginationRequest,userPrincipal), HttpStatus.OK);
    }
    @GetMapping(value= "/{individualId}",produces = INDIVIDUAL_GET_RES_V1)
    public ResponseEntity<IndividualDTO> getIndividualById(@PathVariable UUID individualId) {
        IndividualDTO individualDTO = individualService.getByIndividualId(individualId);
        if (individualDTO != null) {
            return ResponseEntity.ok(individualDTO);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
//
//    /**
//     * Retrieves details of an individual by their unique identifier.
//     *
//     * <p>This endpoint allows authorized users to fetch complete details of a specific individual,
//     * including personal information, assigned roles, permissions, and organization details.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Fetches the individual entity using the provided `individualId`.</li>
//     *   <li>Validates whether the individual exists; if not, an exception is thrown.</li>
//     *   <li>Retrieves the list of roles assigned to the individual.</li>
//     *   <li>Extracts the role of the individual.</li>
//     *   <li>Fetches the permissions associated with the individual's roles.</li>
//     *   <li>Determines the organization to which the individual belongs.</li>
//     *   <li>Maps all retrieved data into a structured `IndividualDTO` object.</li>
//     * </ul>
//     *
//     * <p><b>Where Conditions:</b></p>
//     * <ul>
//     *   <li>Filters the individual based on their unique `individualId`.</li>
//     *   <li>Fetches roles and permissions associated with the individual.</li>
//     *   <li>Determines the organization of the individual using their individual role details.</li>
//     * </ul>
//     *
//     * <p><b>Notes:</b></p>
//     * <ul>
//     *   <li>Returns `404 Not Found` if the individual does not exist.</li>
//     *   <li>Throws an exception if the individual has no assigned roles.</li>
//     *   <li>Includes permission details associated with the individual’s role.</li>
//     * </ul>
//     *
//     * @param userPrincipal logged in user details.
//     * @return A `ResponseEntity` containing an `IndividualDTO` with individual details, role, permissions
//     * , and organization information.
//     * <AUTHOR>
//     */
    @GetMapping(value = "/permissions", produces = INDIVIDUAL_GET_RES_V1)
    public ResponseEntity<IndividualDTO> getIndividualById(@CurrentUser UserPrincipal userPrincipal) {
        IndividualDTO individualDTO = individualService.getByIndividualId(userPrincipal.getId());
        if (individualDTO != null) {
            return ResponseEntity.ok(individualDTO);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
//
//    /**
//     * Updates the role and permissions of an individual.
//     *
//     * <p>This endpoint allows authorized users to modify the assigned role and permissions of a specific individual.
//     * The update includes:
//     * <ul>
//     *   <li>Updating the individual's assigned role.</li>
//     *   <li>Updating the individual's associated permissions.</li>
//     * </ul>
//     * </p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Fetches the list of roles assigned to the individual using `individualId`.</li>
//     *   <li>Validates whether the individual has at least one assigned role; if not, an exception is thrown.</li>
//     *   <li>Updates the role of the individual based on the request payload.</li>
//     *   <li>Fetches the list of permissions associated with the individual.</li>
//     *   <li>Validates whether the individual has existing permissions; if not, an exception is thrown.</li>
//     *   <li>Updates the permissions of the individual as per the request payload.</li>
//     *   <li>Logs a success message indicating the successful update.</li>
//     * </ul>
//     *
//     * <p><b>Notes:</b></p>
//     * <ul>
//     *   <li>Throws an exception if the individual has no assigned roles or permissions.</li>
//     *   <li>Ensures that the update only modifies existing roles and permissions.</li>
//     *   <li>Logs messages for successful updates.</li>
//     * </ul>
//     *
//     * @param individualId The unique identifier of the individual whose role and permissions are being updated.
//     * @param individualRequest The request payload containing the new role and permissions.
//     * @return A `ResponseEntity` containing the updated `IndividualRequest`.
//     * @throws IllegalArgumentException If the individual has no assigned roles or permissions.
//     * <AUTHOR>
//     */
    @PutMapping(value= "/{individualId}/role-permissions")
    public ResponseEntity<IndividualRequest> updateRoleAndPermissions(
            @PathVariable UUID individualId,
            @RequestBody IndividualRequest individualRequest) {
        individualService.updateRoleAndPermissionsInIndividual(individualId, individualRequest);
        return ResponseEntity.ok(individualRequest);
    }
//
//    /**
//     * Activates an individual by updating their status.
//     *
//     * <p>This endpoint allows authorized users to activate an individual by setting their `isActive` status to `true`.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Fetches the individual entity using the provided `individualId`.</li>
//     *   <li>Validates whether the individual exists; if not, an exception is thrown.</li>
//     *   <li>Logs a message confirming the availability of the individual.</li>
//     *   <li>Updates the individual's status to active (`isActive = true`).</li>
//     *   <li>Persists the changes in the database.</li>
//     *   <li>Logs a success message indicating the activation.</li>
//     * </ul>
//     *
//     * <p><b>Notes:</b></p>
//     * <ul>
//     *   <li>Throws an exception if the individual does not exist.</li>
//     *   <li>Ensures that only existing individuals are activated.</li>
//     *   <li>Logs messages for debugging and tracking.</li>
//     * </ul>
//     *
//     * @param individualId The unique identifier of the individual to be activated.
//     * @throws IllegalArgumentException If the individual does not exist.
//     * <AUTHOR>
//     */
    @PatchMapping(path = {"/activate/{individualId}"})
    @ResponseStatus(HttpStatus.OK)
    public void activateIndividual(@PathVariable("individualId")UUID individualId)
    {
        individualService.activateIndividual(individualId);
    }
//
//    /**
//     * De-Activates an individual by updating their status.
//     *
//     * <p>This endpoint allows authorized users to de-activate an individual by setting their `isActive` status to `true`.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Fetches the individual entity using the provided `individualId`.</li>
//     *   <li>Validates whether the individual exists; if not, an exception is thrown.</li>
//     *   <li>Logs a message confirming the availability of the individual.</li>
//     *   <li>Updates the individual's status to inactive (`isActive = false`).</li>
//     *   <li>Persists the changes in the database.</li>
//     *   <li>Logs a success message indicating the de-activation.</li>
//     * </ul>
//     *
//     * <p><b>Notes:</b></p>
//     * <ul>
//     *   <li>Throws an exception if the individual does not exist.</li>
//     *   <li>Ensures that only existing individuals are activated.</li>
//     *   <li>Logs messages for debugging and tracking.</li>
//     * </ul>
//     *
//     * @param individualId The unique identifier of the individual to be activated.
//     * @throws IllegalArgumentException If the individual does not exist.
//     * <AUTHOR>
//     */
    @PatchMapping(path = {"/deactivate/{individualId}"})
    @ResponseStatus(HttpStatus.OK)
    public void deactivateIndividual(@PathVariable("individualId")UUID individualId)
    {
        individualService.deActivateIndividual(individualId);
    }


    @PostMapping(value = "/all/donors")
    public ResponseEntity<GetAllDonors> getAllIndividualsDonors(
            @RequestBody(required = false) PaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = PaginationRequest.builder().build();
        }
        return new ResponseEntity<>(individualService.getAllIndividualsDonor(paginationRequest,userPrincipal), HttpStatus.OK);
    }
}
