package com.chidhagni.donationreceipt.individual.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndividualDTOForContactTYpe {
    private UUID id;
    private String firstName;
    private String lastName;
    private UUID contactType;
    private String mobileNumber;
    private String email;
}