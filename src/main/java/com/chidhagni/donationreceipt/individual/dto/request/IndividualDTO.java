package com.chidhagni.donationreceipt.individual.dto.request;

import com.chidhagni.donationreceipt.organisation.constants.DefaultOrganisationCategoryEnums;
import com.chidhagni.donationreceipt.organisation.constants.OrganisationEnums;
import com.chidhagni.donationreceipt.roles.dto.request.RoleNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualDTO {

    private UUID id;
    private UUID roleId;
    private UUID orgId;
    private OrganisationEnums organisationCategory;
    private List<RoleNode> permissionsDTOList;

    private String email;
    private String name;
    private String mobileNumber;

}
