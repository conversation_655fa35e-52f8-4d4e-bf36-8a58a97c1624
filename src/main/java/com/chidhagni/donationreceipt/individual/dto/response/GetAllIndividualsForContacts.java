package com.chidhagni.donationreceipt.individual.dto.response;

import java.util.List;
import com.chidhagni.donationreceipt.individual.dto.IndividualDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetAllIndividualsForContacts {
    private List<IndividualDTO> individuals;
    private Integer rowCount;

}
