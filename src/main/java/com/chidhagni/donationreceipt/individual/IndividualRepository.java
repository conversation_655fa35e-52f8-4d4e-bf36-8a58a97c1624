package com.chidhagni.donationreceipt.individual;

//import com.chidhagni.houzer.common.exception.InternalServerError;
//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualDao;
//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualPermissionDao;
//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualRoleDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Individual;
//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualPermission;
//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualRole;
//import com.chidhagni.houzer.individual.dto.IndividualDTO;
//import com.chidhagni.houzer.individual.dto.request.ContactsGroupPaginationRequest;
//import com.chidhagni.houzer.individual.dto.request.EmployeeDTO;
//import com.chidhagni.houzer.individual.dto.request.IndividualsDTO;
import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPermissionDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.individual.dto.request.PaginationRequest;
import com.chidhagni.donationreceipt.individual.dto.response.IndividualResponseDTO;
import com.chidhagni.donationreceipt.organisation.dto.response.DonorsResponse;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;
import static com.chidhagni.donationreceipt.db.jooq.tables.Roles.ROLES;

import java.util.List;
import java.util.UUID;
//import static com.chidhagni.houzer.db.jooq.tables.Individual.INDIVIDUAL;
//import static com.chidhagni.houzer.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
//import static com.chidhagni.houzer.db.jooq.tables.Organisation.ORGANISATION;
//import static com.chidhagni.houzer.db.jooq.tables.OrganisationLeadAssignment.ORGANISATION_LEAD_ASSIGNMENT;
//import static com.chidhagni.houzer.db.jooq.tables.Roles.ROLES;

@lombok.extern.slf4j.Slf4j
@Slf4j
@Repository
@RequiredArgsConstructor
public class IndividualRepository {
    private final IndividualDao individualDao;
    private final IndividualRoleDao individualRoleDao;
    private final IndividualPermissionDao individualPermissionDao;

    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;


    @Value("${roles.donor}")
    private UUID donorRoleId;
//
//
//    @Value("${houzer.employee.company.name}")
//    private String houzerCompanyName;
//
//
//    /**
//     * Retrieves a paginated list of individuals based on specified conditions.
//     *
//     * <p>This method queries the database to fetch a list of individuals along with their associated details
//     * such as name, email, mobile number, creation and update timestamps, role, and organization.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Computes the page number based on the pagination request.</li>
//     *   <li>Executes a database query to fetch individual details.</li>
//     *   <li>Uses `LEFT JOIN` to include related data such as roles and organization.</li>
//     *   <li>Applies filtering conditions specified in the request.</li>
//     *   <li>Sorts the results based on specified sorting fields.</li>
//     *   <li>Maps the fetched results into `IndividualResponseDTO`.</li>
//     * </ul>
//     *
//     * @param condition The filtering condition for retrieving individuals.
//     * @param sortFields The sorting criteria.
//     * @param paginationRequest The pagination parameters.
//     * @return A list of `IndividualResponseDTO` objects containing individual details.
//     * @throws InternalServerError If an error occurs during query execution.
//     */
    public List<IndividualResponseDTO> getAllIndividuals(Condition condition, List<SortField<?>> sortFields,
                                                         PaginationRequest paginationRequest,
                                                         UserPrincipal userPrincipal) {
        Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
        try {

            UUID loggedUser = userPrincipal.getId();
            List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(loggedUser);

            Condition finalCondition = condition != null ? condition : DSL.noCondition();

            boolean isTenantAdmin = individualRoles.stream()
                    .anyMatch(role -> role.getRoleId().equals(tenantAdminRoleId));

            UUID orgId = null;
            if (isTenantAdmin) {

                orgId = individualRoles.get(0).getOrgId();
                finalCondition = finalCondition.and(INDIVIDUAL_ROLE.ORG_ID.eq(orgId));
            }


            return individualDao.ctx().select(
                            INDIVIDUAL.ID.as("id"),
                            INDIVIDUAL.NAME.as("name"),
                            INDIVIDUAL.EMAIL.as("email"),
                            INDIVIDUAL.MOBILE_NUMBER.as("mobileNumber"),
                            INDIVIDUAL.CREATED_ON.as("createdOn"),
                            INDIVIDUAL.UPDATED_ON.as("updatedOn"),
                            INDIVIDUAL.as("r0").EMAIL.as("createdBy"),
                            INDIVIDUAL.as("r1").EMAIL.as("updatedBy"),
                            INDIVIDUAL_ROLE.ROLE_ID.as("roleId"),
                            ROLES.NAME.as("roleName"),
                            INDIVIDUAL_ROLE.ORG_ID.as("organisationId"),
                            INDIVIDUAL.IS_ACTIVE.as("isActive")
                    )
                    .from(INDIVIDUAL)
                    .leftJoin(INDIVIDUAL.as("r0")).on(INDIVIDUAL.CREATED_BY.eq(INDIVIDUAL.as("r0").ID))
                    .leftJoin(INDIVIDUAL.as("r1")).on(INDIVIDUAL.UPDATED_BY.eq(INDIVIDUAL.as("r1").ID))
                    .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .leftJoin(ROLES).on(ROLES.ID.eq(INDIVIDUAL_ROLE.ROLE_ID))
                    .where(finalCondition)
                    .orderBy(sortFields)
                    .limit(paginationRequest.getPageSize())
                    .offset(pageNo)
                    .fetchInto(IndividualResponseDTO.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching all Individuals", ex);
        }
    }

//    /**
//     * Retrieves the count of individuals based on a given condition.
//     *
//     * <p>This method counts the number of individuals that match the specified filtering conditions.</p>
//     *
//     * @param condition The filtering condition for counting individuals.
//     * @return The total count of matching individuals.
//     * @throws InternalServerError If any error occurs while fetching the count.
//     */
public Integer getIndividualsCount(Condition condition, UserPrincipal userPrincipal) {
    try {
        UUID loggedUser = userPrincipal.getId();
        List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(loggedUser);

        Condition finalCondition = condition != null ? condition : DSL.noCondition();

        boolean isTenantAdmin = individualRoles.stream()
                .anyMatch(role -> role.getRoleId().equals(tenantAdminRoleId));

        if (isTenantAdmin) {
            UUID orgId = individualRoles.get(0).getOrgId();
            finalCondition = finalCondition.and(INDIVIDUAL_ROLE.ORG_ID.eq(orgId));
        }

        return individualDao.ctx()
                .selectCount()
                .from(INDIVIDUAL)
                .leftJoin(INDIVIDUAL.as("r0")).on(INDIVIDUAL.CREATED_BY.eq(INDIVIDUAL.as("r0").ID))
                .leftJoin(INDIVIDUAL.as("r1")).on(INDIVIDUAL.UPDATED_BY.eq(INDIVIDUAL.as("r1").ID))
                .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                .leftJoin(ROLES).on(ROLES.ID.eq(INDIVIDUAL_ROLE.ROLE_ID))
                .where(finalCondition)
                .fetchOne(0, Integer.class);
    } catch (Exception ex) {
        throw new InternalServerError("Exception occurred while fetching all Individuals count", ex);
    }
}

//
//    /**
//     * Retrieves an individual by their unique ID.
//     *
//     * <p>This method fetches an individual from the database using their ID.</p>
//     *
//     * @param id The unique identifier of the individual.
//     * @return An `Individual` object representing the fetched individual.
//     * @throws InternalServerError If an error occurs while fetching the individual.
//     */

    /**
     * Retrieves an individual by their unique ID.
     *
     * <p>This method fetches an individual from the database using their ID.</p>
     *
     * @param id The unique identifier of the individual.
     * @return An `Individual` object representing the fetched individual.
     * @throws InternalServerError If an error occurs while fetching the individual.
     */

    public Individual getById(UUID id)
    {
        Individual individual= null;
        try {
            individual=individualDao.fetchOneById(id);
        }catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching individual by it's id", ex);
        }
        return individual;
    }
//
    /**
     * Retrieves the roles assigned to an individual.
     *
     * <p>This method fetches the individual roles associated with a given individual ID.</p>
     *
     * @param id The unique identifier of the individual.
     * @return A list of `IndividualRole` objects representing the assigned roles actually list of size 1.
     * @throws InternalServerError If an error occurs while fetching roles.
     */
    public List<IndividualRole> getIndividualRoleByIndividualId(UUID id)
    {
        List<IndividualRole> individualRoleList;
        try {
            individualRoleList=individualRoleDao.fetchByIndividualId(id);
        }catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching individual in individual role by it's id", ex);
        }
        return individualRoleList;
    }
//
//    /**
//     * Retrieves the individual permissions record assigned to an individual.
//     *
//     * @param id
//     * @return A list of `IndividualPermission` rows actually the list of size 1.
//     */
    public List<IndividualPermission> getIndividualPermissionsByIndividualId(UUID id)
    {
        List<IndividualPermission> individualPermissionList;
        try {
            individualPermissionList=individualPermissionDao.fetchByIndividualId(id);
        }catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching individual in individual role by it's id", ex);
        }
        return individualPermissionList;
    }
//
//
//    /**
//     * Retrieves a list of employees associated with a specific organization.
//     *
//     * <p>This method fetches employee details based on their association with a given organization.</p>
//     *
//     * @return A list of `EmployeeDTO` objects containing employee details.
//     */
//    public List<EmployeeDTO> getEmployees() {
//        return individualDao.ctx()
//                .select(
//                        INDIVIDUAL.ID,
//                        DSL.concat(
//                                INDIVIDUAL.FIRST_NAME,
//                                DSL.inline(" "),
//                                DSL.coalesce(INDIVIDUAL.LAST_NAME, DSL.inline(""))
//                        ).as("name")
//                )
//                .from(INDIVIDUAL)
//                .join(INDIVIDUAL_ROLE)
//                .on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
//                .where(INDIVIDUAL_ROLE.ORG_ID.in(
//                        individualDao.ctx().select(ORGANISATION.ID)
//                                .from(ORGANISATION)
//                                .where(ORGANISATION.NAME.eq(houzerCompanyName))
//                ))
//                .orderBy(INDIVIDUAL.FIRST_NAME)
//                .fetch()
//                .map(individuals -> new EmployeeDTO(
//                        individuals.get(INDIVIDUAL.ID),
//                        individuals.get("name", String.class)
//                ));
//    }
//
//    /**
//     * fetch the individuals belong to given organisation id
//     *
//     * @param orgId
//     * @return llist of individualsSDTO objects who belong to the given organization
//     */
//    public List<IndividualsDTO> getIndividualByOrganizations(UUID orgId)
//    {
//        List<UUID> individualIds = individualDao.ctx()
//                .select(INDIVIDUAL_ROLE.INDIVIDUAL_ID)
//                .from(INDIVIDUAL_ROLE)
//                .where(INDIVIDUAL_ROLE.ORG_ID.eq(orgId))
//                .fetch()
//                .getValues(INDIVIDUAL_ROLE.INDIVIDUAL_ID, UUID.class);
//
//
//        return individualDao.ctx().select(
//                        INDIVIDUAL.ID,
//                        DSL.concat(
//                                INDIVIDUAL.FIRST_NAME,
//                                DSL.inline(" "),
//                                DSL.coalesce(INDIVIDUAL.LAST_NAME, DSL.inline(""))
//                        ).as("name"))
//                .from(INDIVIDUAL)
//                .where(INDIVIDUAL.ID.in(individualIds))
//                .orderBy(INDIVIDUAL.FIRST_NAME)
//                .fetch()
//                .map(individuals ->new IndividualsDTO(
//                                individuals.get(INDIVIDUAL.ID,UUID.class),
//                                individuals.get("name",String.class)
//                        )
//                );
//    }
//
//    /**
//     * Retrieves an individual by their email address.
//     *
//     * <p>This method fetches an individual based on their registered email.</p>
//     *
//     * @param email The email address of the individual.
//     * @return An `Individual` object representing the fetched individual.
//     * @throws InternalServerError If an error occurs while fetching the individual by email.
//     */
    public Individual getByEmail(String email) {
        Individual individual = null;
        try {
            individual = individualDao.fetchOneByEmail(email);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching individual by email", ex);
        }
        return individual;
    }
//
//    /**
//     * Checks if an individual exists based on their ID.
//     *
//     * <p>This method verifies whether an individual exists in the database.</p>
//     *
//     * @param id The unique identifier of the individual.
//     * @return `true` if the individual exists, `false` otherwise.
//     */
    public boolean isIndividualExist(UUID id){
        return individualDao.ctx().fetchExists(individualDao.ctx().select(INDIVIDUAL.ID).from(INDIVIDUAL)
                .where(INDIVIDUAL.ID.eq(id)));
    }
//
//    /**
//     * Block of code to insert the individuals if they are not exist and update if they are exist in the database
//     * checks by the individual id.
//     *
//     * @param individuals
//     */
//    public void upsertIndividuals(List<Individual> individuals) {
//        try {
//            for (Individual individual : individuals) {
//                if (individual.getId() != null && isIndividualExist(individual.getId())) {
//                    Individual existing = individualDao.fetchOneById(individual.getId());
//                    if (existing != null && existing.getPassword() != null) {
//                        individual.setPassword(existing.getPassword());
//                    }
//                }
//            }
//            individualDao.merge(individuals);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while upsert individuals", ex);
//        }
//    }
//
//
//    /**
//     * Block of code to return the individuals associated by the given organisation(society) id.
//     *
//     * @param orgId
//     * @return details of society committee members of the given organization(society) unique identifier
//     */
//      /*
//        This Method is used for retrieving individual metaData and mapping it into SocietyCommitteeMemberInformation
//       */
//      public List<SocietyCommitteeMemberInformation> getSocietyIndividualByOrganizations(UUID orgId)
//                      {
//
//                          List<UUID> individualIds=new ArrayList<>();
//                          individualIds = individualDao.ctx()
//                                  .select(INDIVIDUAL_ROLE.INDIVIDUAL_ID)
//                                  .from(INDIVIDUAL_ROLE)
//                                  .where(INDIVIDUAL_ROLE.ORG_ID.eq(orgId))
//                                  .fetch()
//                                  .getValues(INDIVIDUAL_ROLE.INDIVIDUAL_ID, UUID.class);
//                          List<UUID> societyCommitteeMembersUUID = individualDao.ctx()
//                                  .select(INDIVIDUAL.ID)
//                                  .from(INDIVIDUAL)
//                                  .where(DSL.field("meta_data->>'loggedInSocietyMemberIndividualId'").eq(individualIds.get(0).toString()))
//                                  .fetch()
//                                  .getValues(INDIVIDUAL.ID, UUID.class);
//
//
//                          individualIds.addAll(societyCommitteeMembersUUID);
//                          return individualDao.ctx().select(
//                                          INDIVIDUAL.ID,
//                                          DSL.concat(
//                                                  INDIVIDUAL.FIRST_NAME,
//                                                  DSL.inline(" "),
//                                                  DSL.coalesce(INDIVIDUAL.LAST_NAME, DSL.inline(""))
//                                          ).as("name"),
//                                          INDIVIDUAL.EMAIL,
//                                          INDIVIDUAL.MOBILE_NUMBER.as("contactNumber"),
//                                          INDIVIDUAL.IS_ACTIVE.as("isActive"),
//                                          DSL.field("meta_data->>'designation'").as("designation"),
//                                          DSL.field("meta_data->>'alternateNumber'").as("alternateNumber"),
//                                          DSL.field("meta_data->>'fromDate'").as("fromDate"),
//                                          DSL.field("meta_data->>'toDate'").as("toDate")
//                                  )
//                                  .from(INDIVIDUAL)
//                                  .where(INDIVIDUAL.ID.in(individualIds)
//                                  )
//                                  .orderBy(INDIVIDUAL.FIRST_NAME)
//                                  .fetch()
//                                  .map(individuals -> {
//                                      UUID designation = null;
//                                      String designationString = individuals.get("designation", String.class);
//                      if (designationString != null) {
//                          designation = UUID.fromString(designationString);
//                      }
//                      return new SocietyCommitteeMemberInformation(
//                              individuals.get(INDIVIDUAL.ID, UUID.class),
//                              individuals.get("name", String.class),
//                              designation,
//                              individuals.get("contactNumber", String.class),
//                              individuals.get("alternateNumber", String.class),
//                              individuals.get(INDIVIDUAL.EMAIL, String.class),
//                              individuals.get("fromDate", String.class),
//                              individuals.get("toDate", String.class),
//                              individuals.get("isActive", Boolean.class)
//                      );
//                  });
//
//      }
//
//    public List<Individual> getByMobileNumber(String mobileNumber) {
//        List<Individual> individual = null;
//        try {
//            individual = individualDao.fetchByMobileNumber(mobileNumber);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching individual by email", ex);
//        }
//        return individual;
//    }
//
//
      public Individual insertIndividual(Individual individual){
          try{
              individualDao.insert(individual);
          }catch (Exception ex){
              throw new InternalServerError("Exception while inserting the individual", ex);
          }
          return individual;
      }
//
//
    public void update(Individual individual) {

          try{
              individualDao.update(individual);
          }
          catch (Exception ex){
              throw new InternalServerError("Exception while updating the individual", ex);
          }
        }
//
//    public List<IndividualResponseDTO> getAllIndividualsByEmployee(Condition condition
//            , List<SortField<?>> sortFields, PaginationRequest paginationRequest) {
//        Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
//        try {
//
//            return individualDao.ctx().select(INDIVIDUAL.ID.as("id"),
//                            INDIVIDUAL.FIRST_NAME.as("firstName"),
//                            INDIVIDUAL.LAST_NAME.as("lastName"),
//                            INDIVIDUAL.EMAIL.as("email"),
//                            INDIVIDUAL.MOBILE_NUMBER.as("mobileNumber"),
//                            INDIVIDUAL.CREATED_ON.as("createdOn"),
//                            INDIVIDUAL.UPDATED_ON.as("updatedOn"),
//                            INDIVIDUAL.as("r0").EMAIL.as("createdBy"),
//                            INDIVIDUAL.as("r1").EMAIL.as("updatedBy"),
//                            INDIVIDUAL_ROLE.ROLE_ID.as("roleId"),
//                            ROLES.NAME.as("roleName"),
//                            INDIVIDUAL_ROLE.ORG_ID.as("organisationId"),
//                            INDIVIDUAL.IS_ACTIVE.as("isActive"),
//                            ORGANISATION_LEAD_ASSIGNMENT.ASSIGNED_TO.as("assignedTo")
//                    ).from(INDIVIDUAL)
//                    .leftJoin(INDIVIDUAL.as("r0")).on(INDIVIDUAL.CREATED_BY.eq(INDIVIDUAL.as("r0").ID))
//                    .leftJoin(INDIVIDUAL.as("r1")).on(INDIVIDUAL.UPDATED_BY.eq(INDIVIDUAL.as("r1").ID))
//                    .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
//                    .leftJoin(ROLES).on(ROLES.ID.eq(INDIVIDUAL_ROLE.ROLE_ID))
//                    .leftJoin(ORGANISATION_LEAD_ASSIGNMENT).on(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(INDIVIDUAL_ROLE.ORG_ID))
//                    .where(condition)
//                    .orderBy(sortFields)
//                    .limit(paginationRequest.getPageSize())
//                    .offset(pageNo)
//                    .fetchInto(IndividualResponseDTO.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching all Individuals", ex);
//        }
//    }
//
//    public Integer getIndividualsCountByEmployee(Condition condition) {
//        try {
//            return individualDao.ctx()
//                    .selectCount()
//                    .from(INDIVIDUAL)
//                    .leftJoin(INDIVIDUAL.as("r0")).on(INDIVIDUAL.CREATED_BY.eq(INDIVIDUAL.as("r0").ID))
//                    .leftJoin(INDIVIDUAL.as("r1")).on(INDIVIDUAL.UPDATED_BY.eq(INDIVIDUAL.as("r1").ID))
//                    .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
//                    .leftJoin(ROLES).on(ROLES.ID.eq(INDIVIDUAL_ROLE.ROLE_ID))
//                    .leftJoin(ORGANISATION_LEAD_ASSIGNMENT).on(ORGANISATION_LEAD_ASSIGNMENT.ORGANISATION_ID.eq(INDIVIDUAL_ROLE.ORG_ID))
//                    .where(condition)
//                    .fetchOne(0, Integer.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching all Individuals count", ex);
//        }
//    }
//
//    public List<Individual> getAllIndividualsByOrgId(List<UUID> organisationIds) {
//        try {
//            return individualRoleDao.ctx()
//                    .select(INDIVIDUAL.fields())
//                    .from(INDIVIDUAL)
//                    .join(INDIVIDUAL_ROLE).on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
//                    .where(INDIVIDUAL_ROLE.ORG_ID.in(organisationIds))
//                    .fetchInto(Individual.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching individual entities by organisation IDs", ex);
//        }
//    }
//
//    public List<ServiceProviderIndividuals> getServiceProviderIndividualByOrganizations(UUID orgId)
//    {
//        UUID loggedInMemberId = null;
//        List<UUID> individualIds=new ArrayList<>();
//        individualIds = individualDao.ctx()
//                .select(INDIVIDUAL_ROLE.INDIVIDUAL_ID)
//                .from(INDIVIDUAL_ROLE)
//                .where(INDIVIDUAL_ROLE.ORG_ID.eq(orgId))
//                .fetch()
//                .getValues(INDIVIDUAL_ROLE.INDIVIDUAL_ID, UUID.class);
//
//        if(individualIds != null && !CollectionUtils.isEmpty(individualIds)){
//            loggedInMemberId = individualIds.get(0);
//            individualIds.remove(loggedInMemberId);
//        }
//        List<UUID> serviceProviderUUID = individualDao.ctx()
//                .select(INDIVIDUAL.ID)
//                .from(INDIVIDUAL)
//                .where(DSL.field("meta_data->>'loggedInSocietyMemberIndividualId'").eq(loggedInMemberId.toString()))
//                .fetch()
//                .getValues(INDIVIDUAL.ID, UUID.class);
//        individualIds.addAll(serviceProviderUUID);
//        if(individualIds.contains(loggedInMemberId)) {
//            individualIds.remove(loggedInMemberId);
//        }
//        return individualDao.ctx().select(
//                        INDIVIDUAL.ID,
//                        DSL.concat(
//                                INDIVIDUAL.FIRST_NAME,
//                                DSL.inline(" "),
//                                DSL.coalesce(INDIVIDUAL.LAST_NAME, DSL.inline(""))
//                        ).as("name"),
//                        INDIVIDUAL.EMAIL,
//                        INDIVIDUAL.MOBILE_NUMBER.as("contactNumber"),
//                        INDIVIDUAL.IS_ACTIVE.as("isActive"),
//                        DSL.field("meta_data->>'otherDesignation'").as("designation"),
//                        DSL.field("meta_data->>'alternateNumber'").as("alternateNumber"),
//                        DSL.field("meta_data->>'fromDate'").as("fromDate"),
//                        DSL.field("meta_data->>'toDate'").as("toDate")
//                )
//                .from(INDIVIDUAL)
//                .where(INDIVIDUAL.ID.in(individualIds)
//                )
//                .orderBy(INDIVIDUAL.FIRST_NAME)
//                .fetch()
//                .map(individuals -> {
//                    String designationString = individuals.get("designation", String.class);
//                    return new ServiceProviderIndividuals(
//                            individuals.get(INDIVIDUAL.ID, UUID.class),
//                            individuals.get("name", String.class),
//                            individuals.get("contactNumber", String.class),
//                            individuals.get("alternateNumber", String.class),
//                            individuals.get(INDIVIDUAL.EMAIL, String.class),
//                            designationString,
//                            individuals.get("isActive", Boolean.class),
//                            individuals.get("fromDate", String.class),
//                            individuals.get("toDate", String.class)
//                    );
//                });
//    }
//
//    public String getIndividualNameById(UUID id){
//          try{
//                return individualDao.ctx().select(
//                        DSL.concat(INDIVIDUAL.FIRST_NAME,DSL.inline(" ")
//                                ,DSL.coalesce(INDIVIDUAL.LAST_NAME,DSL.inline(""))))
//                        .from(INDIVIDUAL)
//                        .where(INDIVIDUAL.ID.eq(id))
//                        .limit(1)
//                        .fetchOneInto(String.class);
//          }catch(Exception ex){
//              throw new InternalServerError("Exception while fetching the individual name by id", ex);
//          }
//    }
//
//    public GetAllIndividualsForContacts getAllIndividualsForContacts(Condition condition, ContactsGroupPaginationRequest paginationRequest) {
//          try{
//              int offset = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
//              String dateQuery = "TO_CHAR({0} AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'YYYY-MM-DD HH24:MI:SS')";
//
//              List<IndividualDTO> individualList = individualDao.ctx().select(
//                              INDIVIDUAL.ID.as("id"),
//                              INDIVIDUAL.FIRST_NAME.as("firstName"),
//                              INDIVIDUAL.LAST_NAME.as("lastName"),
//                              ORGANISATION.CATEGORY.as("contactType"),
//                              INDIVIDUAL.MOBILE_NUMBER.as("mobileNumber"),
//                              INDIVIDUAL.EMAIL.as("email"),
//                              DSL.field(dateQuery, String.class, INDIVIDUAL.CREATED_ON).as("createdOn"),
//                              DSL.field(dateQuery, String.class, INDIVIDUAL.UPDATED_ON).as("updatedOn"),
//                              INDIVIDUAL.as("m2").EMAIL.as("createdBy"),
//                              INDIVIDUAL.as("m3").EMAIL.as("updatedBy"),
//                              ORGANISATION.NAME.as("orgName"))
//                      .from(INDIVIDUAL)
//                      .leftOuterJoin(INDIVIDUAL.as("m2")).on(INDIVIDUAL.CREATED_BY.eq(INDIVIDUAL.as("m2").ID))
//                      .leftOuterJoin(INDIVIDUAL.as("m3")).on(INDIVIDUAL.UPDATED_BY.eq(INDIVIDUAL.as("m3").ID))
//                      .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(INDIVIDUAL.ID))
//                      .leftJoin(ORGANISATION).on(ORGANISATION.ID.eq(INDIVIDUAL_ROLE.ORG_ID))
//                      .where(condition)
//                      .orderBy(INDIVIDUAL.CREATED_ON.desc())
//                      .limit(paginationRequest.getPageSize())
//                      .offset(offset)
//                      .fetchInto(IndividualDTO.class);
//              int count = individualDao.ctx().selectCount()
//                          .from(INDIVIDUAL)
//                          .leftOuterJoin(INDIVIDUAL.as("m2")).on(INDIVIDUAL.CREATED_BY.eq(INDIVIDUAL.as("m2").ID))
//                          .leftOuterJoin(INDIVIDUAL.as("m3")).on(INDIVIDUAL.UPDATED_BY.eq(INDIVIDUAL.as("m3").ID))
//                          .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(INDIVIDUAL.ID))
//                          .leftJoin(ORGANISATION).on(ORGANISATION.ID.eq(INDIVIDUAL_ROLE.ORG_ID))
//                          .where(condition)
//                          .fetchOneInto(Integer.class);
//              return GetAllIndividualsForContacts.builder()
//                      .individuals(individualList)
//                      .rowCount(count)
//                      .build();
//
//          }catch(Exception ex){
//              throw new InternalServerError("Exception while fetching the individuals for contacts", ex);
//          }
//    }
//
//
//    public UUID getIndividualIdByOrganisationId(UUID orgId){
//        try {
//            return individualRoleDao.ctx().select(INDIVIDUAL_ROLE.INDIVIDUAL_ID).from(INDIVIDUAL_ROLE)
//                    .where(INDIVIDUAL_ROLE.ORG_ID.eq(orgId)).fetchOneInto(UUID.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching individual id by organisation id", ex);
//        }
//    }


    public List<DonorsResponse> getAllIndividualsDonors(Condition condition, List<SortField<?>> sortFields,
                                                        PaginationRequest paginationRequest,
                                                        UserPrincipal userPrincipal) {
        Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
        try {
            return individualDao.ctx()
                    .select(
                            INDIVIDUAL.ID.as("id"),
                            INDIVIDUAL.NAME.as("name"),
                            INDIVIDUAL.EMAIL.as("email"),
                            INDIVIDUAL.MOBILE_NUMBER.as("contactNumber"),
                            DSL.field("INDIVIDUAL.meta_data->>'panNo'").as("panNo"),
                            DSL.field("INDIVIDUAL.meta_data->>'donorType'", UUID.class).as("donorType"),
                            DSL.field("INDIVIDUAL.meta_data->>'donorReferralSource'", UUID.class).as("donorReferralSource"),
                            DSL.field("INDIVIDUAL.meta_data->>'donorReferralSourceAnyOther'",UUID.class).as("donorReferralSourceAnyOther"),
                            INDIVIDUAL_ROLE.ROLE_ID.as("roleId"),
                            INDIVIDUAL_ROLE.ORG_ID.as("donorOrgId"),
                            ORGANISATION.NAME.as("donorOrgName")
                    )
                    .from(INDIVIDUAL)
                    .leftJoin(INDIVIDUAL_ROLE)
                    .on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .leftJoin(ROLES)
                    .on(INDIVIDUAL_ROLE.ROLE_ID.eq(ROLES.ID))
                    .leftJoin(ORGANISATION)
                    .on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                    .where(condition.and(INDIVIDUAL_ROLE.ROLE_ID.eq(donorRoleId)))
                    .orderBy(sortFields)
                    .limit(paginationRequest.getPageSize())
                    .offset(pageNo)
                    .fetchInto(DonorsResponse.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching all Donors", ex);
        }
    }


    public Integer getIndividualsCountDonor(Condition condition, UserPrincipal userPrincipal) {
        try {
            return individualDao.ctx()
                    .select(DSL.countDistinct(INDIVIDUAL.ID))
                    .from(INDIVIDUAL)
                    .leftJoin(INDIVIDUAL_ROLE)
                    .on(INDIVIDUAL.ID.eq(INDIVIDUAL_ROLE.INDIVIDUAL_ID))
                    .leftJoin(ROLES)
                    .on(INDIVIDUAL_ROLE.ROLE_ID.eq(ROLES.ID))
                    .leftJoin(ORGANISATION)
                    .on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
                    .where(condition.and(INDIVIDUAL_ROLE.ROLE_ID.eq(donorRoleId)))
                    .fetchOne(0, Integer.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while counting Donors", ex);
        }
    }


    public boolean existsByMobile(String mobileNumber) {
        try {
            List<Individual> individuals = individualDao.fetchByMobileNumber(mobileNumber);
            return !individuals.isEmpty();
        } catch (Exception ex) {
            throw new InternalServerError("Exception occurred while fetching individual by mobile number", ex);
        }
    }

}
