package com.chidhagni.donationreceipt.organisationleadassignment;

//import com.chidhagni.houzer.db.jooq.tables.daos.OrganisationLeadAssignmentDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.OrganisationLeadAssignment;
import groovy.util.logging.Slf4j;
import lombok.RequiredArgsConstructor;
        import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
@Slf4j
public class OrganisationLeadAssignmentRepository {
//    private static final Logger log = LoggerFactory.getLogger(OrganisationLeadAssignmentRepository.class);
//    private final OrganisationLeadAssignmentDao organisationLeadAssignmentDao;
//    /**
//     * Insert organization lead assignment new record in database.
//     *
//     * <p>This method inserts an organization's lead assignment data.</p>
//     *
//     * @param organisationLeadAssignment The lead assignment object.
//     * @throws InternalServerError If an error occurs while saving data.
//     */
//    public void insertOrganisationLeadAssignment(OrganisationLeadAssignment organisationLeadAssignment){
//        try{
//            organisationLeadAssignmentDao.insert(organisationLeadAssignment);
//        }catch (Exception ex){
//            log.info("Failed in insert the organisation lead assignment");
//            throw new InternalServerError("Exception while inserting organisation lead assignment", ex);
//        }
//    }
//
//    /**
//     * Get organization lead assignment by unique identifier.
//     *
//     * <p>This method retrieve an organization's lead assignment record by primary id.</p>
//     *
//     * @param id Organisation lead assignment unique identifier.
//     * @throws InternalServerError If an error occurs while saving data.
//     */
//    public OrganisationLeadAssignment getById(UUID id)
//    {
//        OrganisationLeadAssignment organisationLeadAssignment;
//        try{
//            organisationLeadAssignment=organisationLeadAssignmentDao.fetchOneById(id);
//        }
//        catch (Exception ex){
//            throw new InternalServerError("Exception while retrieving organisation lead assignment", ex);
//        }
//        return organisationLeadAssignment;
//    }
//
//    /**
//     * Get organization lead assignment by organization id.
//     *
//     * <p>This method inserts or updates an organization's lead assignment data.</p>
//     *
//     * @param orgId The organisation id.
//     * @throws InternalServerError If an error occurs while saving data.
//     * @return list of organisation lead assignment objects.
//     */
//    public List<OrganisationLeadAssignment> getByOrganisationId(UUID orgId)
//    {
//        List<OrganisationLeadAssignment> organisationLeadAssignment;
//        try{
//            organisationLeadAssignment=organisationLeadAssignmentDao.fetchByOrganisationId(orgId);
//        }
//        catch (Exception ex){
//            throw new InternalServerError("Exception while retrieving organisation lead assignment", ex);
//        }
//        return organisationLeadAssignment;
//    }
//
//    /**
//     * Update organization lead assignment by organization lead assignment DTO.
//     *
//     * <p>This method inserts or updates an organization's lead assignment data.</p>
//     *
//     * @param organisationLeadAssignment Data transfer object.
//     * @throws InternalServerError If an error occurs while saving data.
//     */
//    public void updateOrganisationLeadAssignment(OrganisationLeadAssignment organisationLeadAssignment){
//        try{
//            organisationLeadAssignmentDao.update(organisationLeadAssignment);
//        }catch (Exception ex){
//            throw new InternalServerError("Exception while inserting organisation lead assignment", ex);
//        }
//    }
//
//    /**
//     * Upsert organization lead assignment.
//     *
//     * <p>This method inserts or updates an organization's lead assignment data.</p>
//     *
//     * @param organisationLeadAssignment The lead assignment object.
//     * @throws InternalServerError If an error occurs while saving data.
//     */
//    public void upsertOrganisationLeadAssignment(OrganisationLeadAssignment organisationLeadAssignment){
//        try{
//            organisationLeadAssignmentDao.merge(organisationLeadAssignment);
//        }catch (Exception ex){
//            throw new InternalServerError("Exception while upserting organisation lead assignment", ex);
//        }
//    }
}
