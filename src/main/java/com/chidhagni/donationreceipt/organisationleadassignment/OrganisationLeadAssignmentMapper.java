package com.chidhagni.donationreceipt.organisationleadassignment;

//import com.chidhagni.houzer.conversations.ConversationsDTO;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Organisation;
//import com.chidhagni.houzer.db.jooq.tables.pojos.OrganisationLeadAssignment;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class OrganisationLeadAssignmentMapper {
    private final OrganisationLeadAssignmentRepository organisationLeadAssignmentRepository;
    private final ObjectMapper objectMapper;

//    public void updateOrganisationLeadAssignmentForConversationDTO(OrganisationLeadAssignment organisationLeadAssignment,
//                                                                   ConversationsDTO conversationsDTO,
//                                                                   UUID loggedInUser) {
//        organisationLeadAssignment.setLeadStatus(conversationsDTO.getLeadStatus())
//                .setLeadPriority(conversationsDTO.getLeadPriority())
//                .setSmartSummary(conversationsDTO.getSmartSummary())
//                .setUpdatedBy(loggedInUser)
//                .setUpdatedOn(conversationsDTO.getUpdatedOn());
//    }
//
//    public void updateOrganisationLeadAssignmentFieldsCHSForm(OrganisationLeadAssignment organisationLeadAssignment,
//                                                              SocietyRegisterDTO societyRegisterDTO) {
//        organisationLeadAssignment.setLeadStatus(societyRegisterDTO.getLeadStatus())
//                .setLeadPriority(societyRegisterDTO.getLeadPriority())
//                .setSmartSummary(societyRegisterDTO.getRemarks());
//
//    }
//
//    public OrganisationLeadAssignment upsertOrgToOrgLeadAssignment(Organisation organisation) {
//        UUID orgId = organisation.getId();
//        List<OrganisationLeadAssignment> organisationLeadAssignmentsByOrgId
//                = organisationLeadAssignmentRepository.getByOrganisationId(orgId);
//        UUID orgLeadAssignmentId = null;
//        UUID leadStatus = null;
//        UUID leadPriority = null;
//        UUID assignedTo = null;
//        String metaData = organisation.getMetaData().toString();
//        try {
//            JsonNode metaDataNode = objectMapper.readTree(metaData);
//            // Extract individual fields
//            leadStatus = metaDataNode.has("leadStatus") && !metaDataNode.get("leadStatus").isNull()
//                    ? UUID.fromString(metaDataNode.get("leadStatus").asText())
//                    : null;
//            assignedTo = metaDataNode.has("assignedTo") && !metaDataNode.get("assignedTo").isNull()
//                    ? UUID.fromString(metaDataNode.get("assignedTo").asText())
//                    : null;
//            leadPriority = metaDataNode.has("leadPriority") && !metaDataNode.get("leadPriority").isNull()
//                    ? UUID.fromString(metaDataNode.get("leadPriority").asText())
//                    : null;
//        } catch (JsonProcessingException e) {
//            log.error("Failed to parse metaData JSONB: {}", e.getMessage(), e);
//        }
//
//        orgLeadAssignmentId = UUID.randomUUID();
//        OrganisationLeadAssignment organisationLeadAssignment = null;
//        if (organisationLeadAssignmentsByOrgId == null || CollectionUtils.isEmpty(organisationLeadAssignmentsByOrgId)) {
//            log.info("An organisation is not available for the given organisation so creating new one");
//            organisationLeadAssignment = new OrganisationLeadAssignment();
//            organisationLeadAssignment.setId(orgLeadAssignmentId);
//            organisationLeadAssignment.setOrganisationId(orgId);
//            organisationLeadAssignment.setAssignedTo(assignedTo);
//            organisationLeadAssignment.setLeadPriority(leadPriority);
//            organisationLeadAssignment.setLeadStatus(leadStatus);
//            organisationLeadAssignment.setCreatedOn(organisation.getCreatedOn());
//            organisationLeadAssignment.setUpdatedOn(organisation.getUpdatedOn());
//            organisationLeadAssignment.setCreatedBy(organisation.getCreatedBy());
//            organisationLeadAssignment.setUpdatedBy(organisation.getUpdatedBy());
//            organisationLeadAssignment.setIsActive(Boolean.TRUE);
//            return organisationLeadAssignment;
//        }
//        organisationLeadAssignment = organisationLeadAssignmentsByOrgId.get(0);
//        organisationLeadAssignment.setOrganisationId(orgId);
//        organisationLeadAssignment.setAssignedTo(assignedTo);
//        organisationLeadAssignment.setLeadPriority(leadPriority);
//        organisationLeadAssignment.setLeadStatus(leadStatus);
//        organisationLeadAssignment.setCreatedOn(organisation.getCreatedOn());
//        organisationLeadAssignment.setUpdatedOn(organisation.getUpdatedOn());
//        organisationLeadAssignment.setCreatedBy(organisation.getCreatedBy());
//        organisationLeadAssignment.setUpdatedBy(organisation.getUpdatedBy());
//        organisationLeadAssignment.setIsActive(Boolean.TRUE);
//        return organisationLeadAssignment;
//    }
}
