package com.chidhagni.donationreceipt.mail;

//import com.chidhagni.houzer.db.jooq.tables.daos.NewsLetterDao;
//import com.chidhagni.houzer.db.jooq.tables.daos.StagingEmailVerificationDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.NewsLetter;
//import com.chidhagni.houzer.db.jooq.tables.pojos.StagingEmailVerification;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EmailVerificationService {


//    private final StagingEmailVerificationDao stagingEmailVerificationDao;
//    private final EmailVerifyDTOMapper emailVerifyDTOMapper;
//    private final NotificationManager notificationManager;
//    private final NewsLetterDao newsLetterDao;
//    @Value("${server.hostUrl}")
//    public String hostUrl;
//
//    public EmailVerificationService(StagingEmailVerificationDao stagingEmailVerificationDao
//            , EmailVerifyDTOMapper emailVerifyDTOMapper, NotificationManager notificationManager
//            , NewsLetterDao newsLetterDao) {
//        this.stagingEmailVerificationDao = stagingEmailVerificationDao;
//        this.emailVerifyDTOMapper = emailVerifyDTOMapper;
//        this.notificationManager = notificationManager;
//        this.newsLetterDao = newsLetterDao;
//    }
//
//    @Transactional
//    public EmailVerificationResponseDTO create(EmailVerifyDTO emailVerifyDTO) throws MessagingException {
//
//        List<StagingEmailVerification> stagingEmailVerificationList = stagingEmailVerificationDao.fetchByEmail(emailVerifyDTO.getEmail());
//        if (!stagingEmailVerificationList.isEmpty()) {
//            log.info("Email Already Exists");
//            StagingEmailVerification stagingRecord = stagingEmailVerificationList.get(0);
//
//
//            if (stagingRecord.getIsVerified()) {
//                return EmailVerificationResponseDTO.builder().isVerified(true).message("Already Verified.").build();
//            } else {
//                notificationManager.sendOTPV2(stagingRecord, emailVerifyDTO.getRole());
//                return EmailVerificationResponseDTO.builder().isVerified(false).message("OTP has been send to your EMail for verification. Please check.").build();
//            }
//        }
//
//        StagingEmailVerification stagingEmailVerification = emailVerifyDTOMapper.map(emailVerifyDTO);
//        stagingEmailVerificationDao.insert(stagingEmailVerification);
//
//        notificationManager.sendOTPV2(stagingEmailVerification, emailVerifyDTO.getRole() );
//        return EmailVerificationResponseDTO.builder().isVerified(false).message("OTP has been send to your EMail for verification. Please check.").build();
//    }
//
//    @Transactional
//    public EmailVerificationResponseDTO createStagingEmailVerify(StagingEmailVerifyDTO stagingEmailVerifyDTO) throws MessagingException {
//
//        List<StagingEmailVerification> stagingEmailVerificationList = stagingEmailVerificationDao.fetchByEmail(stagingEmailVerifyDTO.getEmail());
//        if (!stagingEmailVerificationList.isEmpty()) {
//            log.info("Email Already Exists");
//            StagingEmailVerification stagingRecord = stagingEmailVerificationList.get(0);
//
//
//            if (stagingRecord.getIsVerified()) {
//                return EmailVerificationResponseDTO.builder().isVerified(true).message("Already Verified.").build();
//            } else {
//                notificationManager.sendOTPV3(stagingRecord);
//                return EmailVerificationResponseDTO.builder().isVerified(false).message("OTP has been send to your EMail for verification. Please check.").build();
//            }
//        }
//
//        StagingEmailVerification stagingEmailVerification = emailVerifyDTOMapper.mapStagingEmailDTO(stagingEmailVerifyDTO);
//        stagingEmailVerificationDao.insert(stagingEmailVerification);
//        log.info("Inserted staging email verification record for organization: {}", stagingEmailVerification.getOrganisationName());
//
//        notificationManager.sendOTPV3(stagingEmailVerification);
//        return EmailVerificationResponseDTO.builder().isVerified(false).message("OTP has been send to your EMail for verification. Please check.").build();
//    }
//
//
//    @Transactional
//    public EmailSubscribedDTO unSubscribeNewsletter(UnsubscribeDataDTO unSubscribeDataDTO) {
//
//        List<NewsLetter> newsLetterList = newsLetterDao.fetchByEmail(unSubscribeDataDTO.getEmail());
//
//        if (newsLetterList == null) {
//            return EmailSubscribedDTO.builder()
//                    .isSubscribed(false)
//                    .message("Invalid Email")
//                    .build();
//        }
//
//        NewsLetter newsLetter = newsLetterList.get(0);
//
//        if (newsLetter.getUnsubscribeCode() == null) {
//            return EmailSubscribedDTO.builder()
//                    .isSubscribed(false)
//                    .message("Newsletter Already unsubscribed for " + unSubscribeDataDTO.getEmail())
//                    .build();
//        } else if (!Objects.equals(newsLetter.getUnsubscribeCode(), unSubscribeDataDTO.getUnsubscribeCode())) {
//            return EmailSubscribedDTO.builder()
//                    .isSubscribed(false)
//                    .message("Invalid unSubscribe Code")
//                    .build();
//        }
//
//        newsLetter.setUnsubscribeCode(null);
//        newsLetter.setReason(unSubscribeDataDTO.getReason());
//        newsLetter.setIsActive(false);
//        newsLetter.setUpdatedOn(DateUtils.currentTimeIST());
//
//        newsLetterDao.update(newsLetter);
//
//        return EmailSubscribedDTO.builder()
//                .isSubscribed(false)
//                .message("Newsletter Successfully unsubscribed for " + unSubscribeDataDTO.getEmail())
//                .build();
//    }
//
//
//    @Transactional
//    public EmailSubscribedDTO subscriptionNewsletter(String email) throws MessagingException {
//        List<StagingEmailVerification> list = stagingEmailVerificationDao.fetchByEmail(email);
//        if (list.isEmpty()) {
//            return EmailSubscribedDTO.builder()
//                    .isSubscribed(false)
//                    .message("Please verify your email.")
//                    .build();
//        }
//
//        StagingEmailVerification stagingRecord = list.get(0);
//
//        if (stagingRecord.getIsVerified()) {
//            List<NewsLetter> existingNewsletters = newsLetterDao.fetchByEmail(email);
//
//            if (!existingNewsletters.isEmpty()) {
//                return EmailSubscribedDTO.builder()
//                        .isSubscribed(true)
//                        .message("You are already subscribed to the newsletter.")
//                        .build();
//            }
//
//            String unsubscribeCode = RandomStringUtils.random(64, true, true);
//
//            NewsLetter newsletter = new NewsLetter();
//            newsletter.setId(UUID.randomUUID());
//            newsletter.setUnsubscribeCode(unsubscribeCode);
//            newsletter.setIsActive(true);
//            newsletter.setCreatedOn(DateUtils.currentTimeIST());
//            newsletter.setUpdatedOn(DateUtils.currentTimeIST());
//            newsletter.setEmail(email);
//
//            newsLetterDao.insert(newsletter);
//
//            notificationManager.sendSuccessSubscriptionEmail(stagingRecord, encodeLink(hostUrl + "/unSubscribe/?unsubscribeCode=" + unsubscribeCode + "&emailId=" + email));
//
//            return EmailSubscribedDTO.builder()
//                    .isSubscribed(true)
//                    .message("You are now subscribed to the newsletter.")
//                    .build();
//        } else {
//            return EmailSubscribedDTO.builder()
//                    .isSubscribed(false)
//                    .message("OTP has been sent to your email for verification. Please check.")
//                    .build();
//        }
//    }
//
//    private String encodeLink(String link) {
//        return UriComponentsBuilder.fromUriString(link).build().encode().toString();
//    }
//
//    public EmailVerificationResponseDTO checkIsVerified(@RequestParam String email) {
//        List<StagingEmailVerification> stagingEmailVerificationList = stagingEmailVerificationDao.fetchByEmail(email);
//
//        if (!stagingEmailVerificationList.isEmpty()) {
//            log.info("Email Already Exists");
//            StagingEmailVerification stagingRecord = stagingEmailVerificationList.get(0);
//
//            if (stagingRecord.getIsVerified()) {
//                return EmailVerificationResponseDTO.builder().isVerified(true).message("Already Verified.").build();
//            } else {
//                return EmailVerificationResponseDTO.builder().isVerified(false).message("Email not verified").build();
//            }
//        }
//
//        return EmailVerificationResponseDTO.builder().isVerified(false).message("Email not found.").build();
//    }

}
