package com.chidhagni.donationreceipt.mail;
import io.micrometer.core.lang.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class MailService {


    @Value("${email.professionals}")
    public String fromProfessionalsEmail;

    @Qualifier("mailSetupProfessionals")
    private final JavaMailSender professionalsMailSender;

    private final SpringTemplateEngine templateEngine;

    @Value("${email.societies}")
    public String fromSocietiesEmail;

    @Qualifier("mailSetupSocieties")
    private final JavaMailSender societiesMailSender;

    @Value("${email.developers}")
    public String fromDevelopersEmail;

    @Qualifier("mailSetupDevelopers")
    private final JavaMailSender developersMailSender;

    @Value("${email.registration}")
    public String fromRegistrationEmail;

    @Qualifier("mailSetupRegistration")
    private final JavaMailSender registrationMailSender;

    @Qualifier("mailSetUpDonations")
    private final JavaMailSender donationMailSender;

    @Value("${email.donations}")
    private String fromDonationEmail;


    public MailService(@Qualifier("mailSetupProfessionals") JavaMailSender professionalsMailSender, SpringTemplateEngine templateEngine, @Qualifier("mailSetupSocieties") JavaMailSender societiesMailSender, @Qualifier("mailSetupDevelopers") JavaMailSender developersMailSender, JavaMailSender javaMailSender, @Qualifier("mailSetupRegistration") JavaMailSender registrationMailSender, JavaMailSender donationMailSender) {
        this.professionalsMailSender = professionalsMailSender;
        this.templateEngine = templateEngine;
        this.societiesMailSender = societiesMailSender;
        this.developersMailSender = developersMailSender;
        this.registrationMailSender = registrationMailSender;
        this.donationMailSender = donationMailSender;
    }


    public void sendActivationLink(String subject, String toEmail, @Nullable String ccEmail, @Nullable String bccEmail, Map<String, Object> props, String templatePath, @Nullable File attachmentFile) throws MessagingException {

        //TODO RENAME TO DONATIONS ACCORDINGLY
        JavaMailSender mailSender = professionalsMailSender;
        String fromEmail = fromProfessionalsEmail;

        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        Context context = new Context();
        context.setVariables(props);

        String emailContent = templateEngine.process(templatePath, context);
        helper.setTo(toEmail);
        if(ccEmail != null) {
            helper.setCc(ccEmail);
        }
        if (bccEmail != null) {
            helper.setBcc(bccEmail);
        }
        helper.setText(emailContent, true);
        helper.setSubject(subject);
        helper.setFrom(fromEmail);

        mailSender.send(message);
        if (attachmentFile != null) {
            boolean isDeleted = attachmentFile.delete();
            if (!isDeleted) {
                log.info("Failed to delete file: {}", attachmentFile.getAbsolutePath());
            }
        }
    }


    public void sendPasswordResetLink(String subject, String toEmail, @Nullable String ccEmail, @Nullable String bccEmail, Map<String, Object> props, String templatePath, @Nullable File attachmentFile) throws MessagingException {

        JavaMailSender mailSender = professionalsMailSender;
        String fromEmail = fromProfessionalsEmail;


        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        Context context = new Context();
        context.setVariables(props);

        String emailContent = templateEngine.process(templatePath, context);
        helper.setTo(toEmail);
        if(ccEmail != null) {
            helper.setCc(ccEmail);
        }
        if (bccEmail != null) {
            helper.setBcc(bccEmail);
        }
        helper.setText(emailContent, true);
        helper.setSubject(subject);
        helper.setFrom(fromEmail);

        mailSender.send(message);
        if (attachmentFile != null) {
            boolean isDeleted = attachmentFile.delete();
            if (!isDeleted) {
                log.info("Failed to delete file: {}", attachmentFile.getAbsolutePath());
            }
        }
    }


    public void sendContactFormEmailToAdmin(String subject, Map<String, Object> props, String contactFormTemplate)
            throws MessagingException {

        JavaMailSender mailSender = professionalsMailSender;
        String fromEmail = fromProfessionalsEmail;

        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        Context context = new Context();
        context.setVariables(props);

        String emailContent = templateEngine.process(contactFormTemplate, context);
        helper.setTo(fromProfessionalsEmail);
        helper.setText(emailContent, true);
        helper.setSubject(subject);
        helper.setFrom(fromEmail);

        mailSender.send(message);
    }

    public void sendDonationReceipt(String subject, String toEmail, @Nullable List<String> ccEmails, @Nullable String bccEmail, Map<String, Object> props, String templatePath, @Nullable File attachmentFile) throws MessagingException {
        JavaMailSender mailSender = professionalsMailSender;
        String fromEmail = fromProfessionalsEmail;


        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        Context context = new Context();
        context.setVariables(props);

        String emailContent = templateEngine.process(templatePath, context);
        helper.setTo(toEmail);
        if (ccEmails != null && !ccEmails.isEmpty()) {
            helper.setCc(ccEmails.toArray(new String[0]));  // Converts the List to an array
        }
        if (bccEmail != null) {
            helper.setBcc(bccEmail);
        }
        if (attachmentFile != null) {
            helper.addAttachment("DonationReceipt.pdf", attachmentFile);
        }
        helper.setText(emailContent, true);
        helper.setSubject(subject);
        helper.setFrom(fromEmail);

        mailSender.send(message);
        if (attachmentFile != null) {
            boolean isDeleted = attachmentFile.delete();
            if (!isDeleted) {
                log.info("Failed to delete file: {}", attachmentFile.getAbsolutePath());
            }
        }
    }

    public void sendCommunicationMessage(String subject, String toEmail, @Nullable String ccEmail,
                                         @Nullable String bccEmail, Map<String, Object> props,
                                         String templatePath, @Nullable File attachmentFile) throws MessagingException {
        JavaMailSender mailSender = professionalsMailSender;
        String fromEmail = fromProfessionalsEmail;


        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        Context context = new Context();
        context.setVariables(props);

        String emailContent = templateEngine.process(templatePath, context);
        helper.setTo(toEmail);
        if(ccEmail != null) {
            helper.setCc(ccEmail);
        }
        if (bccEmail != null) {
            helper.setBcc(bccEmail);
        }
        helper.setText(emailContent, true);
        helper.setSubject(subject);
        helper.setFrom(fromEmail);

        mailSender.send(message);
        if (attachmentFile != null) {
            boolean isDeleted = attachmentFile.delete();
            if (!isDeleted) {
                log.info("Failed to delete file: {}", attachmentFile.getAbsolutePath());
            }
        }
    }
}