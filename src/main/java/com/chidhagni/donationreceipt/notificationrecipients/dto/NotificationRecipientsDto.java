package com.chidhagni.donationreceipt.notificationrecipients.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class NotificationRecipientsDto {
    private UUID notificationId;
    private final UUID id;
    private final UUID individualId;
    private final Boolean status;
    private final Integer sentCount;
    private final String createdBy;
    private final String updatedBy;
    private final LocalDateTime createdOn;
    private final LocalDateTime updatedOn;
}
