package com.chidhagni.donationreceipt.security;

import com.chidhagni.utils.Token;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.io.Serializable;
import java.util.UUID;

@Getter
@RequiredArgsConstructor
public class LoginResponse implements Serializable {
    private static final long serialVersionUID = -8091879091924046844L;
    private final Token token;
    private final UUID individualId;
}