package com.chidhagni.donationreceipt.security;

import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.PathNotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service
@AllArgsConstructor
@Slf4j
public class CustomMethodSecurity {


    public boolean isAuthenticatedWithPermission(String jsonPath, Integer requiredPermission) {
        Collection<? extends GrantedAuthority> authorities = SecurityContextHolder.getContext().getAuthentication().getAuthorities();

        if (authorities == null || authorities.isEmpty()) {
            log.error("No authorities found for the current user.");
            return false;
        }
        return authorities.stream().anyMatch(authority -> {
            try {
                List<?> result = JsonPath.read(authority.getAuthority(), jsonPath);
                if (result.isEmpty()) {
                    return false;
                }
                // Check if there is any match
                int permissionsValueFromDB = extractPermissionsValue(authority.getAuthority(), jsonPath.concat(".permissions"));
                if (permissionsValueFromDB == -1) {
                    return false;
                }
                int resultPermission = permissionsValueFromDB & requiredPermission;
                return resultPermission == requiredPermission;
            } catch (PathNotFoundException e) {
                log.warn("Path not found in JSON: {}", jsonPath);
                return false;
            } catch (Exception e) {
                // Log any other parsing errors
                log.error("Error parsing authority JSON or matching JSON path: ", e);
                return false;
            }
        });
    }


    private int extractPermissionsValue(String authority, String permissionsPath) {
        try {
            Object read = JsonPath.read(authority, permissionsPath);
            if (read instanceof net.minidev.json.JSONArray) {
                net.minidev.json.JSONArray resultArray = (net.minidev.json.JSONArray) read;
                if (!resultArray.isEmpty() && resultArray.get(0) instanceof Number) {
                    return ((Number) resultArray.get(0)).intValue();
                } else {
                    log.error("Expected a single numeric value in JSON array but found: {}", resultArray);
                    return -1;
                }
            } else {
                log.error("Permissions field is not a valid number or array with a single value: {}", read);
                return -1;
            }
        } catch (Exception e) {
            log.error("Error extracting permissions value from JSON: ", e);
            return -1;
        }
    }
}