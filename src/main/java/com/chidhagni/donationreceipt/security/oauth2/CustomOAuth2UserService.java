package com.chidhagni.donationreceipt.security.oauth2;

import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.individualsignup.SignUpService;
import com.chidhagni.donationreceipt.individualverificationaudit.IndividualVerificationAuditService;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.donationreceipt.security.oauth2.user.OAuth2UserInfo;
import com.chidhagni.donationreceipt.security.oauth2.user.OAuth2UserInfoFactory;
import com.chidhagni.utils.CommonOperations;
import com.chidhagni.web.OAuth2AuthenticationProcessingException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationRequest;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.mail.MessagingException;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.UUID;

@Service
public class CustomOAuth2UserService extends DefaultOAuth2UserService {


    @Autowired
    private HttpServletRequest request;


    @Autowired
    private CommonOperations commonOperations;

    @Autowired
    private ObjectMapper objectMapper;


    @Autowired
    private IndividualDao individualDao;

    private SignUpService signUpService;
    private IndividualVerificationAuditService individualVerificationAuditService;


    public CustomOAuth2UserService() {
    }

    @Override
    public OAuth2User loadUser(OAuth2UserRequest oAuth2UserRequest) throws OAuth2AuthenticationException {
        OAuth2User oAuth2User = super.loadUser(oAuth2UserRequest);
        try {
            return processOAuth2Individual(oAuth2UserRequest, oAuth2User);
        } catch (AuthenticationException ex) {
            throw ex;
        } catch (Exception ex) {
            throw new InternalAuthenticationServiceException(ex.getMessage(), ex.getCause());
        }
    }

    private OAuth2User processOAuth2Individual(OAuth2UserRequest oAuth2UserRequest, OAuth2User oAuth2User) throws JsonProcessingException, MessagingException {
        OAuth2UserInfo oAuth2UserInfo = OAuth2UserInfoFactory.getOAuth2UserInfo(
                oAuth2UserRequest.getClientRegistration().getRegistrationId(), oAuth2User.getAttributes());

        String email = oAuth2UserInfo.getEmail();

        if (email == null || email.isEmpty()) {
            throw new OAuth2AuthenticationProcessingException("Email not found from OAuth2 provider");
        }
        // Access the HttpServletRequest
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (!(requestAttributes instanceof ServletRequestAttributes)) {
            {
                throw new IllegalStateException("RequestAttributes is null or not of type ServletRequestAttributes");
            }
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();

        String role = null;
        String organisationName = null;
        UUID companyType = null;

        // Load the OAuth2AuthorizationRequest
        HttpCookieOAuth2AuthorizationRequestRepository authorizationRequestRepository = new HttpCookieOAuth2AuthorizationRequestRepository();
        OAuth2AuthorizationRequest authorizationRequest = authorizationRequestRepository.loadAuthorizationRequest(request);

        if (authorizationRequest != null) {
            Map<String, Object> additionalParameters = authorizationRequest.getAdditionalParameters();

            String typeOfCompany = (String) additionalParameters.get("companyType");
            role = (String) additionalParameters.get("role");
            organisationName = (String) additionalParameters.get("organisationName");
            if (typeOfCompany != null && !typeOfCompany.isEmpty()) {
                companyType = UUID.fromString(typeOfCompany);
            }
        }

        // Proceed with your logic
        Individual individual = individualDao.fetchOneByEmail(email);


        if (role != null && !role.isEmpty()) {
            // Role parameter exists proceed with signing up flow
            if (individual != null) {
                // User found in the database, throw an error
                throw new OAuth2AuthenticationProcessingException(
                        String.format("An Individual Already Exists with given email :: %s", email));
//            } else {
//                // User not found in the database, proceed with signup flow
//                Individual individualEntity
//                        = registerNewIndividual(oAuth2UserRequest, oAuth2UserInfo, role,organisationName,companyType);
//                return UserPrincipal.create(individualEntity, oAuth2User.getAttributes());
//            }
            } else {
                // No role parameter, proceed with login flow
                if (individual != null) {
                    // Existing user found, proceed with your logic
                    // Update existing user details if necessary
                    return UserPrincipal.create(individual, oAuth2User.getAttributes());
                } else {
                    // User not found, throw authentication error
                    throw new OAuth2AuthenticationProcessingException("Individual Not Found");
                }
            }
        }

        return oAuth2User;
    }
}
