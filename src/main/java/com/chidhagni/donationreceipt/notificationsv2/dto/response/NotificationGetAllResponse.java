package com.chidhagni.donationreceipt.notificationsv2.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class NotificationGetAllResponse {
    private List<NotificationGetByIdResponse> notificationGetByIdResponseList;
    private Integer count;
}
