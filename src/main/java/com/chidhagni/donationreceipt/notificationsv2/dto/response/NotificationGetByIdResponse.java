package com.chidhagni.donationreceipt.notificationsv2.dto.response;

import com.chidhagni.donationreceipt.notificationrecipients.dto.NotificationRecipientsDto;
import com.chidhagni.donationreceipt.notificationsv2.dto.request.NotificationsMetaData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
@Data
@Builder
@AllArgsConstructor
@RequiredArgsConstructor
public class NotificationGetByIdResponse {
    private final UUID id;
    private final UUID templateId;
    private final String levelOneContent;
    private final String levelTwoContent;
    private final String type;
    private final NotificationsMetaData metaData;
    private final Boolean isActive;
    private final String createdBy;
    private final String updatedBy;
    private final LocalDateTime createdOn;
    private final LocalDateTime updatedOn;
    private List<NotificationRecipientsDto> recipients;
}
