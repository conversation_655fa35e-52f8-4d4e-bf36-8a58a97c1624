package com.chidhagni.donationreceipt.notificationsv2;

//import com.chidhagni.houzer.db.jooq.Tables;
//import com.chidhagni.houzer.db.jooq.tables.daos.NotificationRecipientDao;
//import com.chidhagni.houzer.db.jooq.tables.daos.NotificationsV2Dao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.NotificationRecipient;
//import com.chidhagni.houzer.db.jooq.tables.pojos.NotificationsV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

//import static com.chidhagni.houzer.db.jooq.Tables.*;
import static org.jooq.impl.DSL.field;


@Service
@Slf4j
@RequiredArgsConstructor
public class NotificationServiceV2 {

//    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");
//
//    private final NotificationsV2Dao notificationsV2Dao;
//    private final NotificationRecipientDao notificationRecipientDao;
//    private final NotificationMapperV2 notificationMapperV2;
//    private final NotificationRecipientMapper notificationRecipientMapper;
//    private final NotificationRepository notificationRepository;
//    private final Integer pageNo = 1;
//    private final Integer pageSize = 10;
//    private final DSLContext context;
//
//
//
//    /**
//     * Creates a new notification and inserts it into NotificationV2 and NotificationRecipients table.
//     *
//     *
//     * <p>This method generates a new notification with specified recipients and persists it in the NotificationV2.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Validates the `NotificationRequest` to ensure it meets the required criteria.</li>
//     *   <li>Generates a unique `UUID` for the new notification.</li>
//     *   <li>Maps the request data to a NotificationsV2 entity using `notificationMapperV2`.</li>
//     *   <li>Inserts the notification into the database via `notificationsV2Dao.insert()`.</li>
//     *   <li>Extracts the list of recipients from the request and maps them to NotificationRecipient entities.</li>
//     *   <li>Inserts the recipients into the database via `notificationRecipientDao.insert()`.</li>
//     *   <li>Returns the generated notification UUID.</li>
//     * </ul>
//     *
//     *
//     * @param request       The request object containing the notification details.
//     * @param userPrincipal The authenticated user initiating the notification creation.
//     * @return A `UUID` representing the newly created notification.
//     * @throws BadRequestException If the request data is invalid.
//     * * @throws InternalServerError If an error occurs while inserting the notification into the database.
//     */
//    @Transactional
//    public UUID create(NotificationRequest request, UserPrincipal userPrincipal) {
//        validateNotificationRequest(request, userPrincipal);
//        UUID notificationId = UUID.randomUUID();
//        NotificationsV2 notificationsV2 = notificationMapperV2.mapNotificationRequestToNotificationsV2(notificationId,
//                request, userPrincipal.getId());
//        notificationsV2Dao.insert(notificationsV2);
//        List<NotificationRecipient> notificationRecipients = request.getRecipients().stream()
//                .map(recipientId -> notificationRecipientMapper.mapNotificationRequestToNotificationRecipient(notificationId,
//                        recipientId, userPrincipal.getId()))
//                .collect(Collectors.toList());
//        notificationRecipientDao.insert(notificationRecipients);
//        return notificationId;
//    }
//
//    /**
//     * Fetches all active notifications for the authenticated user.
//     *
//     *
//     * <p>This method retrieves all active notifications for the given user by performing a database query that
//     * joins multiple tables and structures the data accordingly.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Selects notification details from the `NOTIFICATIONS_V2` table.</li>
//     *   <li>Includes associated template content from the `NOTIFICATION_TEMPLATES` table.</li>
//     *   <li>Retrieves metadata and status from the `NOTIFICATION_RECIPIENT` table.</li>
//     *   <li>Joins with the `INDIVIDUAL` table to determine the full name of the notification creator.</li>
//     *   <li>Filters the results to only include notifications where:
//     *     <ul>
//     *       <li>The recipient's `INDIVIDUAL_ID` matches the authenticated user's ID.</li>
//     *       <li>The notification is marked as active (`IS_ACTIVE = true`).</li>
//     *     </ul>
//     *   </li>
//     *   <li>Orders the notifications by creation date in descending order.</li>
//     *   <li>Maps the results into `NotificationResponse` objects using `toNotificationResponse()`.</li>
//     * </ul>
//     *
//     *
//     * @param userPrincipal The authenticated user requesting their notifications.
//     * @return A NotificationResponses object containing the list of active notifications for the user.
//     * *@throws InternalServerError If an error occurs while retrieving the notifications.
//     */
//    @Transactional
//    public NotificationResponses fetchUserNotifications(UserPrincipal userPrincipal) {
//        List<NotificationResponse> notifications = context.select(
//                        NOTIFICATIONS_V2.ID,
//                        NOTIFICATIONS_V2.TEMPLATE_ID,
//                        NOTIFICATION_TEMPLATES.LEVEL1_CONTENT,
//                        NOTIFICATION_TEMPLATES.LEVEL2_CONTENT,
//                        NOTIFICATIONS_V2.CREATED_ON,
//                        NOTIFICATIONS_V2.META_DATA,
//                        NOTIFICATION_RECIPIENT.CREATED_ON,
//                        NOTIFICATION_RECIPIENT.STATUS,
//                        DSL.case_()
//                                .when(INDIVIDUAL.LAST_NAME.isNull(), INDIVIDUAL.FIRST_NAME)
//                                .otherwise(DSL.concat(INDIVIDUAL.FIRST_NAME, DSL.inline(" "), INDIVIDUAL.LAST_NAME))
//                                .as("createdBy")
//                )
//                .from(NOTIFICATIONS_V2)
//                .join(NOTIFICATION_RECIPIENT)
//                .on(field(NOTIFICATIONS_V2.ID).eq(field(NOTIFICATION_RECIPIENT.NOTIFICATION_ID)))
//                .join(INDIVIDUAL)
//                .on(field(INDIVIDUAL.ID).eq(field(NOTIFICATIONS_V2.CREATED_BY)))
//                .join(Tables.NOTIFICATION_TEMPLATES)
//                .on(field(NOTIFICATION_TEMPLATES.ID).eq(field(NOTIFICATIONS_V2.TEMPLATE_ID)))
//                .where(NOTIFICATION_RECIPIENT.INDIVIDUAL_ID.eq(userPrincipal.getId()))
//                .and(NOTIFICATION_RECIPIENT.IS_ACTIVE.isTrue())
//                .orderBy(NOTIFICATIONS_V2.CREATED_ON.desc())
//                .stream()
//                .map(this::toNotificationResponse)
//                .collect(Collectors.toList());
//        return NotificationResponses.builder()
//                .notifications(notifications)
//                .build();
//    }
//
//
//    /**
//     * Updates the status of specified notifications for the authenticated user.
//     *
//     *
//     * <p>This method toggles the read/unread status of selected notifications for the user
//     * and updates the modification details in the database.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Retrieves the user ID from `userPrincipal`.</li>
//     *   <li>Performs an `UPDATE` query on the `NOTIFICATION_RECIPIENT` table:
//     *     <ul>
//     *       <li>Toggles the `STATUS` field (marks notifications as read/unread).</li>
//     *       <li>Updates `UPDATED_BY` with the authenticated user's ID.</li>
//     *       <li>Updates `UPDATED_ON` with the current timestamp.</li>
//     *     </ul>
//     *   </li>
//     *   <li>Filters the update to only affect notifications:
//     *     <ul>
//     *       <li>That belong to the authenticated user (`INDIVIDUAL_ID = userId`).</li>
//     *       <li>That match the list of `notificationId`s provided in `request.getNotifications()`.</li>
//     *     </ul>
//     *   </li>
//     *   <li>Executes the update query and logs the number of affected rows.</li>
//     * </ul>
//     *
//     *
//     * @param request       The request object containing notification IDs for updating status.
//     * @param userPrincipal The authenticated user initiating the update request.
//     * * @throws InternalServerError If an error occurs while updating the notifications.
//     */
//    @Transactional
//    public void update(NotificationUpdateRequest request, UserPrincipal userPrincipal) {
//        UUID userId = userPrincipal.getId();
//        int rowsAffected = context.update(NOTIFICATION_RECIPIENT)
//                .set(NOTIFICATION_RECIPIENT.STATUS, DSL.not(NOTIFICATION_RECIPIENT.STATUS))
//                .set(NOTIFICATION_RECIPIENT.UPDATED_BY, userId)
//                .set(NOTIFICATION_RECIPIENT.UPDATED_ON, DateUtils.currentDatetime())
//                .where(NOTIFICATION_RECIPIENT.INDIVIDUAL_ID.eq(userId))
//                .and(NOTIFICATION_RECIPIENT.NOTIFICATION_ID.in(request.getNotifications()))
//                .execute();
//        log.info("[{}] rows affected when updating notification [{}] by user [{}]", rowsAffected, request, userPrincipal.getId());
//    }
//
//
//    /**
//     * Soft deletes specified notifications for the authenticated user.
//     *
//     * <p>This method marks selected notifications as inactive (soft delete) for the user
//     * and updates the modification details in the database.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Retrieves the user ID from `userPrincipal`.</li>
//     *   <li>Performs an `UPDATE` query on the `NOTIFICATION_RECIPIENT` table:
//     *     <ul>
//     *       <li>Sets `IS_ACTIVE` to `false`, effectively soft-deleting the notifications.</li>
//     *       <li>Updates `UPDATED_BY` with the authenticated user's ID.</li>
//     *       <li>Updates `UPDATED_ON` with the current timestamp.</li>
//     *     </ul>
//     *   </li>
//     *   <li>Filters the update to only affect notifications:
//     *     <ul>
//     *       <li>That belong to the authenticated user (`INDIVIDUAL_ID = userId`).</li>
//     *       <li>That match the list of `notificationId`s provided in `request.getNotifications()`.</li>
//     *     </ul>
//     *   </li>
//     *   <li>Executes the update query and logs the number of affected rows.</li>
//     * </ul>
//     *
//     *
//     * @param request       The request object containing notification IDs to be marked inactive.
//     * @param userPrincipal The authenticated user initiating the delete request.
//     * *@throws InternalServerError If an error occurs while marking notifications as inactive.
//     */
//    @Transactional
//    public void delete(NotificationUpdateRequest request, UserPrincipal userPrincipal) {
//        UUID userId = userPrincipal.getId();
//        int rowsAffected = context.update(NOTIFICATION_RECIPIENT)
//                .set(NOTIFICATION_RECIPIENT.IS_ACTIVE, false)
//                .set(NOTIFICATION_RECIPIENT.UPDATED_BY, userId)
//                .set(NOTIFICATION_RECIPIENT.UPDATED_ON, DateUtils.currentDatetime())
//                .where(NOTIFICATION_RECIPIENT.INDIVIDUAL_ID.eq(userId))
//                .and(NOTIFICATION_RECIPIENT.NOTIFICATION_ID.in(request.getNotifications()))
//                .execute();
//        log.info("[{}] rows affected when deleting notification [{}] by user [{}]", rowsAffected, request, userPrincipal.getId());
//    }
//
//    /**
//     * Converts a database record into a NotificationResponse object.
//     *
//     *
//     * <p>This method extracts relevant fields from the database record and maps them to a
//     * NotificationResponse object, formatting metadata and content as needed.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Extracts the `meta_data` field from the record as a  Map.</li>
//     *   <li>Retrieves the notification ID from `NOTIFICATIONS_V2.ID`.</li>
//     *   <li>Parses `LEVEL1_CONTENT` and `LEVEL2_CONTENT` using `parseContent()` to format dynamic placeholders.</li>
//     *   <li>Extracts the `redirectPage` field from `meta_data`.</li>
//     *   <li>Determines the read/unread status from `NOTIFICATION_RECIPIENT.STATUS`.</li>
//     *   <li>Formats the creation timestamp using `DATETIME_FORMATTER`.</li>
//     *   <li>Retrieves the creator's name from the `"createdBy"` alias.</li>
//     *   <li>Constructs and returns a `NotificationResponse` object.</li>
//     * </ul>
//     *
//     *
//     * @param record The database record representing a notification.
//     * @return A  NotificationResponse object containing the formatted notification details.
//     */
//    private NotificationResponse toNotificationResponse(Record record) {
//        // Extracting meta_data as a Map
//        Map<String, Object> metaData = record.get(NOTIFICATIONS_V2.META_DATA).getData();
//
//        return NotificationResponse.builder()
//                .notificationId(record.get(NOTIFICATIONS_V2.ID))
//                .level1Content(parseContent(
//                        record.get(NOTIFICATION_TEMPLATES.LEVEL1_CONTENT),
//                        metaData
//                ))
//                .level2Content(parseContent(
//                        record.get(NOTIFICATION_TEMPLATES.LEVEL2_CONTENT),
//                        metaData
//                ))
//                .redirectPage((String) metaData.get("redirectPage"))
//                .isRead(record.get(NOTIFICATION_RECIPIENT.STATUS))
//                .createdOn(record.get(NOTIFICATION_RECIPIENT.CREATED_ON).format(DATETIME_FORMATTER))
//                .createdBy(record.get("createdBy", String.class))
//                .build();
//    }
//
//
//    /**
//     * Parses and replaces placeholders in a content string using provided metadata.
//     *
//     *
//     * <p>This method replaces placeholders in a given content string with corresponding values
//     * from the provided metadata map.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Iterates through each key-value pair in the `data` map.</li>
//     *   <li>Constructs a placeholder pattern in the format <code>{{key}}</code>.</li>
//     *   <li>Uses `replaceAll()` to replace placeholders with their corresponding values.</li>
//     *   <li>Returns the updated content with placeholders replaced.</li>
//     * </ul>
//     *
//     *
//     * @param content The content string containing placeholders.
//     * @param data    The metadata map with keys corresponding to placeholders.
//     * @return A string with placeholders replaced by their respective values.
//     * @throws NullPointerException If `content` or `data` is null.*/
//    private String parseContent(String content, Map<String, Object> data) {
//        for (Map.Entry<String, Object> entry : data.entrySet()) {
//            String placeholder = "\\{\\{" + entry.getKey() + "}}";
//            content = content.replaceAll(placeholder, entry.getValue().toString());
//        }
//        return content;
//    }
//
//
//    private void validateNotificationRequest(NotificationRequest request, UserPrincipal userPrincipal) {
//        if (request == null) {
//            throw new BadRequestException("Notification Request is Invalid");
//        }
//        if (userPrincipal == null || userPrincipal.getId() == null) {
//            throw new BadRequestException("User is Invalid");
//        }
//        if (request.getTemplateId() == null) {
//            throw new BadRequestException("Notification Template is Required");
//        }
//        if (CollectionUtils.isEmpty(request.getRecipients())) {
//            throw new BadRequestException("At least one recipient is Required");
//        }
//    }
//
//
//    /**
//     * Soft deletes all notifications for the authenticated user.
//     *
//     *
//     * <p>This method marks all notifications as inactive (soft delete) for the user
//     * and updates the modification details in the database.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Retrieves the user ID from `userPrincipal`.</li>
//     *   <li>Performs an `UPDATE` query on the `NOTIFICATION_RECIPIENT` table:
//     *     <ul>
//     *       <li>Sets `IS_ACTIVE` to `false`, effectively soft-deleting all notifications.</li>
//     *       <li>Updates `UPDATED_BY` with the authenticated user's ID.</li>
//     *       <li>Updates `UPDATED_ON` with the current timestamp.</li>
//     *     </ul>
//     *   </li>
//     *   <li>Filters the update to only affect notifications that belong to the authenticated user.</li>
//     *   <li>Executes the update query and logs the number of affected rows.</li>
//     * </ul>
//     *
//     *
//     * @param userPrincipal The authenticated user initiating the delete-all request.
//     * */
//    public void deleteAll(UserPrincipal userPrincipal) {
//        UUID userId = userPrincipal.getId();
//        int rowsAffected = context.update(NOTIFICATION_RECIPIENT)
//                .set(NOTIFICATION_RECIPIENT.IS_ACTIVE, false)
//                .set(NOTIFICATION_RECIPIENT.UPDATED_BY, userId)
//                .set(NOTIFICATION_RECIPIENT.UPDATED_ON, DateUtils.currentDatetime())
//                .where(NOTIFICATION_RECIPIENT.INDIVIDUAL_ID.eq(userId))
//                .execute();
//        log.info("[{}] rows affected when deleting all notifications by user [{}]", rowsAffected, userPrincipal.getId());
//    }
//
//    /**
//     * Marks all notifications as read for the authenticated user.
//     *
//     *
//     * <p>This method updates all notifications for the user, setting their `STATUS` field to `false`
//     * (indicating they have been read).</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Retrieves the user ID from `userPrincipal`.</li>
//     *   <li>Performs an `UPDATE` query on the `NOTIFICATION_RECIPIENT` table:
//     *     <ul>
//     *       <li>Sets `STATUS` to `false` (marking notifications as read).</li>
//     *       <li>Updates `UPDATED_BY` with the authenticated user's ID.</li>
//     *       <li>Updates `UPDATED_ON` with the current timestamp.</li>
//     *     </ul>
//     *   </li>
//     *   <li>Filters the update to only affect notifications that belong to the authenticated user.</li>
//     *   <li>Executes the update query and logs the number of affected rows.</li>
//     * </ul>
//     *
//     *
//     * @param userPrincipal The authenticated user initiating the mark-all-as-read request.
//     */
//    public void markAllRead(UserPrincipal userPrincipal) {
//        UUID userId = userPrincipal.getId();
//        int rowsAffected = context.update(NOTIFICATION_RECIPIENT)
//                .set(NOTIFICATION_RECIPIENT.STATUS, false)
//                .set(NOTIFICATION_RECIPIENT.UPDATED_BY, userId)
//                .set(NOTIFICATION_RECIPIENT.UPDATED_ON, DateUtils.currentDatetime())
//                .where(NOTIFICATION_RECIPIENT.INDIVIDUAL_ID.eq(userPrincipal.getId()))
//                .execute();
//        log.info("[{}] rows affected when marked All Read notifications by user [{}]", rowsAffected, userPrincipal.getId());
//    }
//
//    public NotificationGetByIdResponse getNotificationById(UUID notificationId) {
//        NotificationGetByIdResponse notificationGetByIdResponse = notificationRepository.notificationGetById(notificationId);
//        if (notificationGetByIdResponse == null) {
//            log.info("An notification is not available with the given notification id :: {}", notificationId);
//            throw new IllegalArgumentException(String.format("An notification is not available with the given [id = %s]"
//                    , notificationId));
//        }
//        return notificationGetByIdResponse;
//    }
//
//
//    /**
//     * Retrieves all notifications with pagination support.
//     *
//     * <p>This method fetches a paginated list of notifications along with the total count.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Ensures default pagination settings are applied using `applyDefaults(paginationRequest)`.</li>
//     *   <li>Fetches a list of notifications using `notificationRepository.notificationGetAllByAdmin(paginationRequest)`.</li>
//     *   <li>Retrieves the total count of notifications using `notificationRepository.notificationGetAllCountByAdmin()`.</li>
//     *   <li>Constructs and returns a NotificationGetAllResponse object containing both the notifications and the count.</li>
//     * </ul>
//     *
//     *
//     * @param paginationRequest The pagination request object containing page number and size.
//     * @return A NotificationGetAllResponse object containing a list of notifications and the total count.
//     */
//    public NotificationGetAllResponse getAllNotificationResponse(PaginationRequest paginationRequest) {
//        applyDefaults(paginationRequest);
//        List<NotificationGetByIdResponse> notificationResponses = notificationRepository.notificationGetAllByAdmin(paginationRequest);
//        Integer count = notificationRepository.notificationGetAllCountByAdmin();
//        return NotificationGetAllResponse.builder()
//                .notificationGetByIdResponseList(notificationResponses)
//                .count(count)
//                .build();
//    }
//
//    private void applyDefaults(PaginationRequest request) {
//        if (request.getPage() == null || request.getPage() < 1) {
//            request.setPage(pageNo);
//        }
//
//        if (request.getPageSize() == null || request.getPageSize() < 1) {
//            request.setPageSize(pageSize);
//        }
//    }
//
//    /**
//     * Updates the status of multiple notifications for the authenticated user.
//     *
//     *
//     * <p>This method updates the status of selected notifications by setting them as read (`true`) or unread (`false`).</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Retrieves the user ID from `userPrincipal`.</li>
//     *   <li>Calls `notificationRepository.markAllStatus(status, notificationRecipientIds, userId)` to update the database.</li>
//     *   <li>Logs the number of affected rows.</li>
//     *   <li>Throws an `IllegalArgumentException` if no notifications were found for the provided IDs.</li>
//     *   <li>Returns the number of updated rows.</li>
//     * </ul>
//     *
//     *
//     * @param status                 The status to set (`true` for read, `false` for unread).
//     * @param notificationRecipientIds A list of UUIDs representing the notification recipients to update.
//     * @param userPrincipal          The authenticated user initiating the update request.
//     * @return The number of notifications updated.
//     * @throws IllegalArgumentException If no notifications match the given recipient IDs.
//     */
//    public Integer markAllAsStatus(Boolean status, List<UUID> notificationRecipientIds, UserPrincipal userPrincipal) {
//        UUID userId = userPrincipal.getId();
//        int rowsAffected = notificationRepository.markAllStatus(status, notificationRecipientIds, userId);
//        log.info("[{}] rows affected when marked All as [{}] notifications by user [{}]", status, rowsAffected
//                , userPrincipal.getId());
//        if (rowsAffected == 0) {
//            throw new IllegalArgumentException(String.format("Exception as there were no notification recipients" +
//                    " presents with the given list of [id's=%s]", notificationRecipientIds));
//        }
//        return rowsAffected;
//    }

}
