package com.chidhagni.donationreceipt.notificationsv2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v2/notifications")
@CrossOrigin
@RequiredArgsConstructor
@Slf4j
public class NotificationControllerV2 {

    private final NotificationServiceV2 notificationServiceV2;


    /**
     * Creates a new notification.
     *
     *
     * <p>This endpoint allows authenticated users to create a new notification.</p>
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>Delegates the notification creation process to 'notificationServiceV2.create'.</li>
     *   <li>Returns the UUID of the newly created notification.</li>
     * </ul>
     *
     *
     * @param request       The request object containing the notification details.
     * @param userPrincipal The authenticated user initiating the request.
     * @return A 'ResponseEntity' containing the UUID of the created notification.
     *
     * <AUTHOR>
     */
//    @PostMapping
//    public ResponseEntity<UUID> create(@RequestBody NotificationRequest request, @CurrentUser UserPrincipal userPrincipal) {
//        log.info("Inserting Notification for request [{}] by user [{}]", request, userPrincipal.getId());
//        UUID notificationId = notificationServiceV2.create(request, userPrincipal);
//        return ResponseEntity.ok().body(notificationId);
//    }
//    /**
//     * Fetches all notifications for the authenticated user.
//     *
//     *
//     * <p>This endpoint allows an authenticated user to retrieve their notifications.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Delegates the notification retrieval process to 'notificationServiceV2.fetchUserNotifications'.</li>
//     *   <li>Returns a NotificationResponses object containing the list of notifications.</li>
//     * </ul>
//     *
//     *
//     * @param userPrincipal The authenticated user performing the request.
//     * @return A 'ResponseEntity' containing a NotificationResponses object with the user's notifications.
//     *
//     * <AUTHOR>
//     */
//    @GetMapping
//    public ResponseEntity<NotificationResponses> fetchUserNotifications(@CurrentUser UserPrincipal userPrincipal) {
//        log.info("Fetching Notifications for User [{}]", userPrincipal.getId());
//        return ResponseEntity.ok().body(notificationServiceV2.fetchUserNotifications(userPrincipal));
//    }
//
//    /**
//     * Updates the notifications for the authenticated user.
//     *
//     * <p>This endpoint allows an authenticated user to update the status of their notifications.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Delegates the update operation to 'notificationServiceV2.update'.</li>
//     *   <li> return HTTP 200 OK status.</li>
//     * </ul>
//     *
//     *
//     * @param request       The request object containing the details of the notifications to be updated.
//     * @param userPrincipal The authenticated user initiating the update request.
//     * @return A 'ResponseEntity' with HTTP status 200 OK if the update is successful.
//     * * @throws InternalServerError If an exception occurs while updating the notifications.
//     *
//     * <AUTHOR>
//     */
//    @PatchMapping
//    public ResponseEntity<Void> update(@RequestBody NotificationUpdateRequest request,
//                                       @CurrentUser UserPrincipal userPrincipal) {
//        log.info("Update Notifications [{}] Status by User [{}]", request, userPrincipal.getId());
//        notificationServiceV2.update(request, userPrincipal);
//        return ResponseEntity.ok().build();
//    }
//
//
//    /**
//     * Deletes notifications for the authenticated user.
//     *
//     *
//     * <p>This endpoint allows an authenticated user to delete specified notifications.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Delegates the deletion operation to 'notificationServiceV2.delete'.</li>
//     *   <li>Returns an HTTP 200 OK status with no response body upon successful deletion.</li>
//     * </ul>
//     *
//     *
//     * @param request       The request object containing the details of the notifications to be deleted.
//     * @param userPrincipal The authenticated user initiating the delete request.
//     * @return A 'ResponseEntity' with HTTP status 200 OK if the deletion is successful.
//     * * @throws InternalServerError If an exception occurs while deleting the notifications.
//     *
//     * <AUTHOR>
//     */
//    @DeleteMapping
//    public ResponseEntity<Void> delete(@RequestBody NotificationUpdateRequest request,
//                                       @CurrentUser UserPrincipal userPrincipal) {
//        log.info("Delete Notifications [{}] by User [{}]", request, userPrincipal.getId());
//        notificationServiceV2.delete(request, userPrincipal);
//        return ResponseEntity.ok().build();
//    }
//
//
//    /**
//     * Deletes all notifications for the authenticated user.
//     *
//     * <p>This endpoint allows an authenticated user to  delete all their notifications.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Logs the request with the user ID for tracking purposes.</li>
//     *   <li>Calls 'notificationServiceV2.deleteAll' to perform the deletion.</li>
//     *   <li>Returns an HTTP 200 OK response with no body upon successful deletion.</li>
//     * </ul>
//     *
//     *
//     * @param userPrincipal The authenticated user initiating the delete-all request.
//     * @return A `ResponseEntity` with HTTP status 200 OK if the deletion is successful.
//     * * @throws InternalServerError If an exception occurs while deleting the notifications.
//     *
//     * <AUTHOR>
//     */
//    @DeleteMapping("/all")
//    public ResponseEntity<Void> deleteAll(@CurrentUser UserPrincipal userPrincipal) {
//        log.info("Delete All Notifications by User [{}]", userPrincipal.getId());
//        notificationServiceV2.deleteAll(userPrincipal);
//        return ResponseEntity.ok().build();
//    }
//
//
//    /**
//     * Marks all notifications as read for the authenticated user.
//     *
//     *
//     * <p>This endpoint allows an authenticated user to mark all their notifications as read.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Logs the request with the user ID for tracking purposes.</li>
//     *   <li>Calls 'notificationServiceV2.markAllRead' to update the notification status.</li>
//     *   <li>Returns an HTTP 200 OK response with no body upon successful operation.</li>
//     * </ul>
//     *
//     *
//     * @param userPrincipal The authenticated user initiating the mark-all-as-read request.
//     * @return A `ResponseEntity` with HTTP status 200 OK if the operation is successful.
//     *
//     * <AUTHOR>
//     */
//    @PatchMapping("/mark/all")
//    public ResponseEntity<Void> markAllRead(@CurrentUser UserPrincipal userPrincipal) {
//        log.info("Mark All as ReadNotifications by User [{}]", userPrincipal.getId());
//        notificationServiceV2.markAllRead(userPrincipal);
//        return ResponseEntity.ok().build();
//    }
//
//    /**
//     * Retrieves all notifications with optional pagination.
//     *
//     *
//     * <p>This endpoint allows users to retrieve all notifications with optional pagination.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>If no pagination request is provided, a default pagination request is assigned.</li>
//     *   <li>Delegates the fetching operation to 'notificationServiceV2.getAllNotificationResponse'.</li>
//     *   <li>Returns a paginated response containing all notifications.</li>
//     * </ul>
//     *
//     *
//     * @param paginationRequest (Optional) The pagination request object to limit results.
//     * @return A `ResponseEntity` containing a NotificationGetAllResponse object with all notifications.
//     *
//     *
//     * <AUTHOR>
//     */
//    @PostMapping("/all")
//    public ResponseEntity<NotificationGetAllResponse> getAllNotifications(@RequestBody(required = false) PaginationRequest paginationRequest) {
//        if (paginationRequest == null) {
//            log.info("paginatin request is null so assigning default pagination request");
//            paginationRequest = PaginationRequest.builder().build();
//        }
//        return new ResponseEntity<>(notificationServiceV2.getAllNotificationResponse(paginationRequest), HttpStatus.ACCEPTED);
//    }
//
//    /**
//     * Retrieves a notification by its ID
//     *
//     *
//     * <p>This endpoint allows users to retrieve a specific notification by its Id.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Delegates the fetch operation to 'notificationServiceV2.getNotificationById'.</li>
//     *   <li>Returns the notification details wrapped in a  NotificationGetByIdResponse object.</li>
//     * </ul>
//     *
//     *
//     * @param notificationId The unique identifier of the notification.
//     * @return A 'ResponseEntity' containing a  NotificationGetByIdResponse object with the notification details.
//     *
//     * <AUTHOR>
//     */
//    @GetMapping("/{id}")
//    public ResponseEntity<NotificationGetByIdResponse> getNotificationById(@PathVariable("id") UUID notificationId) {
//        return new ResponseEntity<>(notificationServiceV2.getNotificationById(notificationId), HttpStatus.ACCEPTED);
//    }
//
//
//    /**
//     * Updates the status of multiple notifications for the authenticated user.
//     *
//     *
//     * <p>This endpoint allows an authenticated user to mark multiple notifications as either read or unread.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>If the 'status' parameter is null, it defaults to 'false'.</li>
//     *   <li>Logs the request with the user ID and status for tracking purposes.</li>
//     *   <li>Delegates the update operation to 'notificationServiceV2.markAllAsStatus'.</li>
//     *   <li>Returns the number of rows updated.</li>
//     * </ul>
//     *
//     *
//     * @param status                 The status to be updated (`true` for read, `false` for unread). Defaults to `false` if null.
//     * @param notificationRecipientIds A list of notification recipient UUIDs to be updated.
//     * @param userPrincipal          The authenticated user initiating the update request.
//     * @return A 'ResponseEntity' containing the number of notifications updated.
//     *
//     *
//     * <AUTHOR>
//     */
//    @PatchMapping("/mark/all-as/{status}")
//    public ResponseEntity<Integer> markAllStatus(@PathVariable("status") Boolean status
//            , @RequestBody List<UUID> notificationRecipientIds
//            , @CurrentUser UserPrincipal userPrincipal) {
//        if (status == null) {
//            status = Boolean.FALSE;
//        }
//        log.info("Mark All as [{}] by User [{}]", status, userPrincipal.getId());
//        Integer rowsUpdated = notificationServiceV2.markAllAsStatus(status, notificationRecipientIds, userPrincipal);
//        return ResponseEntity.ok().body(rowsUpdated);
//    }

}
