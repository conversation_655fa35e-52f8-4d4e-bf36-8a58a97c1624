package com.chidhagni.donationreceipt.notificationsv2.mapper;

//import com.chidhagni.houzer.db.jooq.tables.pojos.NotificationsV2;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface NotificationMapperV2 {
//    @Mapping(target = "id", source = "id")
//    @Mapping(target = "templateId", source = "request.templateId")
//    @Mapping(target = "metaData", source = "request.metaData")
//    @Mapping(target = "isActive", expression = "java(Boolean.TRUE)")
//    @Mapping(target = "createdBy", source = "loggedInUserId")
//    @Mapping(target = "updatedBy", source = "loggedInUserId")
//    @Mapping(target = "createdOn", expression = "java(java.time.LocalDateTime.now())")
//    @Mapping(target = "updatedOn", expression = "java(java.time.LocalDateTime.now())")
//    NotificationsV2 mapNotificationRequestToNotificationsV2(UUID id, NotificationRequest request, UUID loggedInUserId);
}
