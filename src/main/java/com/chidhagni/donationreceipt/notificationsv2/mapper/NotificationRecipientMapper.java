package com.chidhagni.donationreceipt.notificationsv2.mapper;

//import com.chidhagni.houzer.db.jooq.tables.pojos.NotificationRecipient;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface NotificationRecipientMapper {
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "individualId", source = "individualId")
//    @Mapping(target = "notificationId", source = "notificationId")
//    @Mapping(target = "status", expression = "java(Boolean.FALSE)") // IsRead
//    @Mapping(target = "sentCount", constant = "1")
//    @Mapping(target = "isActive", expression = "java(Boolean.TRUE)")
//    @Mapping(target = "createdBy", source = "loggedInUserId")
//    @Mapping(target = "updatedBy", source = "loggedInUserId")
//    @Mapping(target = "createdOn", expression = "java(java.time.LocalDateTime.now())")
//    @Mapping(target = "updatedOn", expression = "java(java.time.LocalDateTime.now())")
//    NotificationRecipient mapNotificationRequestToNotificationRecipient(UUID notificationId, UUID individualId, UUID loggedInUserId);
}
