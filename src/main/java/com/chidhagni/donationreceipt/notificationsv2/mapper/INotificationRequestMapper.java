package com.chidhagni.donationreceipt.notificationsv2.mapper;

import com.chidhagni.donationreceipt.notificationsv2.dto.request.NotificationRequest;
import com.chidhagni.donationreceipt.notificationsv2.dto.request.NotificationsMetaData;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.UUID;

@Mapper(componentModel = "spring")
public interface INotificationRequestMapper {
    NotificationRequest mapToNotificationRequest(UUID templateId, NotificationsMetaData metaData, List<UUID> recipients);
}