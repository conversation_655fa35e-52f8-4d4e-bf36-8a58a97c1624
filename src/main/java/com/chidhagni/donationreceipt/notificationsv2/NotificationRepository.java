package com.chidhagni.donationreceipt.notificationsv2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;


@Slf4j
@Repository
@RequiredArgsConstructor
public class NotificationRepository {
//    private final NotificationsV2Dao notificationsV2Dao;
//
//
//    /**
//     * Retrieves all notifications for admin users with pagination.
//     *
//     *
//     * <p>This method fetches a paginated list of notifications, including their metadata and recipients,
//     * for admin.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Calculates the pagination offset.</li>
//     *   <li>Executes a database query to retrieve notifications from 'NOTIFICATIONS_V2',
//     *       joining with 'NOTIFICATION_TEMPLATES' and 'INDIVIDUAL' (for created_on and updated_on details).</li>
//     *   <li>Fetches notification recipients from 'NOTIFICATION_RECIPIENT' and groups them by `NOTIFICATION_ID`.</li>
//     *   <li>Maps each notification to a 'NotificationGetByIdResponse', attaching the relevant recipients.</li>
//     *   <li>Returns the list of notifications, each containing recipient details.</li>
//     * </ul>
//     *
//     *
//     * @param request The pagination request object containing page number and page size.
//     * @return A list of NotificationGetByIdResponse objects containing notification details and recipients.
//     * @throws InternalServerError If an error occurs while fetching notifications.
//     */
//    public List<NotificationGetByIdResponse> notificationGetAllByAdmin(PaginationRequest request) {
//        try {
//            Integer offset = (request.getPage() - 1) * request.getPageSize();
//            List<NotificationGetByIdResponse> notifications = notificationsV2Dao.ctx().select(
//                            NOTIFICATIONS_V2.ID.as("id"),
//                            NOTIFICATIONS_V2.TEMPLATE_ID.as("templateId"),
//                            NOTIFICATION_TEMPLATES.LEVEL1_CONTENT.as("levelOneContent"),
//                            NOTIFICATION_TEMPLATES.LEVEL2_CONTENT.as("levelTwoContent"),
//                            NOTIFICATION_TEMPLATES.TYPE.as("type"),
//                            NOTIFICATIONS_V2.META_DATA.as("metaData"),
//                            NOTIFICATIONS_V2.IS_ACTIVE.as("isActive"),
//                            INDIVIDUAL.as("I0").EMAIL.as("createdBy"),
//                            INDIVIDUAL.as("I1").EMAIL.as("updatedBy"),
//                            NOTIFICATIONS_V2.CREATED_ON.as("createdOn"),
//                            NOTIFICATIONS_V2.UPDATED_ON.as("updatedOn")
//                    )
//                    .from(NOTIFICATIONS_V2)
//                    .leftJoin(NOTIFICATION_TEMPLATES).on(NOTIFICATIONS_V2.TEMPLATE_ID.eq(NOTIFICATION_TEMPLATES.ID))
//                    .leftJoin(INDIVIDUAL.as("I0")).on(INDIVIDUAL.as("I0").ID.eq(NOTIFICATIONS_V2.CREATED_BY))
//                    .leftJoin(INDIVIDUAL.as("I1")).on(INDIVIDUAL.as("I1").ID.eq(NOTIFICATIONS_V2.UPDATED_BY))
//                    .orderBy(NOTIFICATIONS_V2.CREATED_BY.desc())
//                    .limit(request.getPageSize())
//                    .offset(offset)
//                    .fetchInto(NotificationGetByIdResponse.class);
//
//            Map<UUID, List<NotificationRecipientsDto>> recipientsMap = notificationsV2Dao.ctx()
//                    .select(
//                            NOTIFICATION_RECIPIENT.NOTIFICATION_ID,
//                            NOTIFICATION_RECIPIENT.ID.as("id"),
//                            NOTIFICATION_RECIPIENT.INDIVIDUAL_ID.as("individualId"),
//                            NOTIFICATION_RECIPIENT.STATUS.as("status"),
//                            NOTIFICATION_RECIPIENT.SENT_COUNT.as("sentCount"),
//                            INDIVIDUAL.as("I0").EMAIL.as("createdBy"),
//                            INDIVIDUAL.as("I1").EMAIL.as("updatedBy"),
//                            NOTIFICATION_RECIPIENT.CREATED_ON.as("createdOn"),
//                            NOTIFICATION_RECIPIENT.UPDATED_ON.as("updatedOn")
//                    )
//                    .from(NOTIFICATION_RECIPIENT)
//                    .leftJoin(INDIVIDUAL.as("I0")).on(INDIVIDUAL.as("I0").ID.eq(NOTIFICATION_RECIPIENT.CREATED_BY))
//                    .leftJoin(INDIVIDUAL.as("I1")).on(INDIVIDUAL.as("I1").ID.eq(NOTIFICATION_RECIPIENT.UPDATED_BY))
//                    .fetchGroups(NOTIFICATION_RECIPIENT.NOTIFICATION_ID, NotificationRecipientsDto.class);
//
//            return notifications.stream().map(notification -> {
//                List<NotificationRecipientsDto> recipients = recipientsMap.get(notification.getId());
//                notification.setRecipients(recipients != null ? recipients : new ArrayList<>());
//                return notification;
//            }).collect(Collectors.toList());
//
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while fetching all notifications by admin", ex);
//        }
//    }
//
//    /**
//     * Retrieves the total count of all notifications for admin users.
//     *
//     *
//     * <p>This method calculates the total number of notifications available for admin users.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Executes a 'SELECT COUNT(*)' query on the 'NOTIFICATIONS_V2' table.</li>
//     *   <li>Joins with 'NOTIFICATION_TEMPLATES' to ensure template-related filtering if needed.</li>
//     *   <li>Joins with the 'INDIVIDUAL' table to include information about the notification created_on and updated_on.</li>
//     *   <li>Retrieves and returns the total count as an `Integer`.</li>
//     * </ul>
//     *
//     *
//     * @return The total count of notifications for admin users.
//     * @throws InternalServerError If an error occurs while fetching the count from the database.
//     */
//    public Integer notificationGetAllCountByAdmin() {
//        try {
//            return notificationsV2Dao.ctx().selectCount()
//                    .from(NOTIFICATIONS_V2)
//                    .leftJoin(NOTIFICATION_TEMPLATES).on(NOTIFICATIONS_V2.TEMPLATE_ID.eq(NOTIFICATION_TEMPLATES.ID))
//                    .leftJoin(INDIVIDUAL.as("I0")).on(INDIVIDUAL.as("I0").ID.eq(NOTIFICATIONS_V2.CREATED_BY))
//                    .leftJoin(INDIVIDUAL.as("I1")).on(INDIVIDUAL.as("I1").ID.eq(NOTIFICATIONS_V2.UPDATED_BY))
//                    .fetchOneInto(Integer.class);
//
//
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while fetching all notifications count by admin", ex);
//        }
//    }
//
//
//    /**
//     * Retrieves a specific notification by its unique identifier.
//     *
//     *
//     * <p>This method fetches a single notification along with its metadata and recipients.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Executes a database query to retrieve the notification details from `NOTIFICATIONS_V2`.</li>
//     *   <li>Joins with `NOTIFICATION_TEMPLATES` to fetch template-related data.</li>
//     *   <li>Joins with `INDIVIDUAL` table to retrieve the creator and updater information.</li>
//     *   <li>Filters the query to match the provided `notificationId`.</li>
//     *   <li>Fetches the associated recipients from `NOTIFICATION_RECIPIENT`.</li>
//     *   <li>Maps recipient details into a list of NotificationRecipientsDto.</li>
//     *   <li>Attaches the recipient list to the NotificationGetByIdResponse object.</li>
//     *   <li>Returns the fully constructed notification response.</li>
//     * </ul>
//     *
//     *
//     * @param notificationId The unique identifier of the notification.
//     * @return A NotificationGetByIdResponse object containing the notification details and its recipients.
//     * @throws InternalServerError If an error occurs while fetching the notification.
//     */
//    public NotificationGetByIdResponse notificationGetById(UUID notificationId) {
//        try {
//            NotificationGetByIdResponse notification = notificationsV2Dao.ctx().select(
//                            NOTIFICATIONS_V2.ID.as("id"),
//                            NOTIFICATIONS_V2.TEMPLATE_ID.as("templateId"),
//                            NOTIFICATION_TEMPLATES.LEVEL1_CONTENT.as("levelOneContent"),
//                            NOTIFICATION_TEMPLATES.LEVEL2_CONTENT.as("levelTwoContent"),
//                            NOTIFICATION_TEMPLATES.TYPE.as("type"),
//                            NOTIFICATIONS_V2.META_DATA.as("metaData"),
//                            NOTIFICATIONS_V2.IS_ACTIVE.as("isActive"),
//                            INDIVIDUAL.as("I0").EMAIL.as("createdBy"),
//                            INDIVIDUAL.as("I1").EMAIL.as("updatedBy"),
//                            NOTIFICATIONS_V2.CREATED_ON.as("createdOn"),
//                            NOTIFICATIONS_V2.UPDATED_ON.as("updatedOn")
//                    )
//                    .from(NOTIFICATIONS_V2)
//                    .leftJoin(NOTIFICATION_TEMPLATES).on(NOTIFICATIONS_V2.TEMPLATE_ID.eq(NOTIFICATION_TEMPLATES.ID))
//                    .leftJoin(INDIVIDUAL.as("I0")).on(INDIVIDUAL.as("I0").ID.eq(NOTIFICATIONS_V2.CREATED_BY))
//                    .leftJoin(INDIVIDUAL.as("I1")).on(INDIVIDUAL.as("I1").ID.eq(NOTIFICATIONS_V2.UPDATED_BY))
//                    .where(NOTIFICATIONS_V2.ID.eq(notificationId))
//                    .fetchOneInto(NotificationGetByIdResponse.class);
//            List<NotificationRecipientsDto> recipients = notificationsV2Dao.ctx()
//                    .select(
//                            NOTIFICATION_RECIPIENT.NOTIFICATION_ID,
//                            NOTIFICATION_RECIPIENT.ID.as("id"),
//                            NOTIFICATION_RECIPIENT.INDIVIDUAL_ID.as("individualId"),
//                            NOTIFICATION_RECIPIENT.STATUS.as("status"),
//                            NOTIFICATION_RECIPIENT.SENT_COUNT.as("sentCount"),
//                            INDIVIDUAL.as("I0").EMAIL.as("createdBy"),
//                            INDIVIDUAL.as("I1").EMAIL.as("updatedBy"),
//                            NOTIFICATION_RECIPIENT.CREATED_ON.as("createdOn"),
//                            NOTIFICATION_RECIPIENT.UPDATED_ON.as("updatedOn")
//                    )
//                    .from(NOTIFICATION_RECIPIENT)
//                    .leftJoin(INDIVIDUAL.as("I0")).on(INDIVIDUAL.as("I0").ID.eq(NOTIFICATION_RECIPIENT.CREATED_BY))
//                    .leftJoin(INDIVIDUAL.as("I1")).on(INDIVIDUAL.as("I1").ID.eq(NOTIFICATION_RECIPIENT.UPDATED_BY))
//                    .where(NOTIFICATION_RECIPIENT.NOTIFICATION_ID.eq(notificationId))
//                    .fetchInto(NotificationRecipientsDto.class);
//            notification.setRecipients(recipients);
//            return notification;
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while fetching all notifications by admin", ex);
//        }
//    }
//
//
//    /**
//     * Updates the status of multiple notifications for a given list of recipients.
//     *
//     *
//     * <p>This method updates the status of selected notifications for specified recipients,
//     * marking them as either read ('true') or unread ('false').</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Executes an 'UPDATE' query on the `NOTIFICATION_RECIPIENT` table:</li>
//     *   <ul>
//     *     <li>Sets 'STATUS' to the provided 'status' value ('true' for read, 'false' for unread).</li>
//     *     <li>Updates 'UPDATED_BY' with the provided 'userId'.</li>
//     *     <li>Updates 'UPDATED_ON' with the current timestamp.</li>
//     *   </ul>
//     *   <li>Filters the update to only affect notifications matching the list of 'notificationRecipientIds'.</li>
//     *   <li>Executes the update and logs the number of affected rows.</li>
//     * </ul>
//     *
//     *
//     * @param status                 The status to set ('true' for read, 'false' for unread).
//     * @param notificationRecipientIds A list of UUIDs representing the notification recipients to update.
//     * @param userId                 The UUID of the user initiating the update request.
//     * @return The number of notifications updated.
//     * @throws InternalServerError If an error occurs while updating the notifications.
//     */
//    public Integer markAllStatus(Boolean status, List<UUID> notificationRecipientIds, UUID userId) {
//        try {
//            int rowsAffected = notificationsV2Dao.ctx().update(Tables.NOTIFICATION_RECIPIENT)
//                    .set(Tables.NOTIFICATION_RECIPIENT.STATUS, status)
//                    .set(Tables.NOTIFICATION_RECIPIENT.UPDATED_BY, userId)
//                    .set(Tables.NOTIFICATION_RECIPIENT.UPDATED_ON, DateUtils.currentDatetime())
//                    .where(Tables.NOTIFICATION_RECIPIENT.ID.in(notificationRecipientIds))
//                    .execute();
//            log.info("[{}] rows affected when marked All as [{}] notifications by user [{}]", rowsAffected, status
//                    , userId);
//            return rowsAffected;
//        } catch (Exception ex) {
//            throw new InternalServerError(String.format("Exception while patching all notification recipients status as :: " +
//                    "{} by user of id :: {}", status, userId), ex);
//        }
//    }
}
