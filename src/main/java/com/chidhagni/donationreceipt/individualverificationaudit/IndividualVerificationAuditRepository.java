package com.chidhagni.donationreceipt.individualverificationaudit;


import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualVerificationAuditDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualVerificationAudit;
import com.chidhagni.donationreceipt.individualverificationaudit.constants.VerificationStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.Email;
import java.util.List;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.IndividualVerificationAudit.INDIVIDUAL_VERIFICATION_AUDIT;

@Slf4j
@Repository
@RequiredArgsConstructor
public class IndividualVerificationAuditRepository {
    private final IndividualVerificationAuditDao individualVerificationAuditDao;

    public void insertIndividualVerificationAudit(IndividualVerificationAudit individualVerificationAudit) {
        try{
            individualVerificationAuditDao.insert(individualVerificationAudit);
            log.info("Individual Verification Audit inserted successfully");
        } catch (Exception e) {
            throw new InternalServerError("Exception while inserting the individual verification audit", e);
        }
    }

    public List<IndividualVerificationAudit> getByEmail(String email) {

        return individualVerificationAuditDao.fetchByContactValue(email);
    }

    public void updateIndividualVerificationAudit(IndividualVerificationAudit individualVerificationAudit) {
        try{
            individualVerificationAuditDao.update(individualVerificationAudit);
            log.info("Individual Verification Audit updated successfully");
        } catch (Exception e) {
            throw new InternalServerError("Exception while updating the individual verification audit", e);
        }
    }

    public List<IndividualVerificationAudit> getByOrgIdAndPending(UUID orgId) {

        return individualVerificationAuditDao.ctx().select()
                .from(INDIVIDUAL_VERIFICATION_AUDIT)
                .where(INDIVIDUAL_VERIFICATION_AUDIT.ORG_ID.eq(orgId))
                .and(INDIVIDUAL_VERIFICATION_AUDIT.VERIFICATION_STATUS.eq(VerificationStatusEnum.PENDING.name()))
                .fetchInto(IndividualVerificationAudit.class);
    }


    public IndividualVerificationAudit findByContactValueAndContactType(String contactValue,String contactType)
    {
        return individualVerificationAuditDao.ctx().select()
                .from(INDIVIDUAL_VERIFICATION_AUDIT)
                .where(INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_VALUE.eq(contactValue)
                        .and(INDIVIDUAL_VERIFICATION_AUDIT.CONTACT_TYPE.eq(contactType)))
                .fetchOneInto(IndividualVerificationAudit.class);
    }
}