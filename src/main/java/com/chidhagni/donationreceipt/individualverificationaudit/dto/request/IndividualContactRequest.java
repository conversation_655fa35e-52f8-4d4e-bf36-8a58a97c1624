package com.chidhagni.donationreceipt.individualverificationaudit.dto.request;


import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

@Data
@Builder
public class IndividualContactRequest {
    @Email(regexp = "[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,6}", flags = Pattern.Flag.CASE_INSENSITIVE)
    private String email;
    @Size(max = 10, message = "Mobile number must be 10 digits or fewer")
    private String mobile;
    private String role;
}
