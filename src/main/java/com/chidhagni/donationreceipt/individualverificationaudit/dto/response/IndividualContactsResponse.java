package com.chidhagni.donationreceipt.individualverificationaudit.dto.response;


//import com.chidhagni.houzer.individualcontacts.constants.StatusEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndividualContactsResponse {
    private UUID id;
    private String email;
    private String mobile;
   // private StatusEnums status;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}
