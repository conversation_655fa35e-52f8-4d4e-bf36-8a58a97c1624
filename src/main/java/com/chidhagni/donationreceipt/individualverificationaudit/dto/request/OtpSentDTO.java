package com.chidhagni.donationreceipt.individualverificationaudit.dto.request;

import com.chidhagni.donationreceipt.individualverificationaudit.constants.ContactType;
import com.chidhagni.donationreceipt.organisation.constants.DefaultOrganisationCategoryEnums;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.Pattern;


@Data
@Builder
public class OtpSentDTO {
    private ContactType contactType;
    @Pattern(
            regexp = "^(?:[6-9]\\d{9}|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})$",
            message = "Invalid contact value. Provide a valid email or mobile number"
    )
    private String contactValue;
    @Pattern(
            regexp = "^((25[0-5]|2[0-4]\\d|[0-1]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[0-1]?\\d\\d?)$",
            message = "Invalid IP address format"
    )
    private String ipAddress;
    private DefaultOrganisationCategoryEnums defaultOrganisationCategoryEnums;
}
