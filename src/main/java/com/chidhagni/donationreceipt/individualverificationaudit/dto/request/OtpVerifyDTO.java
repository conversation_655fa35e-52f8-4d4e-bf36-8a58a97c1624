package com.chidhagni.donationreceipt.individualverificationaudit.dto.request;

import com.chidhagni.donationreceipt.individualverificationaudit.constants.ContactType;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.Pattern;

@Data
@Builder
public class OtpVerifyDTO {
    private ContactType contactType;
    @Pattern(
            regexp = "^(?:[6-9]\\d{9}|[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,})$",
            message = "Invalid contact value. Provide a valid email or mobile number"
    )
    private String contactValue;
    @Pattern(
            regexp = "^\\d{6}$",
            message = "Invalid OTP format. OTP must be exactly 6 digits."
    )
    private String otp;
}
