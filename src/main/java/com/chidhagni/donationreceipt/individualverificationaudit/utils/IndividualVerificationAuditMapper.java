package com.chidhagni.donationreceipt.individualverificationaudit.utils;

import org.mapstruct.*;


@Mapper(componentModel = "spring")
public interface IndividualVerificationAuditMapper {

//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "contactType", source = "otpSentDTO.contactType")
//    @Mapping(target = "contactValue", source = "otpSentDTO.contactValue")
//    @Mapping(target = "ipAddress", source = "otpSentDTO.ipAddress")
//    @Mapping(target = "verificationStatus",
//            expression = "java(String.valueOf(com.chidhagni.houzer.individualverificationaudit" +
//                    ".constants.VerificationStatusEnum.PENDING))")
//    @Mapping(target = "isActive", expression = "java(true)")
//    @Mapping(target = "createdBy", expression = "java(userPrincipal.getId())")
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "role",source = "otpSentDTO.defaultOrganisationCategoryEnums")
//    IndividualVerificationAudit mapDTOToIndividualVerificationAudit(OtpSentDTO otpSentDTO, UserPrincipal userPrincipal);
//
//    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
//    @Mapping(target = "id", ignore = true)
//    @Mapping(target = "contactType", ignore = true)
//    @Mapping(target = "contactValue", ignore = true)
//    @Mapping(target = "ipAddress", ignore = true)
//    @Mapping(target = "isActive", ignore = true)
//    @Mapping(target = "createdBy", ignore = true)
//    @Mapping(target = "createdOn", ignore = true)
//    @Mapping(target = "role", ignore = true)
//    @Mapping(target = "otpCode", expression = "java(null)")
//    @Mapping(target = "otpCreatedAt", expression = "java(null)")
//    @Mapping(target = "otpExpiresAt", expression = "java(null)")
//    @Mapping(target = "otpVerifiedAt", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "verificationStatus",
//            expression = "java(String.valueOf(com.chidhagni.houzer.individualverificationaudit" +
//                    ".constants.VerificationStatusEnum.VERIFIED))")
//    @Mapping(target = "updatedBy", expression = "java(userPrincipal.getId())")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    void updateIndividualVerificationAudit(@MappingTarget IndividualVerificationAudit individualVerificationAudit
//            , UserPrincipal userPrincipal);
//
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "contactType"
//         , expression = "java(com.chidhagni.houzer.individualverificationaudit.constants.ContactType.OAUTH.toString())")
//    @Mapping(target = "contactValue", source = "email")
//    @Mapping(target = "otpVerifiedAt", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "role", expression = "java(defaultOrganisationCategoryEnums.toString())")
//    @Mapping(target = "verificationStatus", expression
//        = "java(com.chidhagni.houzer.individualverificationaudit.constants.VerificationStatusEnum.VERIFIED.toString())")
//    @Mapping(target = "socialLoginProvider", source = "provider")
//    @Mapping(target = "socialLoginProviderId", source = "providerId")
//    @Mapping(target = "socialLoginProviderImageUrl", source = "imageUrl")
//    @Mapping(target = "isActive", expression = "java(Boolean.TRUE)")
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    IndividualVerificationAudit createIndividualVerificationAuditForOauth(String email
//            , DefaultOrganisationCategoryEnums defaultOrganisationCategoryEnums, String provider
//            , String providerId, String imageUrl);
//
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "contactType"
//            , expression = "java(individualContactRequest.getEmail() != null " +
//            "? com.chidhagni.houzer.individualverificationaudit.constants.ContactType.EMAIL.toString() " +
//            ": com.chidhagni.houzer.individualverificationaudit.constants.ContactType.MOBILE.toString())")
//    @Mapping(target = "contactValue", expression = "java(individualContactRequest.getEmail() != null " +
//            "? individualContactRequest.getEmail().toString()" +
//            ": individualContactRequest.getMobile().toString())")
//    @Mapping(target = "otpCode", ignore = true)
//    @Mapping(target = "otpCreatedAt", ignore = true)
//    @Mapping(target = "otpExpiresAt", ignore = true)
//    @Mapping(target = "otpVerifiedAt", ignore = true)
//    @Mapping(target = "ipAddress", ignore = true)
//    @Mapping(target = "role", source = "role")
//    @Mapping(target = "verificationStatus", expression = "java(com.chidhagni.houzer.individualverificationaudit.constants.VerificationStatusEnum.NOT_VERIFIED_INDIVIDUAL_CONTACT.toString())")
//    @Mapping(target = "isActive", expression = "java(true)")
//    @Mapping(target = "createdBy", ignore = true)
//    @Mapping(target = "updatedBy", ignore = true)
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    IndividualVerificationAudit mapIndividualContactRequestToVerificationAudit(
//            IndividualContactRequest individualContactRequest);
}
