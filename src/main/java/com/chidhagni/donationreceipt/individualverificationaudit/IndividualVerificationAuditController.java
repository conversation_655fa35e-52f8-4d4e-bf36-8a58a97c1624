package com.chidhagni.donationreceipt.individualverificationaudit;

import com.chidhagni.donationreceipt.individualverificationaudit.dto.request.EmailCheckRequestDTO;
import com.chidhagni.donationreceipt.individualverificationaudit.dto.response.EmailVerifyResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



//import com.chidhagni.houzer.individualverificationaudit.dto.request.IndividualContactRequest;
//import com.chidhagni.houzer.individualverificationaudit.dto.request.OtpSentDTO;
//import com.chidhagni.houzer.individualverificationaudit.dto.request.OtpVerifyDTO;
//import com.chidhagni.houzer.individualverificationaudit.dto.request.ReSentOtpDTO;
//import com.chidhagni.houzer.individualverificationaudit.dto.response.EmailVerifyResponseDTO;
//import com.chidhagni.houzer.individualverificationaudit.dto.response.GetAllIndividualVerifiationResponse;
//import com.chidhagni.houzer.individualverificationaudit.dto.response.SentOrResentOTPResponseDTO;
//import com.chidhagni.houzer.individualverificationaudit.dto.response.OtpVerifyResponseDTO;
//import com.chidhagni.houzer.individualverificationaudit.dto.response.PaginationRequest;
//import com.chidhagni.houzer.individualverificationaudit.dto.response.*;
//import com.chidhagni.houzer.security.CurrentUser;
//import com.chidhagni.houzer.security.UserPrincipal;
//import lombok.RequiredArgsConstructor;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import javax.mail.MessagingException;
//
//import java.util.List;
//import java.util.UUID;
//
//import static com.chidhagni.houzer.individualverificationaudit.constants.IndividualVerificationAuditMetaData.*;
//
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class IndividualVerificationAuditController {

    private final IndividualVerificationAuditService individualVerificationAuditService;

////    @PostMapping(path = "/api/v1/send-otp", produces = OTP_SENT_RESPONSE, consumes = OTP_SENT_REQUEST)
////    public ResponseEntity<SentOrResentOTPResponseDTO> sentOtp
////            (@RequestBody OtpSentDTO otpSentDTO, @CurrentUser UserPrincipal userPrincipal) throws MessagingException {
////        return new ResponseEntity<>(individualVerificationAuditService.sendOtp(otpSentDTO, userPrincipal)
////                , HttpStatus.OK);
////    }
////
////    @PatchMapping(path = "/api/v1/verify-otp", produces = OTP_VERIFY_RESPONSE, consumes = OTP_VERIFY_REQUEST)
////    public ResponseEntity<OtpVerifyResponseDTO> verifyOtp
////            (@RequestBody OtpVerifyDTO otpVerifyDTO, @CurrentUser UserPrincipal userPrincipal) throws MessagingException {
////        return new ResponseEntity<>(individualVerificationAuditService.verifyOtp(otpVerifyDTO, userPrincipal)
////                , HttpStatus.OK);
////    }
////
////    @PatchMapping(path = "/api/v1/resend-otp", produces = OTP_RE_SENT_RESPONSE, consumes = OTP_RE_SENT_REQUEST)
////    public ResponseEntity<SentOrResentOTPResponseDTO> reSendOtp
////            (@RequestBody ReSentOtpDTO reSentOtpDTO, @CurrentUser UserPrincipal userPrincipal) throws MessagingException {
////        return new ResponseEntity<>(individualVerificationAuditService.reSendOtp(reSentOtpDTO, userPrincipal)
////                , HttpStatus.OK);
////    }
////
////    @GetMapping(path = "api/v1/check-email-verified/{email}", produces = CHECK_EMAIL_VERIFIED_RESPONSE)
////    public ResponseEntity<EmailVerifyResponseDTO> checkIfEmailVerified(@PathVariable("email") String email){
////        return new ResponseEntity<>(individualVerificationAuditService.checkIfEmailVerified(email)
////                , HttpStatus.OK);
////    }
////
////    @PostMapping(path = "api/v1/leads-contact", consumes = LEADS_CONTACT_INFO_REQUEST
////            , produces = LEADS_CONTACT_INFO_RESPONSE)
////    public ResponseEntity<UUID> leadsContactInfoWithOutRegistering(@RequestBody IndividualContactRequest individualContactRequest){
////        return new ResponseEntity<>(
////                individualVerificationAuditService.insertLeadsInfoWithStatusNotVerified(individualContactRequest)
////                , HttpStatus.OK);
////    }
////
////    @PostMapping(path = "/all")
////    public ResponseEntity<GetAllIndividualVerifiationResponse> getAllIndividualVerificationResponses(
////            @RequestBody(required = false) PaginationRequest paginationRequest) {
////
////        if (paginationRequest == null) {
////            paginationRequest = PaginationRequest.builder()
////                    .page(1)
////                    .pageSize(10)
////                    .searchKeyword("")
////                    .build();
////        }
////
////        return ResponseEntity.ok().body(individualVerificationAuditService.getAllIndividualVerificationResponses(paginationRequest));
////    }
//

    @PostMapping("/check-email")
    public ResponseEntity<EmailVerifyResponseDTO> checkEmail(@RequestBody EmailCheckRequestDTO request) {
        EmailVerifyResponseDTO response = individualVerificationAuditService.checkEmailExists(request.getEmail());
        return ResponseEntity.ok(response);
    }

}
