package com.chidhagni.donationreceipt.roles.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoleNode {
    private String name;
    private Integer permissions;
    private Integer displayNumber;

    private String type;
    @Builder.Default
    private List<RoleNode> children = new ArrayList<>();

    public RoleNode(String name) {
        this.name=name;
    }
}