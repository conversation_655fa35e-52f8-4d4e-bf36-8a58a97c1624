package com.chidhagni.donationreceipt.roles;


import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Organisation;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles;
import com.chidhagni.donationreceipt.organisation.OrganizationRepository;
import com.chidhagni.donationreceipt.roles.dto.request.*;
import com.chidhagni.donationreceipt.roles.dto.response.GetAllRolesResponse;
import com.chidhagni.donationreceipt.roles.dto.response.RolesPermissionsResponse;
import com.chidhagni.donationreceipt.roles.dto.response.RolesResponse;
import com.chidhagni.donationreceipt.roles.utils.RolesMapper;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

import static com.chidhagni.donationreceipt.db.jooq.tables.Roles.ROLES;


@Service
@RequiredArgsConstructor
@Slf4j
public class RolesService {
    private final RolesRepository rolesRepository;
    private final OrganizationRepository organizationRepository;
    private static Integer pageSize = 10;
    private static Integer pageNo = 1;

    @Value("${roles.tenant}")
    private UUID tenantRoleId;

    public UUID createRole(UserPrincipal userPrincipal, ParentRole rolesRequestDTO) {
        UUID parentRoleId = rolesRequestDTO.getParentRoleId();
        String name = rolesRequestDTO.getName();
        Roles roleById = rolesRepository.getRoleById(parentRoleId);
        optionalOrElseThrowForRoles(roleById);
        Organisation organisation = organizationRepository.getById(rolesRequestDTO.getOrgId());
        if (organisation == null) {
            throw new IllegalArgumentException("Organisation Not found ");
        }
        roleWithNameAndOrgIdExists(name,organisation.getId());
        List<Roles> rolesList = rolesRepository.getRoleByRoleName(name);
        if (!CollectionUtils.isEmpty(rolesList)) {
            throw new IllegalArgumentException("Exception Occurred while a role already exist with given name");
        }
        Roles role = RolesMapper.mapToEntity(userPrincipal, rolesRequestDTO);
        rolesRepository.createRole(role);
        return role.getId();
    }


    public GetAllRolesResponse getAllRoles(PaginationRequest paginationRequest, UserPrincipal userPrincipal) {
        applyDefaults(paginationRequest);
        Condition finalCondition = getSearchingCondition(paginationRequest);
        List<SortField<?>> sortFields = new ArrayList<>();

        getSortingCondition(sortFields, paginationRequest);

        List<RolesResponse> rolesResponseDTOList = rolesRepository.getAllRoles(finalCondition, sortFields, paginationRequest, userPrincipal);

        Integer count = rolesRepository.getRolesCount(finalCondition, userPrincipal);

        return GetAllRolesResponse.builder()
                .rowCount(count)
                .rolesResponseDTOS(rolesResponseDTOList)
                .paginationRequest(paginationRequest)
                .build();
    }


    public void inactivateRole(UUID roleId) {
        Roles role = rolesRepository.getRoleById(roleId);
        optionalOrElseThrowForRoles(role);
        log.info("Role is available with given id:: {}", roleId);
        role.setIsActive(false);
        rolesRepository.updateRole(role);
        log.info("Role with id:: {} has been inactivated", roleId);
    }


    public void activateRole(UUID roleId) {
        Roles role = rolesRepository.getRoleById(roleId);
        optionalOrElseThrowForRoles(role);
        log.info("Role available with given id:: {}", roleId);
        role.setIsActive(true);
        rolesRepository.updateRole(role);
        log.info("Role with id:: {} has been activated", roleId);
    }


    public void applyDefaults(PaginationRequest paginationRequest) {
        if (paginationRequest.getPage() == null || paginationRequest.getPage() < 1) {
            paginationRequest.setPage(pageNo);
        }

        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() < 1) {
            paginationRequest.setPageSize(pageSize);
        }
    }

    public void getSortingCondition(List<SortField<?>> sortFields, PaginationRequest paginationRequest) {
        sortFields.add(ROLES.CREATED_ON.desc());
        if (paginationRequest.isSortByNameDesc()) {
            sortFields.add(ROLES.NAME.desc());
        }
        if (paginationRequest.isSortByNameAsc()) {
            if (sortFields.contains(ROLES.NAME.desc())) {
                sortFields.remove(ROLES.NAME.desc());
            }
            sortFields.add(ROLES.NAME.asc());
        }
        if (paginationRequest.isSortByCreatedDateAsc()) {
            sortFields.remove(ROLES.CREATED_ON.desc());
            sortFields.add(ROLES.CREATED_ON.asc());
        }

    }


    public Condition getSearchingCondition(PaginationRequest paginationRequest) {
        String searchByName = paginationRequest.getNameFilter();
        String searchByDescription = paginationRequest.getDescriptionFilter();
        UUID searchByOrgId=paginationRequest.getOrgId();
        UUID parentRole = paginationRequest.getParentRoleFilter();
        log.info("given search by name, description, parent role name are :: {}, {}, {}", searchByName, searchByDescription, parentRole);

        List<Condition> conditions = new ArrayList<>();

        if (searchByName != null && !searchByName.isEmpty()) {
            conditions.add(ROLES.NAME.containsIgnoreCase(searchByName));
        }

        if (searchByDescription != null && !searchByDescription.isEmpty()) {
            conditions.add(ROLES.DESCRIPTION.containsIgnoreCase(searchByDescription));
        }

        if (parentRole != null) {
            conditions.add(ROLES.PARENT_ROLE_ID.eq(parentRole));
        }

        if (searchByOrgId != null) {
            conditions.add(ROLES.ORG_ID.eq(searchByOrgId));
        }
        return conditions.stream().reduce(DSL.noCondition(), Condition::and);
    }


    public RolesPermissionsResponse updateRole(UUID roleId, ParentRole rolesDTO, UserPrincipal userPrincipal) {
        Roles existingRole = rolesRepository.getRoleById(roleId);
        optionalOrElseThrowForRoles(existingRole);
        UUID orgId = existingRole.getOrgId();
        boolean isNameChanged = !existingRole.getName().equalsIgnoreCase(rolesDTO.getName());
        if (isNameChanged) {
            boolean nameExists = rolesRepository.existsByNameAndOrgIdExcludingId(
                    rolesDTO.getName(), orgId, roleId
            );
            if (nameExists) {
                rolesDTO.setName(existingRole.getName());
                throw new IllegalArgumentException("A role with this name already exists in the organization. Please choose a different role name.");
            }
        }
        UUID loggedInUserId = userPrincipal.getId();
        Set<UUID> mismatchIndividualIds = new HashSet<>();
        rolesRepository.updateIndividualPermissions(roleId, rolesDTO, loggedInUserId, mismatchIndividualIds);
        log.info("Updated Permissions for Individual who belongs to roleId ::{} successfully", roleId);
        Roles mappedRoleEntity = RolesMapper.map(existingRole, rolesDTO, loggedInUserId);
        rolesRepository.updateRole(mappedRoleEntity);
        log.info("Updated Roles Permissions for the role id :: {} Successfully", roleId);
        return RolesMapper.mapToPermissionDTO(mappedRoleEntity, mismatchIndividualIds);
    }

    public RolesPermissionsResponse getRoleById(UUID roleId) {
        Roles roleEntity = rolesRepository.getRoleById(roleId);
        optionalOrElseThrowForRoles(roleEntity);
        return RolesMapper.mapToDTO(roleEntity);
    }


//    public GetRolesListResponse getRolesByParentRoleId(UUID parentRoleId) {
//        List<GetRolesListResponse.RolesGetResponse> roleDTOs;
//
//        if (parentRoleId == null) {
//            log.info("Fetching top-level roles as parentRoleId is null.");
//            roleDTOs = getTopLevelRoles();
//        } else {
//            Roles roleById = rolesRepository.getRoleById(parentRoleId);
//            optionalOrElseThrowForRoles(roleById);
//            log.info("Fetching child roles for parentRoleId: {}", parentRoleId);
//            roleDTOs = getChildRoles(parentRoleId);
//        }
//
//        return GetRolesListResponse.builder()
//                .rolesGetResponse(roleDTOs)
//                .build();
//    }
//
//
//    /**
//     * Retrieves a list of top-level roles that do not have a parent role.
//     *
//     * <p>This method fetches roles where `parentRoleId` is null, meaning they are at the highest level
//     * in the hierarchy.</p>
//     *
//     * <p><b>Process:</b></p>
//     * <ul>
//     *   <li>Logs the execution of the query for tracking purposes.</li>
//     *   <li>Fetches all roles from the repository where `parentRoleId` is null.</li>
//     *   <li>Logs the count of retrieved roles.</li>
//     *   <li>Maps the role entities to `RolesGetResponse` DTOs.</li>
//     *   <li>Returns the mapped list of top-level roles.</li>
//     * </ul>
//     *
//     * @return A list of `RolesGetResponse` representing top-level roles.
//     */
//    private List<GetRolesListResponse.RolesGetResponse> getTopLevelRoles() {
//        log.info("Executing query to fetch top-level roles (parentRoleId is null).");
//        List<Roles> rolesList = rolesRepository.fetchRolesWithNullParentId();
//        log.info("Fetched {} top-level roles.", rolesList.size());
//        return RolesMapper.mapToRolesGetResponse(rolesList);
//    }
//
//
//    /**
//     * Retrieves a list of child roles based on the provided parent role ID.
//     *
//     * <p>This method fetches roles that have the specified `parentRoleId`, indicating they are
//     * subordinate to the given parent role.</p>
//     *
//     * <p><b>Process:</b></p>
//     * <ul>
//     *   <li>Fetches child roles from the repository using `parentRoleId`.</li>
//     *   <li>Logs the count of retrieved child roles.</li>
//     *   <li>Maps the role entities to `RolesGetResponse` DTOs.</li>
//     *   <li>Returns the mapped list of child roles.</li>
//     * </ul>
//     *
//     * @param parentRoleId The unique identifier of the parent role.
//     * @return A list of `RolesGetResponse` representing child roles.
//     */
//    private List<GetRolesListResponse.RolesGetResponse> getChildRoles(UUID parentRoleId) {
//        List<Roles> rolesList = rolesRepository.fetchRolesByParentRoleId(parentRoleId);
//        log.info("Fetched {} child-level roles.", rolesList.size());
//        return RolesMapper.mapToRolesGetResponse(rolesList);
//    }
//
    private void optionalOrElseThrowForRoles(Roles roles) {
        if (roles == null) {
            throw new IllegalArgumentException("Exception Occurred while fetching role by it's id, as role is not available");
        }
    }


    public List<GetOrgRoles> getRolesByOrgId(UUID orgId) {
        return rolesRepository.getRolesByOrgId(orgId);
    }

    public GetTenantRolePermissionsDTO getTenantRolePermissionsDTO()
    {
        return rolesRepository.getRolePermissionsById(tenantRoleId);
    }


    private void roleWithNameAndOrgIdExists(String name, UUID orgId) {
       boolean isExist=rolesRepository.roleWithNameAndOrgIdExists(name,orgId);
        if (isExist) {
            throw new DuplicateKeyException("A role with this name already exists.");
        }
    }


}
