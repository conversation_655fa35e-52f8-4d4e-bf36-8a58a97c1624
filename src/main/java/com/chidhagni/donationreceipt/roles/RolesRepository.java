package com.chidhagni.donationreceipt.roles;

//import com.chidhagni.houzer.roles.utils.RolesMapper;
import com.chidhagni.donationreceipt.common.exception.InternalServerError;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualPermissionDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualRoleDao;
import com.chidhagni.donationreceipt.db.jooq.tables.daos.RolesDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPermission;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualRole;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Roles;
import com.chidhagni.donationreceipt.roles.dto.request.*;
import com.chidhagni.donationreceipt.roles.dto.response.RolesResponse;
import com.chidhagni.donationreceipt.roles.utils.RolesMapper;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.SortField;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.chidhagni.donationreceipt.db.jooq.tables.Individual.INDIVIDUAL;
import static com.chidhagni.donationreceipt.db.jooq.tables.Organisation.ORGANISATION;
import static com.chidhagni.donationreceipt.db.jooq.tables.Roles.ROLES;


@Repository
@RequiredArgsConstructor
@Slf4j
public class RolesRepository {

    private final RolesDao rolesDao;
    private final IndividualRoleDao individualRoleDao;
    private final IndividualPermissionDao individualPermissionDao;

    private static final String PARENT_ROLE_ID = "parent_role_id";
    private static final String ROLE_ID ="parentRoleId";
    @Value("${roles.tenant.admin}")
    private UUID tenantAdminRoleId;
    public Roles getRoleById(UUID id) {
        Roles roles = null;
        try {
            roles = rolesDao.fetchOneById(id);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching role by it's id", ex);
        }
        return roles;
    }
//
//    /**
//     * Retrieves a list of roles matching the specified role name.
//     *
//     * @param name The name of the role to search for.
//     * @return A list of `Roles` matching the given name.
//     * @throws InternalServerError If an error occurs while fetching the role.
//     */
    public List<Roles> getRoleByRoleName(String name) {

        try {
            return rolesDao.fetchByName(name);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching role by name", ex);
        }
    }
//
//    /**
//     * Creates a new role.
//     *
//     * @param role The role entity to be created.
//     * @throws InternalServerError If an error occurs while creating the role.
//     */
    public void createRole(Roles role) {

        try {
            rolesDao.insert(role);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while creating role", ex);
        }
    }
//
//    /**
//     * Retrieves a paginated list of all roles based on the specified condition, sorting, and pagination settings.
//     *
//     * <p>This method executes a database query to fetch roles.The result is paginated and sorted based on the provided criteria.</p>
//     *
//     * <p><b>Process:</b></p>
//     * <ul>
//     *   <li>Calculates the offset for pagination based on the provided `page` and `pageSize`.</li>
//     *   <li>Constructs a query that selects role attributes, including:</li>
//     *   <ul>
//     *     <li>Role ID, name, description, parent role ID, status, created_on, and updated_on timestamps.</li>
//     *     <li>Email of the individual who created or last updated the role.</li>
//     *     <li>Parent role name for reference.</li>
//     *   </ul>
//     *   <li>Performs left joins to retrieve associated user and parent role information.</li>
//     *   <li>Applies the provided condition and sorting criteria.</li>
//     *   <li>Executes the query with pagination limits and offset.</li>
//     *   <li>Maps the results to `RolesResponse` DTOs.</li>
//     *   <li>Handles exceptions by throwing an `InternalServerError` if an error occurs during execution.</li>
//     * </ul>
//     *
//     * @param condition The filtering condition for retrieving roles.
//     * @param sortFields The list of fields used for sorting the results.
//     * @param paginationRequest The pagination settings, including page number and page size.
//     * @return A list of `RolesResponse` DTOs containing role details.
//     * @throws InternalServerError If an error occurs while fetching roles from the database.
//     */
public List<RolesResponse> getAllRoles(Condition condition, List<SortField<?>> sortFields, PaginationRequest paginationRequest,
                                       UserPrincipal userPrincipal) {
    Integer pageNo = (paginationRequest.getPage() - 1) * paginationRequest.getPageSize();
    try {
        UUID loggedUser = userPrincipal.getId();
        List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(loggedUser);

        Condition finalCondition = condition != null ? condition : DSL.noCondition();

        boolean isTenantAdmin = individualRoles.stream()
                .anyMatch(role -> role.getRoleId().equals(tenantAdminRoleId));

        if (isTenantAdmin && !individualRoles.isEmpty()) {
            UUID orgId = individualRoles.get(0).getOrgId();
            if (orgId != null) {
                finalCondition = finalCondition.and(ROLES.ORG_ID.eq(orgId));
            }
        }

        return rolesDao.ctx().select(
                        ROLES.ID.as("id"),
                        ROLES.NAME.as("name"),
                        ROLES.DESCRIPTION.as("description"),
                        ROLES.PARENT_ROLE_ID.as("parentRoleId"),
                        ROLES.IS_ACTIVE.as("isActive"),
                        ROLES.CREATED_ON.as("createdOn"),
                        ROLES.UPDATED_ON.as("updatedOn"),
                        INDIVIDUAL.as("r1").EMAIL.as("createdBy"),
                        INDIVIDUAL.as("r2").EMAIL.as("createdBy"),
                        ROLES.as("r0").NAME.as("parentRoleName"),
                        ORGANISATION.ID.as("organisationId"),
                        ORGANISATION.NAME.as("organisationName")
                )
                .from(ROLES)
                .leftJoin(ROLES.as("r0")).on(ROLES.PARENT_ROLE_ID.eq(ROLES.as("r0").ID))
                .leftJoin(INDIVIDUAL.as("r1")).on(ROLES.CREATED_BY.eq(INDIVIDUAL.as("r1").ID))
                .leftJoin(INDIVIDUAL.as("r2")).on(ROLES.UPDATED_BY.eq(INDIVIDUAL.as("r2").ID))
                .leftJoin(ORGANISATION).on(ROLES.ORG_ID.eq(ORGANISATION.ID))
                .where(finalCondition)
                .orderBy(sortFields)
                .limit(paginationRequest.getPageSize())
                .offset(pageNo)
                .fetchInto(RolesResponse.class);
    } catch (Exception ex) {
        throw new InternalServerError("Exception Occurred while fetching all roles", ex);
    }
}


    public Integer getRolesCount(Condition condition,UserPrincipal userPrincipal) {
        try {
            UUID loggedUser = userPrincipal.getId();
            List<IndividualRole> individualRoles = individualRoleDao.fetchByIndividualId(loggedUser);

            Condition finalCondition = condition != null ? condition : DSL.noCondition();

            boolean isTenantAdmin = individualRoles.stream()
                    .anyMatch(role -> role.getRoleId().equals(tenantAdminRoleId));

            if (isTenantAdmin) {
                UUID orgId = individualRoles.get(0).getOrgId();
                finalCondition = finalCondition.and(ROLES.ORG_ID.eq(orgId));
            }
            return rolesDao.ctx().selectCount().from(ROLES).where(finalCondition).fetchOne(0, Integer.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching all roles count", ex);
        }
    }

    public void updateRole(Roles role) {
        try {
            rolesDao.update(role);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while updating role", ex);
        }
    }

    List<Roles> fetchRolesWithNullParentId() {

        try {
            return rolesDao.ctx().selectFrom(ROLES)
                    .where(ROLES.PARENT_ROLE_ID.isNull())
                    .fetchInto(Roles.class);
        } catch (Exception ex) {
            throw new InternalServerError("Exception Occurred while fetching Roles with Null ParentRoleId ", ex);

        }
    }
//
//    /**
//     * Retrieves all child roles for a given parent role ID using a recursive query.
//     *
//     * <p>This method performs a query to fetch all roles
//     * that are hierarchically below the specified parent role.</p>
//     *
//     * <p><b>Process:</b></p>
//     * <ul>
//     *   <li>Defines a recursive query to retrieve roles in a hierarchical structure.</li>
//     *   <li>The base case selects the starting node (the parent role itself).</li>
//     *   <li>The recursive step joins child roles with their respective parent roles.</li>
//     *   <li>Executes the recursive query to retrieve all descendant roles.</li>
//     *   <li>Maps the query result into a list of `Roles` entities.</li>
//     *   <li>Handles errors by throwing an `InternalServerError` if an exception occurs during execution.</li>
//     * </ul>
//     *
//     * @param parentRoleId The unique identifier of the parent role whose child roles are to be retrieved.
//     * @return A list of `Roles` representing all descendant roles of the given parent role.
//     * @throws InternalServerError If an error occurs while fetching descendant roles.
//     */
//    public List<Roles> fetchRolesByParentRoleId(UUID parentRoleId) {
//        try {
//            String hierarchy = "RoleHierarchy";
//
//            return rolesDao.ctx().withRecursive(hierarchy, "id", ROLE_ID, "name","description")
//                    .as(rolesDao.ctx()
//                            // Include the starting node in the initial part of the CTE
//                            .select(ROLES.ID, ROLES.PARENT_ROLE_ID, ROLES.NAME,ROLES.DESCRIPTION)
//                            .from(ROLES)
//                            .where(ROLES.ID.eq(parentRoleId))  // Fetch the starting node
//                            .unionAll(
//                                    rolesDao.ctx().select(ROLES.ID, ROLES.PARENT_ROLE_ID, ROLES.NAME,ROLES.DESCRIPTION)
//                                            .from(ROLES)
//                                            .join(table(name(hierarchy)))
//                                            .on(ROLES.PARENT_ROLE_ID.eq(field(name(hierarchy, "id"), UUID.class)))
//                            )
//                    )
//                    .select(field(name("id"), UUID.class),
//                            field(name(ROLE_ID), UUID.class),
//                            field(name("name"), String.class),
//                            field(name("description"), String.class))
//                    .from(table(name(hierarchy)))
//                    .fetchInto(Roles.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception occurred while fetching all descendant roles", ex);
//        }
//    }
//
//
//    /**
//     * Updates individual permissions associated with the updated role.
//     *
//     * <p>This method updates the permissions of all individuals linked to a specified role based
//     * on the updated role details provided in the request.</p>
//     *
//     * <p><b>Process:</b></p>
//     * <ul>
//     *   <li>Fetches all individual-role associations for the given role.</li>
//     *   <li>For each individual associated with the role:</li>
//     *   <ul>
//     *     <li>Retrieves the individual ID and their existing permissions.</li>
//     *     <li>Checks if the individual has existing permissions; throws an exception if none are found.</li>
//     *     <li>Maps and updates the individual permissions with the new role permissions.</li>
//     *     <li>Updates the individual permissions in the database.</li>
//     *     <li>Tracks any mismatches in permissions (if applicable).</li>
//     *     <li>Logs the successful update of individual permissions.</li>
//     *   </ul>
//     * </ul>
//     *
//     * @param roleId The unique identifier of the role whose individual permissions are being updated.
//     * @param rolesDTO The request object containing the updated role details and permissions.
//     * @param loggedInUserId The ID of the authenticated user performing the update.
//     * @param mismatchIndividualIds A set used to track mismatches between existing and updated permissions.
//     * @throws IllegalArgumentException If no permissions are found for an individual.
//     */
    public void updateIndividualPermissions(UUID roleId, ParentRole rolesDTO, UUID loggedInUserId, Set<UUID> mismatchIndividualIds) {

        List<IndividualRole> individualRoleList = individualRoleDao.fetchByRoleId(roleId);
        for (IndividualRole individualRole : individualRoleList) {
            UUID individualId = individualRole.getIndividualId();
            List<Roles> existingRolesPermissions = rolesDao.fetchById(roleId);
            List<IndividualPermission> individualPermissions = individualPermissionDao.fetchByIndividualId(individualId);
            if (CollectionUtils.isEmpty(individualPermissions)) {
                throw new IllegalArgumentException("No permissions were found for individual => " + individualId);
            }
            IndividualPermission individualPermission = individualPermissions.get(0);
            Roles roles = existingRolesPermissions.get(0);
            RolesMapper.updateIndividualPermissionWithNewPermissions(roles,
                    individualPermission, rolesDTO.getPermissions(), loggedInUserId, mismatchIndividualIds, individualId);
            individualPermissionDao.update(individualPermission);
            log.info("Permissions in IndividualPermissions for the role id :: {} is updated successfully ", roleId);
        }

    }

    public List<GetOrgRoles> getRolesByOrgId(UUID orgId) {
        return rolesDao.ctx().select(ROLES.ID.as("id"),ROLES.NAME.as("roleName"),ROLES.DESCRIPTION.as("roleDescription"))
                .from(ROLES)
                .where(ROLES.ORG_ID.eq(orgId).and(ROLES.IS_ACTIVE.eq(Boolean.TRUE)))
                .fetchInto(GetOrgRoles.class);

    }

    public GetTenantRolePermissionsDTO getRolePermissionsById(UUID tenantRoleId) {

        return rolesDao.ctx().select(ROLES.ID.as("tenantRoleId"),ROLES.PERMISSIONS.as("permissions"))
                .from(ROLES)
                .where(ROLES.ID.eq(tenantRoleId))
                .fetchOneInto(GetTenantRolePermissionsDTO.class);
    }

    public boolean roleWithNameAndOrgIdExists(String name, UUID orgId) {
        return rolesDao.ctx().selectCount()
                .from(ROLES)
                .where(ROLES.NAME.eq(name).and(ROLES.ORG_ID.eq(orgId)))
                .fetchOneInto(Integer.class) > 0;
    }


    public boolean existsByNameAndOrgIdExcludingId(String name, UUID orgId, UUID excludedRoleId) {
        return rolesDao.ctx().selectCount()
                .from(ROLES)
                .where(ROLES.NAME.eq(name)
                        .and(ROLES.ORG_ID.eq(orgId))
                        .and(ROLES.ID.ne(excludedRoleId)))
                .fetchOneInto(Integer.class) > 0;
    }

//
//    /**
//     * Retrieves the highest-level parent role ID for a given role in the hierarchy.
//     *
//     * <p>This method uses a recursive Common Table Expression (CTE) to traverse the role hierarchy
//     * and identify the root (top-most) parent role for the specified `roleId`.</p>
//     *
//     * <p><b>Process:</b></p>
//     * <ul>
//     *   <li>Defines a recursive CTE (`RoleHierarchy`) to traverse the role hierarchy.</li>
//     *   <li>The base case selects the given role and its parent role ID.</li>
//     *   <li>The recursive step continues joining parent roles until reaching the top-most parent.</li>
//     *   <li>The query selects the role ID where `PARENT_ROLE_ID` is `NULL`, indicating the root role.</li>
//     *   <li>Returns the UUID of the highest-level parent role.</li>
//     *   <li>Handles errors by throwing an `InternalServerError` in case of query execution failure.</li>
//     * </ul>
//     *
//     * @param roleId The unique identifier of the role for which the top-most parent is to be determined.
//     * @return The `UUID` of the highest-level parent role in the hierarchy.
//     * @throws InternalServerError If an error occurs while fetching the parent role hierarchy.
//     */
//    public UUID getParentRoleID(UUID roleId) {
//        String hierarchy = "RoleHierarchy";
//        try {
//            return rolesDao.ctx().withRecursive(hierarchy, "id", PARENT_ROLE_ID)
//                    .as(rolesDao.ctx().select(ROLES.ID, ROLES.PARENT_ROLE_ID)
//                            .from(ROLES)
//                            .where(ROLES.ID.eq(roleId))
//                            .unionAll(
//                                    rolesDao.ctx().select(ROLES.ID, ROLES.PARENT_ROLE_ID)
//                                            .from(ROLES)
//                                            .join(table(name(hierarchy)))
//                                            .on(ROLES.ID.eq(field(name(hierarchy, PARENT_ROLE_ID), UUID.class)))
//                            ))
//                    .select(field(name("id"), UUID.class))
//                    .from(table(name(hierarchy)))
//                    .where(field(name(PARENT_ROLE_ID)).isNull())
//                    .fetchOneInto(UUID.class);
//        }catch (Exception ex){
//            throw new InternalServerError("Exception occurred while fetching Roles Tree By ParentRoleId", ex);
//        }
//    }
//
//    public Boolean isRoleExist(UUID roleId){
//        try{
//            return rolesDao.ctx().fetchExists(rolesDao.ctx().selectOne().from(ROLES).where(ROLES.ID.eq(roleId)));
//        }catch (Exception ex){
//            throw new InternalServerError(
//                    String.format("Exception while checking role exist by given role id :: %s", roleId));
//        }
//    }
//    public String getRoleNameByRoleId(UUID roleId){
//        try{
//            return rolesDao.ctx().select(ROLES.NAME).from(ROLES).where(ROLES.ID.eq(roleId)).fetchOneInto(String.class);
//        }catch (Exception ex){
//            throw new InternalServerError("Exception occurred while fetching Role Name By RoleId", ex);
//        }
//    }
}

