package com.chidhagni.donationreceipt.donorsimport.dto.request;

import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportStagingPatchRequest {
    private String name;
    private String mobileNumber;
    private String email;
    private String panNo;
    private DonorMetaData donorMetaData;
}