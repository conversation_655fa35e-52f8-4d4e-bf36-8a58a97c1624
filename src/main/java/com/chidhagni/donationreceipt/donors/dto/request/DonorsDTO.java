package com.chidhagni.donationreceipt.donors.dto.request;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonorsDTO {

    private String name;
    @Pattern(regexp = "^[0-9]{10}$", message = "Contact number must be exactly 10 digits")
    private String contactNumber;
    @Email(regexp = "[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,6}", flags = Pattern.Flag.CASE_INSENSITIVE)
    private String email;
    private UUID tenantOrgId;
    @Pattern(regexp = "^[A-Z]{5}[0-9]{4}[A-Z]$|^$", message = "PAN number must have 10 characters: " +
            "first 5 uppercase letters, followed by 4 digits, and ending with 1 uppercase letter. " +
            "Example: **********")
    private String panNo;
    private DonorMetaData donorMetaData;
    private String createdOn;
    private String updatedOn;
    private String createdBy;
    private String updatedBy;
    private Boolean isActive;
}
