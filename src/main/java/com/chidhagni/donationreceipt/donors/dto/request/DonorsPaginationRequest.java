package com.chidhagni.donationreceipt.donors.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@AllArgsConstructor
public class DonorsPaginationRequest {
    @Builder.Default
    private Integer page = 1;

    @Builder.Default
    private Integer pageSize = 10;

    @Builder.Default
    private String nameFilter = "";

    @Builder.Default
    private String emailFilter = "";

    @Builder.Default
    private String mobileFilter = "";


    @Builder.Default
    private String panNoFilter = "";

    @Builder.Default
    private UUID orgId = null;

    @Builder.Default
    private String stateFilter = "";
    @Builder.Default
    private String addressFilter = "";
    @Builder.Default
    private String pinCodeFilter = "";
    @Builder.Default
    private List<UUID> tagsFilter = new ArrayList<>();

    private boolean sortByNameAsc;

    private boolean sortByNameDesc;

    private boolean sortByEmailAsc;

    private boolean sortByEmailDesc;

    private boolean sortByMobileAsc;

    private boolean sortByMobileDesc;

    private boolean sortByCreatedDateAsc;

}