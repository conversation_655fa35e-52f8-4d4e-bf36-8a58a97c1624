package com.chidhagni.donationreceipt.donors;


import com.chidhagni.donationreceipt.donationreceipts.dto.response.DonorDropdownResponse;
import com.chidhagni.donationreceipt.donors.dto.request.DonorsDTO;
import com.chidhagni.donationreceipt.donors.dto.request.DonorsPaginationRequest;
import com.chidhagni.donationreceipt.donors.dto.response.DonorsResponse;
import com.chidhagni.donationreceipt.donors.dto.response.GetAllDonors;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/donors")
@RequiredArgsConstructor
public class DonorsController {


    private final DonorsService donorsService;

    @PostMapping
    public ResponseEntity<UUID> createDonor(@Valid @RequestBody DonorsDTO donorsDTO,
                                            @CurrentUser UserPrincipal userPrincipal) {
        UUID donorId = donorsService.create(donorsDTO, userPrincipal);
        return new ResponseEntity<>(donorId, HttpStatus.CREATED);
    }


    @GetMapping("/{id}")
    public ResponseEntity<DonorsResponse> getDonor(@PathVariable UUID id) {
        DonorsResponse donor = donorsService.getById(id);
        if (donor == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        return new ResponseEntity<>(donor, HttpStatus.OK);
    }


    @PatchMapping("/update/{id}")
    public ResponseEntity<DonorsResponse> updateDonor(
            @PathVariable UUID id,
            @Valid @RequestBody DonorsDTO donorsDTO,
            @CurrentUser UserPrincipal userPrincipal) {
        DonorsResponse updateDonor=donorsService.update(id, donorsDTO, userPrincipal);
        return new ResponseEntity<>(updateDonor, HttpStatus.OK);
    }


    @PostMapping(value = "/all/donor")
    public ResponseEntity<GetAllDonors> getAllIndividualsDonors(
            @RequestBody(required = false) DonorsPaginationRequest donorsPaginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (donorsPaginationRequest == null) {
            donorsPaginationRequest = DonorsPaginationRequest.builder().build();
        }
        return new ResponseEntity<>(donorsService.getAllDonor(donorsPaginationRequest,userPrincipal), HttpStatus.OK);
    }



    @PatchMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> activateDonor(
            @PathVariable UUID id,
            @CurrentUser UserPrincipal userPrincipal) {
        donorsService.activateDonors(id, userPrincipal.getId());
        return ResponseEntity.ok().build();
    }


    @DeleteMapping("/{id}")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Void> deactivateDonors(
            @PathVariable UUID id,
            @CurrentUser UserPrincipal userPrincipal) {
        donorsService.deactivateDonors(id, userPrincipal.getId());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/donor-dropdown")
    public List<DonorDropdownResponse> getDonorDropDownResponse()
    {
        return  donorsService.getDonorDropdown();
    }

    @GetMapping("/donor-dropdown-by-tenant-id/{tenantOrgId}")
    public List<DonorDropdownResponse> getDonorDropDownResponse(@PathVariable UUID tenantOrgId)
    {
        return  donorsService.getTenantDonorDropdown(tenantOrgId);
    }
}
