//package com.chidhagni.donationreceipt.databaseInitializer;
//
//
//import com.chidhagni.donationreceipt.db.jooq.tables.daos.IndividualDao;
//import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
//import com.chidhagni.donationreceipt.security.UserPrincipal;
//import com.chidhagni.utils.CommonOperations;
//import com.chidhagni.utils.DateUtils;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.jooq.JSONB;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.ApplicationListener;
//import org.springframework.context.annotation.Profile;
//import org.springframework.context.event.ContextRefreshedEvent;
//import org.springframework.core.io.ClassPathResource;
//import org.springframework.security.crypto.password.PasswordEncoder;
//import org.springframework.stereotype.Component;
//import org.springframework.util.CollectionUtils;
//
//import java.io.IOException;
//import java.io.InputStream;
//import java.nio.charset.StandardCharsets;
//import java.time.LocalDateTime;
//import java.util.Collections;
//import java.util.List;
//import java.util.UUID;
//
//
//
//@Component
//@Slf4j
//@Profile("local")
//public class DatabaseInitializer implements ApplicationListener<ContextRefreshedEvent> {
//
//    private final PasswordEncoder passwordEncoder;
//    private final ObjectMapper objectMapper;
//    private final CommonOperations commonOperations;
//    private final IndividualDao individualDao;
//
//    //SUPER ADMIN PROFILE
//    @Value("${admin.name}")
//    private String adminName;
//
//    @Value("${admin.email}")
//    private String adminEmail;
//
//    @Value("${admin.phone}")
//    private String adminPhone;
//
//    @Value("${adminPassword}")
//    private String adminPassword;
//
//
//    @Value("${admin.role.type.id}")
//    private UUID adminRoleTypeId;
//
//    @Value("${admin.role.name}")
//    private String adminRoleName;
//
//    @Value("${houzer.first.employee.email}")
//    private String employeeOneEmail;
//    @Value("${houzer.second.employee.email}")
//    private String employeeSecondEmail;
//    @Value("${houzer.first.employee.name}")
//    private String EmployeeOneName;
//    @Value("${houzer.second.employee.name}")
//    private String employeeSecondName;
//    @Value("${employee.roles.id}")
//    private UUID employeeRoleId;
//
//    private final String superAdminAccessPagesJson = "src/main/resources/admin-access-pages.json";
//
//
//    public DatabaseInitializer(PasswordEncoder passwordEncoder, ObjectMapper objectMapper,CommonOperations commonOperations,IndividualDao individualDao) {
//        this.passwordEncoder = passwordEncoder;
//        this.objectMapper = objectMapper;
//        this.commonOperations = commonOperations;
//        this.individualDao=individualDao;
//    }
//
//    @Override
//    public void onApplicationEvent(ContextRefreshedEvent event) {
//        try {
//            initializeSuperAdminData();
//            log.info("Initialized Super Admin Data");
//        } catch (IOException | RuntimeException e) {
//            log.error("Error while Initializing SuperAdminData {}", e.getMessage());
//            throw new RuntimeException(e);
//        }
//    }
//
//    private void initializeSuperAdminData() throws IOException {
//
//        List<Individual> existingUsers = individualDao.fetchByEmail(adminEmail);
//        if (!existingUsers.isEmpty()) {
//            if (existingUsers.size() > 1) {
//                throw new RuntimeException("More the one admin account found");
//            }
//            Individual existingUser = existingUsers.get(0);
////            if (!existingUser.getUserCategoryId().equals(UUID.fromString(DefaultUserCategoryEnums.SUPER_ADMIN.getValue()))
////                    || !existingUser.getFirstName().equals(adminName)) {
////                throw new RuntimeException("Admin Account data is not correct");
////            }
//            if (existingUser.getRoleId() == null) {
//                RoleDTO roleDTO = roleService.create(generateRoleData(), generateUserPrincipal());
//                existingUser.setRoleId(roleDTO.getId());
//            } else {
//                roleService.update(existingUser.getRoleId(), generateRoleData(), generateUserPrincipal());
//            }
//            existingUser.setUpdatedOn(DateUtils.currentTimeIST());
//            usersDao.update(existingUser);
//            //Admin account already there.
//
//            updateTestEmployeePermissions();
//            return;
//        }
//
//        // Insert data into STAGING_EMAIL_VERIFICATION table
//        StagingEmailVerification stagingUser = new StagingEmailVerification();
//        stagingUser.setId(UUID.randomUUID());
//        stagingUser.setEmail(adminEmail);
//        stagingUser.setName(adminName);
//        stagingUser.setOtpCode("");
//        stagingUser.setIsVerified(true);
//        stagingUser.setContactNumber(adminPhone);
//        stagingUser.setMetadata(JSONB.valueOf("{}"));
//        stagingUser.setCreatedOn(DateUtils.currentTimeIST());
//        stagingUser.setUpdatedOn(DateUtils.currentTimeIST());
//        stagingEmailVerificationDao.insert(stagingUser);
//
//        // Insert data into USERS table
//        String password = passwordEncoder.encode(adminPassword);
//        Users user = new Users();
//        user.setId(UUID.randomUUID());
//        user.setFirstName(adminName);
//        user.setEmail(adminEmail);
//        user.setPassword(password);
//        user.setUserCategoryId(UUID.fromString(DefaultUserCategoryEnums.SUPER_ADMIN.getValue()));
//        user.setStatus(UserStatusEnum.ACTIVE.getValue());
//        user.setCreatedOn(DateUtils.currentTimeIST());
//        user.setUpdatedOn(DateUtils.currentTimeIST());
//        usersDao.insert(user);
//
//        commonOperations.mapAndInsertOrUpdateIndividualCoreAndDerivedEntities(user);
//
//        RoleDTO roleDTO = roleService.create(generateRoleData(), generateUserPrincipal());
//        user.setRoleId(roleDTO.getId());
//        usersDao.update(user);
//        insertEmployee(EmployeeOneName, employeeOneEmail);
//        insertEmployee(employeeSecondName, employeeSecondEmail);
//    }
//
//    private UserPrincipal generateUserPrincipal() {
//        // Create a dummy UserPrincipal for this initialization
//        return new UserPrincipal(
//                UUID.randomUUID(),
//                adminEmail,
//                adminPassword,
//                LocalDateTime.now(),
//                LocalDateTime.now(),
//                Collections.emptyList()
//        );
//    }
//
//    private RoleDTO generateRoleData() throws IOException {
//        // Use ClassPathResource to load the resource from the classpath
//        ClassPathResource resource = new ClassPathResource("admin-access-pages.json");
//
//        try (InputStream inputStream = resource.getInputStream()) {
//            // Read role metadata from JSON file
//            String siteMapDataJson = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
//            UserManagementDTO userManagementDTO = objectMapper.readValue(siteMapDataJson, UserManagementDTO.class);
//
//            // Ensure pages is not null
//            if (userManagementDTO.getPages() == null) {
//                throw new IllegalArgumentException("Pages not defined correctly in JSON");
//            }
//
//            // Create RoleMetadata
//            RoleDTO.RoleMetadata roleMetadata = RoleDTO.RoleMetadata.builder()
//                    .siteMapData(userManagementDTO)
//                    .build();
//
//            // Create / Update RoleDTO
//            return RoleDTO.builder()
//                    .id(UUID.randomUUID())
//                    .name(adminRoleName)
//                    .roleTypeId(adminRoleTypeId)
//                    .isActive(true)
//                    .roleMetaData(roleMetadata)
//                    .createdOn(DateUtils.currentTimeIST())
//                    .updatedOn(DateUtils.currentTimeIST())
//                    .createdBy(UUID.randomUUID())
//                    .updatedBy(UUID.randomUUID())
//                    .build();
//        }
//    }
//
//    private void insertEmployee(String name, String email) {
//        String employeeRole = DefaultUserCategoryEnums.EMPLOYEE.name();
//        StagingEmailVerification stagingUser = new StagingEmailVerification();
//        stagingUser.setId(UUID.randomUUID());
//        stagingUser.setEmail(email);
//        stagingUser.setName(name);
//        stagingUser.setOtpCode("");
//        stagingUser.setIsVerified(true);
//        stagingUser.setMetadata(JSONB.valueOf("{}"));
//        stagingUser.setCreatedOn(DateUtils.currentTimeIST());
//        stagingUser.setUpdatedOn(DateUtils.currentTimeIST());
//        stagingEmailVerificationDao.insert(stagingUser);
//
//        String password = passwordEncoder.encode(adminPassword);
//        Users user = new Users();
//        user.setId(UUID.randomUUID());
//        user.setFirstName(name);
//        user.setEmail(email);
//        user.setPassword(password);
//        user.setSystemCode(commonOperations.generateSystemCode(SystemCodes.EMP));
//        user.setUserCategoryId(UUID.fromString(DefaultUserCategoryEnums.EMPLOYEE.getValue()));
//        user.setStatus(UserStatusEnum.ACTIVE.getValue());
//        user.setCreatedOn(DateUtils.currentTimeIST());
//        user.setUpdatedOn(DateUtils.currentTimeIST());
//        usersDao.insert(user);
//
//        commonOperations.mapAndInsertIntoIndividualCoreAndDerivedEntities(user, employeeRole);
//    }
//
//    public void updateTestEmployeePermissions() {
//        List<UUID> testEmployeeIds = fetchTestEmployeeIds();
//        if (CollectionUtils.isEmpty(testEmployeeIds)) {
//            log.info("No test employees id");
//            return;
//        }
//
//        List<UUID> individualPermissionsId = fetchIndividualPermissionsIds(testEmployeeIds);
//        if (CollectionUtils.isEmpty(individualPermissionsId)) {
//            log.info("No test employees present in individual permissions to update");
//            return;
//        }
//
//        List<RoleNode> employeePermissions = fetchEmployeePermissions();
//        if (employeePermissions == null || employeePermissions.isEmpty()) {
//            log.info("No permissions found for employee role");
//            return;
//        }
//
//        updateIndividualPermissions(individualPermissionsId, employeePermissions);
//    }
//
//    private List<UUID> fetchTestEmployeeIds() {
//        try {
//            return usersDao.ctx().select(USERS.ID).from(USERS)
//                    .where(USERS.EMAIL.in(employeeOneEmail, employeeSecondEmail))
//                    .fetchInto(UUID.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while fetching test employee ids", ex);
//        }
//    }
//
//    private List<UUID> fetchIndividualPermissionsIds(List<UUID> testEmployeeIds) {
//        try {
//            return usersDao.ctx().select(INDIVIDUAL_PERMISSION.ID).from(INDIVIDUAL_PERMISSION)
//                    .where(INDIVIDUAL_PERMISSION.INDIVIDUAL_ID.in(testEmployeeIds))
//                    .fetchInto(UUID.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while fetching individual permissions ids", ex);
//        }
//    }
//
//    private List<RoleNode> fetchEmployeePermissions() {
//        try {
//            Roles roles = usersDao.ctx().select().from(ROLES)
//                    .where(ROLES.ID.eq(employeeRoleId))
//                    .fetchOneInto(Roles.class);
//            return roles.getPermissions();
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while fetching test employee role by role id", ex);
//        }
//    }
//
//    private void updateIndividualPermissions(List<UUID> individualPermissionsId, List<RoleNode> employeePermissions) {
//        try {
//            int count = usersDao.ctx().update(INDIVIDUAL_PERMISSION)
//                    .set(INDIVIDUAL_PERMISSION.PERMISSIONS, employeePermissions)
//                    .where(INDIVIDUAL_PERMISSION.ID.in(individualPermissionsId))
//                    .execute();
//            log.info("Updated test employees count: {}", count);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while updating test employee role permissions", ex);
//        }
//    }
//
//
//}
//
//
//
//
