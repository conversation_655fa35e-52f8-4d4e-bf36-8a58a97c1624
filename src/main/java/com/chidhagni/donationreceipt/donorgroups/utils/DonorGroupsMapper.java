package com.chidhagni.donationreceipt.donorgroups.utils;

import com.chidhagni.donationreceipt.db.jooq.tables.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsDto;
import com.chidhagni.donationreceipt.donorgroups.dto.response.DonorResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetDonorGroupsResponse;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;


@Component
public class DonorGroupsMapper {
    public DonorGroups mapToDonorGroups(DonorGroupsDto donorGroupsDto, UserPrincipal userPrincipal) {
        DonorGroups donorGroups = new DonorGroups();
        donorGroups.setId(UUID.randomUUID());
        donorGroups.setName(donorGroupsDto.getName());
        donorGroups.setDescription(donorGroupsDto.getDescription());
        donorGroups.setFilters(donorGroupsDto.getFilters());
        donorGroups.setOrgId(donorGroupsDto.getOrgId());
        donorGroups.setIsActive(Boolean.TRUE);
        donorGroups.setCreatedBy(userPrincipal.getId());
        donorGroups.setUpdatedBy(userPrincipal.getId());
        donorGroups.setCreatedOn(DateUtils.currentTimeIST());
        donorGroups.setUpdatedOn(DateUtils.currentTimeIST());
        return donorGroups;
    }

    public GetDonorGroupsResponse mapToDonorGroupsResponse(DonorGroups donorGroups, List<DonorResponse> donorResponseList) {
        return GetDonorGroupsResponse.builder()
                .id(donorGroups.getId())
                .name(donorGroups.getName())
                .description(donorGroups.getDescription())
                .donorResponseList(donorResponseList)
                .filters(donorGroups.getFilters())
                .createdBy(donorGroups.getCreatedBy())
                .updatedBy(donorGroups.getUpdatedBy())
                .createdOn(donorGroups.getCreatedOn())
                .updatedOn(donorGroups.getUpdatedOn())
                .isActive(donorGroups.getIsActive())
                .orgId(donorGroups.getOrgId())
                .build();
    }

    public DonorResponse mapToDonorResponse(Donors donors) {
        return DonorResponse.builder()
                .id(donors.getId())
                .name(donors.getName())
                .email(donors.getEmail())
                .contactNumber(donors.getMobileNumber())
                .address(donors.getMetaData() != null ? donors.getMetaData().getAddress() : null)
                .panNo(donors.getPanNo() != null ? donors.getPanNo() : null)
                .state(donors.getMetaData() != null ? donors.getMetaData().getState() : null)
                .pinCode(donors.getMetaData() != null ? donors.getMetaData().getPinCode() : null)
                .orgId(donors.getTenantOrgId())
                .build();
    }
}
