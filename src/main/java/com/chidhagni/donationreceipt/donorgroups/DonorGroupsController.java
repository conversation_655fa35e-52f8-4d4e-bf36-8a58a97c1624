package com.chidhagni.donationreceipt.donorgroups;


import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsPaginationRequest;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetAllDonorGroupsResponses;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetDonorGroupsResponse;
import com.chidhagni.donationreceipt.donors.DonorsService;
import com.chidhagni.donationreceipt.security.CurrentUser;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api/v1/donor-groups")
@RequiredArgsConstructor
public class DonorGroupsController {

    private final DonorGroupsService donorGroupsService;


    @PostMapping("/create-donor-groups")
    public ResponseEntity<UUID> createDonorGroups(@RequestBody DonorGroupsDto donorGroupsDto,
                                                  @CurrentUser UserPrincipal userPrincipal) {
        UUID donorGroupId = donorGroupsService.createDonorGroups(donorGroupsDto, userPrincipal);
        return new ResponseEntity<>(donorGroupId, HttpStatus.CREATED);
    }


    @GetMapping("/get-by-id/{id}")
    public ResponseEntity<GetDonorGroupsResponse> getDonorGroupsById(@PathVariable UUID id) {
        GetDonorGroupsResponse donorGroups = donorGroupsService.getDonorGroupsById(id);
        if (donorGroups == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        return new ResponseEntity<>(donorGroups, HttpStatus.OK);
    }


    @PatchMapping("/update/{id}")
    public ResponseEntity<GetDonorGroupsResponse> updateDonorGroups(@PathVariable UUID id,
                                                                   @RequestBody DonorGroupsDto donorGroupsDto,
                                                                   @CurrentUser UserPrincipal userPrincipal) {
        GetDonorGroupsResponse updatedDonorGroups = donorGroupsService.updateDonorGroups(id, donorGroupsDto, userPrincipal);
        return new ResponseEntity<>(updatedDonorGroups, HttpStatus.OK);
    }


    @PatchMapping("/activate/{id}")
    public ResponseEntity<Void> activateDonorGroups(@PathVariable UUID id,
                                                    @CurrentUser UserPrincipal userPrincipal) {
        donorGroupsService.activateDonorGroups(id, userPrincipal.getId());
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PatchMapping("/deactivate/{id}")
    public ResponseEntity<Void> deactivateDonorGroups(@PathVariable UUID id,
                                                      @CurrentUser UserPrincipal userPrincipal) {
        donorGroupsService.deactivateDonorGroups(id, userPrincipal.getId());
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @PostMapping("/all")
    public ResponseEntity<GetAllDonorGroupsResponses> getAllDonorGroups(
            @RequestBody(required = false) DonorGroupsPaginationRequest paginationRequest,
            @CurrentUser UserPrincipal userPrincipal) {
        if (paginationRequest == null) {
            paginationRequest = DonorGroupsPaginationRequest.builder().build();
        }
        GetAllDonorGroupsResponses getAllDonorGroupsResponses =
                donorGroupsService.getAllDonorGroupsResponses(paginationRequest,userPrincipal);
        return ResponseEntity.ok(getAllDonorGroupsResponses);
    }
}
