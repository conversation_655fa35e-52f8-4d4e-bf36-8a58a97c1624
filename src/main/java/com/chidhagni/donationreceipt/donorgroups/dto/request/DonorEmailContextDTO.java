package com.chidhagni.donationreceipt.donorgroups.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonorEmailContextDTO {

    private String donorName;
    private String donorEmail;
    private String organizationName;
    private int year;

    // Campaign announcement fields
    private String campaignTitle;
    private String campaignLocation;
    private LocalDate campaignStartDate;
    private LocalDate campaignEndDate;
    private String campaignCause;
    private String campaignLink;

    // Receipt fields
    private int receiptYear;
    private String receiptLink;

    // Thank you note specific fields
    private String donationAmount;
    private String donationDate;
    private String campaignName;
    private String beneficiariesCount;
    private String projectsSupported;
    private String monthlyTarget;
    private String impactArea;
    private String livesImpacted;
    private String impactReportLink;

}