package com.chidhagni.donationreceipt.donorgroups.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DonorEmailContextDTO {

    private String donorName;
    private String donorEmail;
    private String organizationName;
    private int year;


    private String campaignTitle;
    private String campaignLocation;
    private LocalDate campaignStartDate;
    private LocalDate campaignEndDate;
    private String campaignCause;
    private String campaignLink;

}