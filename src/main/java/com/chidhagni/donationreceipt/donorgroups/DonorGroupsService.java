package com.chidhagni.donationreceipt.donorgroups;


import com.chidhagni.donationreceipt.db.jooq.tables.daos.DonorsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroupMapping;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.DonorGroups;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Donors;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsDto;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupsPaginationRequest;
import com.chidhagni.donationreceipt.donorgroups.dto.response.DonorResponse;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetAllDonorGroupsResponses;
import com.chidhagni.donationreceipt.donorgroups.dto.response.GetDonorGroupsResponse;
import com.chidhagni.donationreceipt.donorgroups.utils.DonorGroupsMapper;
import com.chidhagni.donationreceipt.donors.DonorsRepository;
import com.chidhagni.donationreceipt.security.UserPrincipal;
import com.chidhagni.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.jooq.Condition;
import org.jooq.SortField;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class DonorGroupsService {

    private final DonorGroupsRepository donorGroupsRepository;
    private final DonorGroupsMapper donorGroupMapper;
    private static Integer pageSize = 10;
    private static Integer pageNo = 1;
    private final DonorsRepository donorsRepository;

    public UUID createDonorGroups(DonorGroupsDto donorGroupsDto, UserPrincipal userPrincipal) {

        boolean isAlreadyExistByName=donorGroupsRepository.existsByName(donorGroupsDto.getName(),donorGroupsDto.getOrgId());
        if(isAlreadyExistByName){
            throw new IllegalArgumentException("Donor Groups Already Exists with the given name.Please try with different name");
        }
        DonorGroups donorGroups = donorGroupMapper.mapToDonorGroups(donorGroupsDto, userPrincipal);
        DonorGroups createdDonorGroups = donorGroupsRepository.create(donorGroups);

        if (donorGroupsDto.getDonorIds() != null && !donorGroupsDto.getDonorIds().isEmpty()) {
            donorGroupsRepository.addDonorsToGroup(createdDonorGroups.getId(), donorGroupsDto.getDonorIds(),userPrincipal);
        }
        return createdDonorGroups.getId();
    }

    public GetDonorGroupsResponse getDonorGroupsById(UUID id) {
        DonorGroups donorGroups = donorGroupsRepository.getById(id);
        if (donorGroups == null) {
            throw new IllegalArgumentException("Donor Groups Not found");
        }
        List<DonorResponse> donorResponseList=new ArrayList<>();
        List<DonorGroupMapping> donorGroupMappings = donorGroupsRepository.getDonorGroupMappingsByGroupId(id);
        for(DonorGroupMapping donorGroupMapping:donorGroupMappings)
        {
            DonorGroupMapping checkInActive=
                    donorGroupsRepository.fetchOneByDonorIdAndGroupId(donorGroupMapping.getDonorId(),donorGroupMapping.getGroupId());
            if(!checkInActive.getIsActive()){
                continue;
            }
            else{
                Donors donors = donorsRepository.getByDonorId(donorGroupMapping.getDonorId());
                donorResponseList.add(donorGroupMapper.mapToDonorResponse(donors));
            }
        }
        return donorGroupMapper.mapToDonorGroupsResponse(donorGroups,donorResponseList);
    }


    public GetDonorGroupsResponse updateDonorGroups(UUID id, DonorGroupsDto donorGroupsDto,
                                                    UserPrincipal userPrincipal) {
        DonorGroups existingDonorGroups = donorGroupsRepository.getById(id);
        if (existingDonorGroups == null) {
            throw new IllegalArgumentException("Donor Groups Not found");
        }

        // Update fields
        existingDonorGroups.setName(donorGroupsDto.getName());
        existingDonorGroups.setDescription(donorGroupsDto.getDescription());
        existingDonorGroups.setFilters(donorGroupsDto.getFilters());
        existingDonorGroups.setUpdatedBy(userPrincipal.getId());
        existingDonorGroups.setUpdatedOn(DateUtils.currentTimeIST());
        existingDonorGroups.setOrgId(donorGroupsDto.getOrgId());

        donorGroupsRepository.update(existingDonorGroups);


        // Fetch both active and inactive mappings (full history)
        List<UUID> allMappedDonorIds = donorGroupsRepository.getAllMappedDonorIdsByGroupId(id); // includes both active and inactive
        List<UUID> activeMappedDonorIds = donorGroupsRepository.getActiveMappedDonorIdsByGroupId(id); // only active ones

        List<UUID> newDonorIds = donorGroupsDto.getDonorIds() != null ? donorGroupsDto.getDonorIds() : new ArrayList<>();

        Set<UUID> allSet = new HashSet<>(allMappedDonorIds); // full history
        Set<UUID> activeSet = new HashSet<>(activeMappedDonorIds); // currently active
        Set<UUID> newSet = new HashSet<>(newDonorIds);

       // Donors to add (new ones or reactivating old ones)
        Set<UUID> toAdd = new HashSet<>(newSet);
        toAdd.removeAll(activeSet);

        // Donors to remove (deactivate if currently active but no longer present in new)
        Set<UUID> toRemove = new HashSet<>(activeSet);
        toRemove.removeAll(newSet);


        if (!toAdd.isEmpty()) {
            donorGroupsRepository.addDonorsToGroup(existingDonorGroups.getId(), new ArrayList<>(toAdd), userPrincipal);
        }

        if (!toRemove.isEmpty()) {
            donorGroupsRepository.removeDonorsFromGroup(existingDonorGroups.getId(), new ArrayList<>(toRemove));
        }
        List<DonorGroupMapping> donorGroupMappings = donorGroupsRepository.getDonorGroupMappingsByGroupId(id);
        List<DonorResponse> donorResponseList = new ArrayList<>();
        for (DonorGroupMapping mapping : donorGroupMappings) {
            Donors donor = donorsRepository.getByDonorId(mapping.getDonorId());
            donorResponseList.add(donorGroupMapper.mapToDonorResponse(donor));
        }

        return donorGroupMapper.mapToDonorGroupsResponse(existingDonorGroups, donorResponseList);
    }



    public void activateDonorGroups(UUID id, UUID userId) {
        DonorGroups donorGroups = donorGroupsRepository.getById(id);
        if (donorGroups == null) {
            throw new IllegalArgumentException("Donor Groups Not found");
        }
        donorGroups.setIsActive(Boolean.TRUE);
        donorGroups.setUpdatedBy(userId);
        donorGroups.setUpdatedOn(DateUtils.currentTimeIST());
        donorGroupsRepository.update(donorGroups);
    }

    public void deactivateDonorGroups(UUID id, UUID userId) {
        DonorGroups donorGroups = donorGroupsRepository.getById(id);
        if (donorGroups == null) {
            throw new IllegalArgumentException("Donor Groups Not found");
        }
        donorGroups.setIsActive(Boolean.FALSE);
        donorGroups.setUpdatedBy(userId);
        donorGroups.setUpdatedOn(DateUtils.currentTimeIST());
        donorGroupsRepository.update(donorGroups);
    }


    public GetAllDonorGroupsResponses getAllDonorGroupsResponses(DonorGroupsPaginationRequest paginationRequest, UserPrincipal userPrincipal) {
        applyDefaults(paginationRequest);
        Condition finalCondition = null;
        List<SortField<?>> sortFields = new ArrayList<>();
        List<GetDonorGroupsResponse> allDonorGroupsResponses =
                donorGroupsRepository.getAllDonorGroupsResponses(finalCondition, sortFields, paginationRequest,userPrincipal);
        int count = allDonorGroupsResponses.size();
        return GetAllDonorGroupsResponses.builder()
                .donorGroupsResponses(allDonorGroupsResponses)
                .rowCount(count)
                .build();
    }

    public void applyDefaults(DonorGroupsPaginationRequest paginationRequest) {
        if (paginationRequest.getPageSize() == null || paginationRequest.getPageSize() < 1) {
            paginationRequest.setPageSize(pageSize);
        }
        if (paginationRequest.getPage() == null || paginationRequest.getPage() < 1) {
            paginationRequest.setPage(pageNo);
        }
    }
}
