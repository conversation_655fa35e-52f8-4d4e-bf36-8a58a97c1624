package com.chidhagni.donationreceipt.notification;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetAllNotificationsResponse {

    private List<NotificationResponseDTO> listOfNotifications;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationResponseDTO {
        private UUID id;

        private String content;

        private String email;

        private Boolean isRead;

        private Boolean isActive;

        private String createdOn;
        private String updatedOn;
        private String createdBy;
        private String updatedBy;
    }
}
