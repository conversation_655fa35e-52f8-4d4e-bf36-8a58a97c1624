package com.chidhagni.donationreceipt.notification;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
public class NotificationDTO {

    private UUID id;

    private String content;

    private String email;

    private Boolean isRead;

    private Boolean isActive;

    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;

}
