package com.chidhagni.donationreceipt.notification;

import com.chidhagni.donationreceipt.contactus.dto.request.ContactUsRequestDTO;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.Individual;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.IndividualPasswordResetAudit;
import com.chidhagni.donationreceipt.donorgroups.dto.request.DonorEmailContextDTO;
import com.chidhagni.donationreceipt.mail.EmailHelperService;
import com.chidhagni.donationreceipt.mail.MailService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.io.File;
import java.util.*;


@Service
@RequiredArgsConstructor
public class NotificationManager {


    private final MailService mailService;
    private final EmailHelperService emailHelperService;

    private final String ACTIVATION_LINK = "email-verification.html";
    private final String ACTIVATION_LINK_DONOR = "email-verification-donors.html";
    private final String RESET_PASSWORD_TEMPLATE="reset-password.html";
    private final String CONTACT_FORM_TEMPLATE = "contact-form.html";
    private final String DONATION_RECEIPT="receipt-email.html";
    private final String CAMPAIGN_COMMUNICATION="campaign-announcement.html";



    public void sendActivationLink(String email,String activationLink,String trustName)
            throws MessagingException {
        String subject = "Donation Receipt - Activation Link";
        Map<String, Object> props = new HashMap<>();
        props.put("email", email);
        props.put("activationLink", activationLink);
        props.put("trustName",trustName);
        mailService.sendActivationLink(subject, email, null, null, props, ACTIVATION_LINK, null);
    }

    public void sendResetPasswordLink(Individual individual,
                                      IndividualPasswordResetAudit mapperIndividualPasswordResetAudit,
                                      UUID roleId) throws MessagingException {

        String resetLink=emailHelperService.generatePasswordResetLink(mapperIndividualPasswordResetAudit.getResetLink(),
                roleId,individual.getEmail());
        String subject = "Donation Receipt- Reset Password";
        Map<String, Object> props = new HashMap<>();
        props.put("registeredName", individual.getName());
        props.put("resetLink", resetLink);

        mailService.sendPasswordResetLink(subject, individual.getEmail(), null, null, props, RESET_PASSWORD_TEMPLATE, null);
    }


    public void sendContactFormEmailToAdmin(ContactUsRequestDTO contactUsRequestDTO) throws MessagingException {

        String subject = "Donation Receipt - Contact Us Form Submission";
        Map<String, Object> props = new HashMap<>();
        props.put("name", contactUsRequestDTO.getName());
        props.put("email", contactUsRequestDTO.getEmail());
        props.put("message", contactUsRequestDTO.getMessage());
        props.put("contactNumber", contactUsRequestDTO.getContactNumber());
        mailService.sendContactFormEmailToAdmin(subject,  props, CONTACT_FORM_TEMPLATE);
    }

    public void sendGeneratedReceipt(File attachmentFile, String name,String donorEmail,String donorName,String orgEmail,String orgTenantEmail) throws MessagingException {

        String subject="Donation Receipt – Please Find Attached Copy";
        Map<String, Object> props = new HashMap<>();
       props.put("trustName", name);
       props.put("donorName",donorName);

        List<String> ccEmails = new ArrayList<>();

        if (orgEmail != null) {
            ccEmails.add(orgEmail);
        }
        if (orgTenantEmail != null) {
            ccEmails.add(orgTenantEmail);
        }
        mailService.sendDonationReceipt(subject, donorEmail, ccEmails, null, props, DONATION_RECEIPT, attachmentFile);

    }


    public void sendActivationLinkDonor(String email,String activationLink,String name)
            throws MessagingException {
        String subject = "Donation Receipt - Activation Link";
        Map<String, Object> props = new HashMap<>();
        props.put("email", email);
        props.put("activationLink", activationLink);
        props.put("name",name);
        mailService.sendActivationLink(subject, email, null, null, props, ACTIVATION_LINK_DONOR, null);
    }

    public void sendCommunicationMessage(DonorEmailContextDTO donorEmailContextDTO,
                                         String templateName) throws MessagingException {
        String subject = getEmailSubject(templateName);
        Map<String, Object> props = new HashMap<>();

        // Common fields
        props.put("donorName", donorEmailContextDTO.getDonorName());
        props.put("organizationName", donorEmailContextDTO.getOrganizationName());
        props.put("year", donorEmailContextDTO.getYear());

        // Campaign announcement fields
        props.put("campaignTitle", donorEmailContextDTO.getCampaignTitle());
        props.put("campaignLocation", donorEmailContextDTO.getCampaignLocation());
        props.put("campaignStartDate", donorEmailContextDTO.getCampaignStartDate());
        props.put("campaignEndDate", donorEmailContextDTO.getCampaignEndDate());
        props.put("campaignCause", donorEmailContextDTO.getCampaignCause());
        props.put("campaignLink", donorEmailContextDTO.getCampaignLink());

        // Receipt fields
        props.put("receiptYear", donorEmailContextDTO.getReceiptYear());
        props.put("receiptLink", donorEmailContextDTO.getReceiptLink());

        // Thank you note specific fields
        props.put("donationAmount", donorEmailContextDTO.getDonationAmount());
        props.put("donationDate", donorEmailContextDTO.getDonationDate());
        props.put("campaignName", donorEmailContextDTO.getCampaignName());
        props.put("beneficiariesCount", donorEmailContextDTO.getBeneficiariesCount());
        props.put("projectsSupported", donorEmailContextDTO.getProjectsSupported());
        props.put("monthlyTarget", donorEmailContextDTO.getMonthlyTarget());
        props.put("impactArea", donorEmailContextDTO.getImpactArea());
        props.put("livesImpacted", donorEmailContextDTO.getLivesImpacted());
        props.put("impactReportLink", donorEmailContextDTO.getImpactReportLink());

        mailService.sendCommunicationMessage(subject, donorEmailContextDTO.getDonorEmail(), null, null,
                props, templateName, null);
    }

    private String getEmailSubject(String templateName) {
        switch (templateName) {
            case "campaign-announcement.html":
                return "Donation Receipt - Campaign Announcement";
            case "campaign-thank-you-note.html":
                return "Thank You for Your Generous Contribution";
            case "receipt-email.html":
                return "Donation Receipt - Please Find Attached Copy";
            case "acknowledgement_email.html":
                return "Acknowledgement of Your Support";
            default:
                return "Donation Receipt - Communication";
        }
    }
}
