package com.chidhagni.donationreceipt.notification;

//import com.chidhagni.houzer.db.jooq.tables.daos.NotificationsDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Notifications;
import org.springframework.stereotype.Service;

//import static com.chidhagni.houzer.db.jooq.Tables.NOTIFICATIONS;
//import static com.chidhagni.houzer.db.jooq.Tables.USERS;

@Service
public class NotificationService {
//    private final NotificationsDao notificationsDao;
//    private final DSLContext context;
//
//    public NotificationService(NotificationsDao notificationsDao, DSLContext context) {
//        this.notificationsDao = notificationsDao;
//        this.context = context;
//    }
//
//    public void updateNotificationRead(UUID id, UserPrincipal userPrincipal) {
//        Notifications notification = notificationsDao.fetchOneById(id);
//        if (notification == null) {
//            throw new IllegalArgumentException("Notification doesn't exist");
//        }
//        notification.setIsRead(true);
//        notification.setUpdatedBy(userPrincipal.getId());
//        notification.setUpdatedOn(DateUtils.currentTimeIST());
//        notificationsDao.update(notification);
//    }
//
//    @Transactional
//    public GetAllNotificationsResponse getAll(UserPrincipal userPrincipal) {
//        LocalDateTime thirtyDaysAgo = DateUtils.currentDatetime().minusDays(30);
//
//        String dateQuery = "TO_CHAR({0} AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Kolkata', 'YYYY-MM-DD HH24:MI:SS')";
//
//        List<GetAllNotificationsResponse.NotificationResponseDTO> notifications = context
//                .select(
//                        NOTIFICATIONS.ID,
//                        NOTIFICATIONS.CONTENT,
//                        NOTIFICATIONS.EMAIL,
//                        NOTIFICATIONS.IS_READ.as("isRead"),
//                        NOTIFICATIONS.IS_ACTIVE,
//                        DSL.field(dateQuery, String.class, NOTIFICATIONS.CREATED_ON).as("createdOn"),
//                        DSL.field(dateQuery, String.class, NOTIFICATIONS.UPDATED_ON).as("updatedOn"),
//                        DSL.concat(
//                                USERS.as("m2").FIRST_NAME.coalesce(""),
//                                DSL.when(USERS.as("m2").LAST_NAME.isNotNull(), DSL.concat(" ", USERS.as("m2").LAST_NAME)).otherwise("")
//                        ).as("createdBy"),
//                        USERS.as("m3").EMAIL.as("updatedBy")
//                )
//                .from(NOTIFICATIONS)
//                .leftOuterJoin(USERS.as("m2")).on(NOTIFICATIONS.CREATED_BY.eq(USERS.as("m2").ID))
//                .leftOuterJoin(USERS.as("m3")).on(NOTIFICATIONS.UPDATED_BY.eq(USERS.as("m3").ID))
//                .where(NOTIFICATIONS.CREATED_ON.greaterOrEqual(thirtyDaysAgo)
//                        .and(NOTIFICATIONS.USER_ID.eq(userPrincipal.getId()))
//                        .and(NOTIFICATIONS.IS_ACTIVE.isTrue()))
//                .fetchInto(GetAllNotificationsResponse.NotificationResponseDTO.class);
//
//        return GetAllNotificationsResponse.builder()
//                .listOfNotifications(notifications)
//                .build();
//    }
//
//
//    public void deleteNotification(UUID id, UserPrincipal userPrincipal) {
//        Notifications notification = notificationsDao.fetchOneById(id);
//        if (notification == null) {
//            throw new IllegalArgumentException("Notification doesn't exists");
//        }
//        notification.setIsActive(false);
//        notification.setUpdatedBy(userPrincipal.getId());
//        notification.setUpdatedOn(DateUtils.currentTimeIST());
//        notificationsDao.update(notification);
//    }
}
