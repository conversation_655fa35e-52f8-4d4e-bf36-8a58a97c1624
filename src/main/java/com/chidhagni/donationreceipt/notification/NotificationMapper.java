package com.chidhagni.donationreceipt.notification;

//import com.chidhagni.houzer.db.jooq.tables.pojos.Notifications;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Reminders;
import org.springframework.stereotype.Component;

@Component
public class NotificationMapper {


//    public Notifications map(Reminders reminders) {
//
//        Notifications notification = new Notifications();
//        notification.setId(UUID.randomUUID());
//        notification.setContent(reminders.getMessage());
//        notification.setUserId(reminders.getUserId());
//        notification.setEmail(reminders.getEmail());
//        notification.setContent(reminders.getMessage());
//        notification.setIsActive(true);
//        notification.setIsRead(false);
//        notification.setCreatedOn(DateUtils.currentTimeIST());
//        notification.setUpdatedOn(DateUtils.currentTimeIST());
//        notification.setCreatedBy(reminders.getCreatedBy());
//        notification.setUpdatedBy(reminders.getUpdatedBy());
//        return notification;
//    }
}
