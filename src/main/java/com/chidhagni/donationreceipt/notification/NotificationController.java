package com.chidhagni.donationreceipt.notification;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/notification")
@CrossOrigin
public class NotificationController {

    private final NotificationService notificationService;

    public NotificationController(NotificationService notificationService) {
        this.notificationService = notificationService;
    }

//    @PatchMapping(value = {"/{id}"}, produces = APPLICATION_JSON_VALUE)
//    public void updateNotificationRead(@PathVariable UUID id, @CurrentUser UserPrincipal userPrincipal) {
//        notificationService.updateNotificationRead(id, userPrincipal);
//    }
//
//
//    @PostMapping("/all")
//    public ResponseEntity<GetAllNotificationsResponse> getAll(@CurrentUser UserPrincipal userPrincipal) {
//        return ResponseEntity.ok().body(notificationService.getAll(userPrincipal));
//    }
//
//    @DeleteMapping(value = {"/{id}"}, produces = APPLICATION_JSON_VALUE)
//    public void deleteNotification(@PathVariable UUID id, @CurrentUser UserPrincipal userPrincipal) {
//        notificationService.deleteNotification(id, userPrincipal);
//    }


}
