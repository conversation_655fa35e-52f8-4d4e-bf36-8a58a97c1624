package com.chidhagni.donationreceipt.documentCategories;

import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/documentCategories")
public class DocumentCategoryController {

    private final DocumentCategoryService documentCategoryService;

    public DocumentCategoryController(DocumentCategoryService documentCategoryService) {
        this.documentCategoryService = documentCategoryService;
    }

//    @PostMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
//    public ResponseEntity<DocumentCategoryDTO> create(@RequestBody @Valid DocumentCategoryDTO body, @CurrentUser UserPrincipal userPrincipal) throws Exception {
//        DocumentCategoryDTO createdDocumentCategory = documentCategoryService.create(body, userPrincipal);
//
//        return ResponseEntity.created(
//                        UriComponentsBuilder.
//                                fromPath("/documentCategories").
//                                buildAndExpand()
//                                .toUri())
//                .body(createdDocumentCategory);
//    }
//
//
//    @GetMapping(value = {"/{categoryId}"}, produces = APPLICATION_JSON_VALUE)
//    public ResponseEntity<DocumentCategoryDTO> getDocumentCategory(@PathVariable UUID categoryId) throws JsonProcessingException {
//        return ResponseEntity.ok().body(documentCategoryService.getDocumentCategoryById(categoryId));
//
//    }
//
//    @DeleteMapping(value = {"/{categoryId}"}, produces = APPLICATION_JSON_VALUE)
//    public void deleteDocumentCategory(@PathVariable UUID categoryId, @CurrentUser UserPrincipal userPrincipal) throws JsonProcessingException {
//        documentCategoryService.deleteDocumentCategoryById(categoryId, userPrincipal);
//    }
//
//
//    @PostMapping("/all")
//    public ResponseEntity<List<DocumentCategoryResponseDTO>> getAll() {
//
//        return ResponseEntity.ok().body(documentCategoryService.getAll());
//    }

}
