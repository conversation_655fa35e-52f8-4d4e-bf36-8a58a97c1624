package com.chidhagni.donationreceipt.documentCategories;

//import com.chidhagni.houzer.db.jooq.tables.pojos.DocumentCategories;
import org.springframework.stereotype.Component;

@Component
public class DocumentCategoryDTOToDocumentCategoryMapper {

//    public DocumentCategories map(DocumentCategoryDTO documentCategoryDTO, UserPrincipal userPrincipal) {
//
//        DocumentCategories documentCategories = new DocumentCategories();
//
//        if (documentCategoryDTO.getId() == null) {
//            documentCategories.setId(UUID.randomUUID());
//            documentCategories.setIsActive(true);
//
//            documentCategories.setCreatedBy(userPrincipal.getId());
//            documentCategories.setUpdatedBy(userPrincipal.getId());
//            documentCategories.setCreatedOn(DateUtils.currentTimeIST());
//            documentCategories.setUpdatedOn(DateUtils.currentTimeIST());
//        } else {
//            documentCategories.setId(documentCategoryDTO.getId());
//            documentCategories.setIsActive(documentCategoryDTO.getIsActive());
//
//            documentCategories.setCreatedBy(documentCategoryDTO.getCreatedBy());
//            documentCategories.setUpdatedBy(documentCategoryDTO.getUpdatedBy());
//            documentCategories.setCreatedOn(documentCategoryDTO.getCreatedOn());
//            documentCategories.setUpdatedOn(documentCategoryDTO.getUpdatedOn());
//        }
//
//        documentCategories.setDocumentCategory(documentCategoryDTO.getDocumentCategory());
//
//        return documentCategories;
//    }
//
//    public DocumentCategoryDTO map(DocumentCategories documentCategories) {
//        return DocumentCategoryDTO.builder()
//                .id(documentCategories.getId())
//                .documentCategory(documentCategories.getDocumentCategory())
//                .isActive(documentCategories.getIsActive())
//                .createdBy(documentCategories.getCreatedBy())
//                .updatedBy(documentCategories.getUpdatedBy())
//                .createdOn(documentCategories.getCreatedOn())
//                .updatedOn(documentCategories.getUpdatedOn())
//                .build();
//
//    }
//
//    public void mapForDelete(DocumentCategories documentCategories, UserPrincipal userPrincipal) {
//        documentCategories.setIsActive(false);
//        documentCategories.setUpdatedOn(DateUtils.currentTimeIST());
//        documentCategories.setCreatedBy(userPrincipal.getId());
//    }
}
