package com.chidhagni.donationreceipt.documentCategories;

//import com.chidhagni.houzer.db.jooq.tables.daos.DocumentCategoriesDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.DocumentCategories;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

//import static com.chidhagni.houzer.db.jooq.Tables.DOCUMENT_CATEGORIES;
//import static com.chidhagni.houzer.db.jooq.Tables.MEMBER;

@Service
@Slf4j
public class DocumentCategoryService {
//    private final DSLContext dslContext;
//    private final DocumentCategoryDTOToDocumentCategoryMapper mapper;
//    private final DocumentCategoriesDao documentCategoriesDao;
//
//    public DocumentCategoryService(DSLContext dslContext, DocumentCategoryDTOToDocumentCategoryMapper mapper, DocumentCategoriesDao documentCategoriesDao) {
//        this.dslContext = dslContext;
//        this.mapper = mapper;
//        this.documentCategoriesDao = documentCategoriesDao;
//    }
//
//    public DocumentCategoryDTO create(DocumentCategoryDTO documentCategoryDTO, UserPrincipal userPrincipal) {
//
//        List<DocumentCategoryDTO> roleTypesList = dslContext.select()
//                .from(DOCUMENT_CATEGORIES)
//                .where(DOCUMENT_CATEGORIES.DOCUMENT_CATEGORY.containsIgnoreCase(documentCategoryDTO.getDocumentCategory()))
//                .fetchInto(DocumentCategoryDTO.class);
//
//        if (!roleTypesList.isEmpty()) {
//            throw new IllegalArgumentException("Document Category Already Exists");
//        }
//
//        DocumentCategories documentCategories = mapper.map(documentCategoryDTO, userPrincipal);
//        documentCategoriesDao.insert(documentCategories);
//
//        return mapper.map(documentCategories);
//    }
//
//    public DocumentCategoryDTO getDocumentCategoryById(UUID categoryId) {
//        DocumentCategories documentCategories = documentCategoriesDao.fetchOneById(categoryId);
//
//        if(documentCategories==null){
//            throw new IllegalArgumentException("Invalid document Category");
//        }
//
//        return mapper.map(documentCategories);
//    }
//
//
//    public void deleteDocumentCategoryById(UUID documentCategoryId, UserPrincipal userPrincipal) {
//        DocumentCategories documentCategories = documentCategoriesDao.fetchOneById(documentCategoryId);
//
//        if (documentCategories == null) {
//            throw new IllegalArgumentException("Invalid Document Category");
//        }
//
//        mapper.mapForDelete(documentCategories, userPrincipal);
//
//        documentCategoriesDao.update(documentCategories);
//    }
//
//    public List<DocumentCategoryResponseDTO> getAll() {
//
//        return dslContext.select(
//                        DOCUMENT_CATEGORIES.ID,
//                        DOCUMENT_CATEGORIES.DOCUMENT_CATEGORY,
//                        DOCUMENT_CATEGORIES.IS_ACTIVE,
//                        MEMBER.as("m2").LOGIN_EMAIL.as("createdBy"),
//                        MEMBER.as("m3").LOGIN_EMAIL.as("updatedBy"),
//                        DOCUMENT_CATEGORIES.CREATED_ON,
//                        DOCUMENT_CATEGORIES.UPDATED_ON
//                )
//                .from(DOCUMENT_CATEGORIES)
//                .leftOuterJoin(MEMBER.as("m2")).on(DOCUMENT_CATEGORIES.CREATED_BY.eq(MEMBER.as("m2").ID))
//                .leftOuterJoin(MEMBER.as("m3")).on(DOCUMENT_CATEGORIES.UPDATED_BY.eq(MEMBER.as("m3").ID))
//                .fetchInto(DocumentCategoryResponseDTO.class);
//
//    }

}
