package com.chidhagni.donationreceipt.documentCategories;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
public class DocumentCategoryResponseDTO {
    private UUID id;

    private String documentCategory;

    private Boolean isActive;

    private String createdBy;

    private String updatedBy;

    private LocalDateTime createdOn;

    private LocalDateTime updatedOn;
}
