package com.chidhagni.donationreceipt.payment;

//import com.chidhagni.houzer.common.exception.InternalServerError;
//import com.chidhagni.houzer.db.jooq.tables.daos.PaymentsDao;
//import com.chidhagni.houzer.payment.dto.response.PaymentResponseDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

//import static com.chidhagni.houzer.db.jooq.tables.Individual.INDIVIDUAL;
//import static com.chidhagni.houzer.db.jooq.tables.IndividualRole.INDIVIDUAL_ROLE;
//import static com.chidhagni.houzer.db.jooq.tables.Organisation.ORGANISATION;
//import static com.chidhagni.houzer.db.jooq.tables.Payments.PAYMENTS;
//import static com.chidhagni.houzer.db.jooq.tables.Settings.SETTINGS;
//import static com.chidhagni.houzer.db.jooq.tables.Subscriptions.SUBSCRIPTIONS;

@Repository
@RequiredArgsConstructor
public class PaymentsRepository {
//    private final PaymentsDao paymentsDao;
//    private static final String PACKAGE_TYPE_DATA_SET = "PackageTypesData";
//
//
//    /**
//     * Retrieves a paginated list of all payment transactions for administrators.
//     *
//     *
//     * <p>This method fetches detailed payment records from the database, including subscription,
//     * organization, and user details, for administrative users.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Calculates the pagination offset based on the requested page number and page size.</li>
//     *   <li>Performs a SQL query to fetch payment data along with related subscription and organization details.</li>
//     *   <li>Retrieves and maps the required data into 'PaymentResponseDTO':
//     *   <li>Handles exceptions by throwing an `InternalServerError` if any issue occurs during query execution.</li>
//     *   <li>Returns a list of `PaymentResponseDTO` objects containing payment transaction details.</li>
//     * </ul>
//     *
//     *
//     * @param request The request object containing pagination details.
//     * @return A list of `PaymentResponseDTO` objects containing payment transaction details.
//     * @throws InternalServerError If an error occurs while retrieving payment data.
//     */
//    public List<PaymentResponseDTO> getAllPaymentsByAdmin(PaginationRequest request) {
//        try {
//            Integer offset = (request.getPage() - 1) * request.getPageSize();
//            return paymentsDao.ctx().select(
//                            PAYMENTS.ID.as("id"),
//                            SUBSCRIPTIONS.ID.as("subscriptionId"),
//                            SUBSCRIPTIONS.PACKAGE_ID.as("packageId"),
//                            SUBSCRIPTIONS.UPGRADED_FROM.as("upgradedForm"),
//                            SUBSCRIPTIONS.FAILURE_DESCRIPTION.as("failureDescription"),
//                            SUBSCRIPTIONS.SP_ORGANISATION_ID.as("orgId"),
//                            ORGANISATION.NAME.as("orgName"),
//                            INDIVIDUAL.as("I0").ID.as("individualId"),
//                            DSL.concat(
//                                    DSL.coalesce(INDIVIDUAL.as("I0").FIRST_NAME, ""),
//                                    DSL.val(" "),
//                                    DSL.coalesce(INDIVIDUAL.as("I0").LAST_NAME, "")
//                            ).as("individualName"),
//                            PAYMENTS.PAYMENT_METHOD.as("paymentMethod"),
//                            PAYMENTS.PAYMENT_GATEWAY.as("paymentGateWay"),
//                            PAYMENTS.ORDER_ID.as("orderID"),
//                            PAYMENTS.STATUS.as("status"),
//                            PAYMENTS.PRICE.as("price"),
//                            PAYMENTS.IS_PAID.as("isPaid"),
//                            PAYMENTS.IS_ACTIVE.as("isActive"),
//                            INDIVIDUAL.as("I1").EMAIL.as("createdByEmail"),
//                            INDIVIDUAL.as("I2").EMAIL.as("updatedByEmail"),
//                            PAYMENTS.UPDATED_ON.as("updatedOn"),
//                            PAYMENTS.CREATED_ON.as("createdOn"),
//                            DSL.field(
//                                    "(SELECT package->>'name' " +
//                                            " FROM jsonb_array_elements(SETTINGS.METADATA->'packageTypes') package " +
//                                            " WHERE package->>'id' = {0}::text)",
//                                    String.class,
//                                    SUBSCRIPTIONS.PACKAGE_ID
//                            ).as("packageName")
//                    ).from(PAYMENTS).leftJoin(SUBSCRIPTIONS).on(PAYMENTS.SUBSCRIPTION_ID.eq(SUBSCRIPTIONS.ID))
//                    .leftJoin(ORGANISATION).on(ORGANISATION.ID.eq(SUBSCRIPTIONS.SP_ORGANISATION_ID))
//                    .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
//                    .leftJoin(INDIVIDUAL.as("I0")).on(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(INDIVIDUAL.as("I0").ID))
//                    .leftJoin(INDIVIDUAL.as("I1")).on(PAYMENTS.CREATED_BY.eq(INDIVIDUAL.as("I1").ID))
//                    .leftJoin(INDIVIDUAL.as("I2")).on(PAYMENTS.UPDATED_BY.eq(INDIVIDUAL.as("I2").ID))
//                    .leftJoin(SETTINGS).on(SETTINGS.NAME.eq(PACKAGE_TYPE_DATA_SET))
//                    .offset(offset)
//                    .limit(request.getPageSize())
//                    .fetchInto(PaymentResponseDTO.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while fetching all payments by admin", ex);
//        }
//    }
//
//
//    /**
//     * Retrieves the total count of all payment transactions for administrators.
//     *
//     * @return The total number of payment transactions.
//     * @throws InternalServerError If an error occurs while retrieving the count.
//     */
//    public Integer getAllPaymentsCountByAdmin() {
//        try {
//            return paymentsDao.ctx().selectCount().from(PAYMENTS).leftJoin(SUBSCRIPTIONS).on(PAYMENTS.SUBSCRIPTION_ID.eq(SUBSCRIPTIONS.ID))
//                    .leftJoin(ORGANISATION).on(ORGANISATION.ID.eq(SUBSCRIPTIONS.SP_ORGANISATION_ID))
//                    .leftJoin(INDIVIDUAL_ROLE).on(INDIVIDUAL_ROLE.ORG_ID.eq(ORGANISATION.ID))
//                    .leftJoin(INDIVIDUAL.as("I0")).on(INDIVIDUAL_ROLE.INDIVIDUAL_ID.eq(INDIVIDUAL.as("I0").ID))
//                    .leftJoin(INDIVIDUAL.as("I1")).on(PAYMENTS.CREATED_BY.eq(INDIVIDUAL.as("I1").ID))
//                    .leftJoin(INDIVIDUAL.as("I2")).on(PAYMENTS.UPDATED_BY.eq(INDIVIDUAL.as("I2").ID))
//                    .leftJoin(SETTINGS).on(SETTINGS.NAME.eq(PACKAGE_TYPE_DATA_SET))
//                    .fetchOneInto(Integer.class);
//        } catch (Exception ex) {
//            throw new InternalServerError("Exception while fetching all payments count by admin");
//        }
//    }
}
