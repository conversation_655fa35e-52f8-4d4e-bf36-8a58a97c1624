
package com.chidhagni.donationreceipt.payment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/payments")
public class PaymentController {
    @Autowired
    private PaymentService service;


    /**
     * Retrieves all payment with optional pagination.
     *
     *
     * <p>This endpoint allows users to fetch a list of all payments in the system.
     * If a pagination request is not provided, a default pagination configuration is applied.</p>
     *
     * <p><b>Logic:</b></p>
     * <ul>
     *   <li>If 'paginationRequest' is null, initializes it with default values.</li>
     *   <li>Delegates to 'service.getAllPayments()' to fetch payment records.</li>
     *   <li>Returns the list of payments wrapped in a `GetAllResponseDTO` object.</li>
     *   <li>Responds with HTTP status `200 OK` upon successful retrieval.</li>
     * </ul>
     *
     * @param paginationRequest (Optional) The pagination request containing page details.
     * @return A `ResponseEntity<GetAllResponseDTO>` containing the list of payments.
     * <AUTHOR>
     */
//    @PostMapping("/all")
//    public ResponseEntity<GetAllResponseDTO> getAllPayments(@RequestBody(required = false) PaginationRequest paginationRequest){
//        if(paginationRequest==null){
//            paginationRequest= PaginationRequest.builder().build();
//        }
//        return new ResponseEntity<>(service.getAllPayments(paginationRequest), HttpStatus.OK);
//    }
//
//    /**
//     * Creates a new payment transaction.
//     *
//     *
//     * <p>This endpoint allows authenticated users to initiate a new payment transaction.
//     * It processes the payment details provided in the request payload and returns
//     * the created payment record.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Receives payment details via 'paymentPayloadDTO'.</li>
//     *   <li>Identifies the authenticated user from 'userPrincipal'.</li>
//     *   <li>Delegates to 'service.createPayment()' to process the payment.</li>
//     *   <li>Returns the created payment record wrapped in a 'PaymentDTO' object.</li>
//     *   <li>Responds with HTTP status '201 Created' upon successful transaction.</li>
//     * </ul>
//     *
//     *
//     * @param paymentPayloadDTO The request payload containing payment details.
//     * @param userPrincipal The authenticated user performing the transaction.
//     * @return A 'ResponseEntity<PaymentDTO>' containing the details of the created payment.
//     * @throws RazorpayException If an error occurs during payment processing.
//     * <AUTHOR>
//     */
//    @PostMapping
//    public ResponseEntity<PaymentDTO> createPayment(@RequestBody PaymentPayloadDTO paymentPayloadDTO, @CurrentUser UserPrincipal userPrincipal) throws RazorpayException {
//
//        return new ResponseEntity<>(service.createPayment(paymentPayloadDTO, userPrincipal), HttpStatus.CREATED);
//    }
//
//    /**
//     * Retrieves the details of subscription.
//     *
//     *
//     * <p>This endpoint allows users to fetch details of a subscription using ID</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Receives the payment ID as a path variable.</li>
//     *   <li>Delegates to 'service.getPaymentDetails()' to fetch transaction details.</li>
//     *   <li>Returns the payment details wrapped in a 'PaymentDTO' object.</li>
//     *   <li>Responds with HTTP status `200 OK` upon successful retrieval.</li>
//     * </ul>
//
//     * @param paymentID The unique identifier of the payment transaction.
//     * @return A 'ResponseEntity<PaymentDTO>' containing the payment details.
//     *
//     * <AUTHOR>
//     */
//    @GetMapping("/{id}")
//    public ResponseEntity<PaymentDTO> getSubscriptionDetails(@PathVariable("id") UUID paymentID){
//        return new ResponseEntity<>(service.getPaymentDetails(paymentID), HttpStatus.OK);
//    }
//
//    /**
//     * Retrieves the Razorpay response details for a specific payment.
//     *
//     *
//     * <p>This endpoint allows users to fetch the payment response from Razorpay using the Razorpay payment ID.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>Receives the Razorpay payment ID as a path variable.</li>
//     *   <li>Delegates to 'service.getRazorpayResponse()' to fetch transaction details.</li>
//     *   <li>Returns the Razorpay payment response wrapped in a 'RazorpayResponseDTO' object.</li>
//     *   <li>Responds with HTTP status '200 OK' upon successful retrieval.</li>
//     * </ul>
//     *
//     *
//     * @param razorpayPaymentId The unique identifier of the Razorpay payment transaction.
//     * @return A 'ResponseEntity<RazorpayResponseDTO>' containing the Razorpay payment details.
//     * @throws Exception If an error occurs while retrieving the Razorpay response.
//     * <AUTHOR>
//     */
//    @GetMapping("/Razorpay/{id}")
//    public ResponseEntity<RazorpayResponseDTO> getRazorPayResponse(@PathVariable("id") String razorpayPaymentId) throws Exception {
//        return new ResponseEntity<>(service.getRazorpayResponse(razorpayPaymentId), HttpStatus.OK);
//    }
//
//    /**
//     * Retrieves all payment records for admin with optional pagination.
//     *
//     *
//     * <p>This endpoint allows administrators to fetch a list of all payments in the system.
//     * If a pagination request is not provided, a default pagination configuration is applied.</p>
//     *
//     * <p><b>Logic:</b></p>
//     * <ul>
//     *   <li>If 'paginationRequest' is null, initializes it with default values.</li>
//     *   <li>Delegates to 'service.getAllPaymentsResponseDTO()' to fetch payment records.</li>
//     *   <li>Returns the list of payments wrapped in a 'GetAllPaymentsResponseDTO' object.</li>
//     *   <li>Responds with HTTP status '200 OK' upon successful retrieval.</li>
//     * </ul>
//     *
//     *
//     * @param paginationRequest (Optional) The pagination request containing page details.
//     * @return A `ResponseEntity<GetAllPaymentsResponseDTO>` containing the list of payments.
//     *
//     * <AUTHOR>
//     */
//    @PostMapping("/admin/all")
//    public ResponseEntity<GetAllPaymentsResponseDTO> getAllPaymentsByAdmin(
//            @RequestBody(required = false) PaginationRequest paginationRequest){
//        if(paginationRequest==null){
//            paginationRequest= PaginationRequest.builder().build();
//        }
//        return new ResponseEntity<>(service.getAllPaymentsResponseDTO(paginationRequest), HttpStatus.OK);
//    }
}