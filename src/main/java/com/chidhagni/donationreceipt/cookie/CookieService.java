package com.chidhagni.donationreceipt.cookie;

//import com.chidhagni.houzer.db.jooq.tables.daos.CookieDao;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Cookie;
import org.springframework.stereotype.Service;


@Service
public class CookieService {

//    private CookieDao cookieDAO;
//
//    private DSLContext context;
//
//    private CookieDTOCookieMapper cookieMapper;
//
//    public CookieService(DSLContext context, CookieDao cookieDAO,CookieDTOCookieMapper cookieMapper) {
//        this.context = context;
//        this.cookieDAO = cookieDAO;
//        this.cookieMapper=cookieMapper;
//    }
//
//    @Transactional
//    public CookieDTO create(String accepted,String ipAddress) {
//        Cookie cookie = cookieMapper.map(CookieDTO.builder().accepted(accepted).ipAddress(ipAddress).build());
//        cookieDAO.insert(cookie);
//        return cookieMapper.map(cookie);
//    }
}
