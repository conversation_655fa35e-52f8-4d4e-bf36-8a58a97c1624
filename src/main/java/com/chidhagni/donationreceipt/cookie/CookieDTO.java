package com.chidhagni.donationreceipt.cookie;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
public class CookieDTO {

    private UUID id;

    private String ipAddress;

    private String accepted;

    private UUID createdBy;

    private UUID updatedBy;

    private LocalDateTime createdOn;

    private LocalDateTime updatedOn;
}
