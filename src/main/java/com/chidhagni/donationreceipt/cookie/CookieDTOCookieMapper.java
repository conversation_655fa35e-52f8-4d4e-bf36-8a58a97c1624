package com.chidhagni.donationreceipt.cookie;

//import com.chidhagni.houzer.db.jooq.tables.pojos.Cookie;
import org.springframework.stereotype.Component;

@Component
public class CookieDTOCookieMapper {
//    public CookieDTO map(Cookie cookie) {
//        return CookieDTO.builder()
//                .id(cookie.getId())
//                .ipAddress(cookie.getIpAddress())
//                .accepted(cookie.getAccepted())
//                .createdBy(cookie.getCreatedBy())
//                .updatedBy(cookie.getUpdatedBy())
//                .createdOn(cookie.getCreatedOn())
//                .updatedOn(cookie.getUpdatedOn())
//                .build();
//    }
//
//
//
//    public Cookie map(CookieDTO cookieDTO) {
//
//        Cookie cookie = new Cookie();
//
//        if (cookieDTO.getId() == null) {
//            cookie.setId(UUID.randomUUID());
//            cookie.setCreatedOn(DateUtils.currentTimeIST());
//        } else {
//            cookie.setId(cookieDTO.getId());
//            cookie.setCreatedOn(cookieDTO.getCreatedOn());
//        }
//
//        cookie.setIpAddress(cookieDTO.getIpAddress());
//        cookie.setAccepted(cookieDTO.getAccepted());
//
//        if (cookieDTO.getUpdatedOn() == null) {
//            cookie.setUpdatedOn(DateUtils.currentTimeIST());
//        } else {
//            cookie.setUpdatedOn(cookieDTO.getUpdatedOn());
//        }
//        return cookie;
//    }
}
