package com.chidhagni.donationreceipt.cookie;


import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/cookie")
public class CookieController {

    private final CookieService cookieService;

    public CookieController(CookieService cookieService) {
        this.cookieService = cookieService;
    }

//    @PostMapping(produces = APPLICATION_JSON_VALUE, consumes = APPLICATION_JSON_VALUE)
//    public ResponseEntity<CookieDTO> create(@RequestBody @Valid CookieRequest cookieRequest) {
//
//
//        CookieDTO createdCookie = cookieService.create(cookieRequest.getAccepted(), cookieRequest.getIpAddress());
//        return ResponseEntity.created(
//                        UriComponentsBuilder.
//                                fromPath("/cookie/{id}").
//                                buildAndExpand(createdCookie.getId())
//                                .toUri()
//                )
//                .body(createdCookie);
//    }
}
