package com.chidhagni.donationreceipt.wati;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.net.http.HttpResponse;

@RestController
@RequestMapping("/api/wati")
public class WatiController {

    @Autowired
    private WatiService watiService;

    @PostMapping("/sendTemplateMessage")
    public ResponseEntity<String> sendMessage(@RequestParam String whatsappNumber, @RequestBody @Valid WatiDTO watiDTO)
            throws IOException, InterruptedException {

        HttpResponse<String> response = watiService.sendTemplateMessage(whatsappNumber, watiDTO);
        return ResponseEntity.status(response.statusCode()).body(response.body());
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> upload(@RequestParam String whatsappNumber, @RequestParam("files") MultipartFile file)
            throws IOException {

        okhttp3.Response response = watiService.sendSessionFile(whatsappNumber, file);
        return ResponseEntity.status(response.code()).body(response.body().string());
    }

    @PostMapping("/TemplateMessages")
    public ResponseEntity<String> sendTemplateMessage(@RequestBody @Valid TemplateMessagesDTO templateMessagesDTO)
            throws IOException, InterruptedException {

        HttpResponse<String> response = watiService.sendTemplateMessages(templateMessagesDTO);
        return ResponseEntity.status(response.statusCode()).body(response.body());
    }

    @PostMapping("/sessionMessage")
    public ResponseEntity<String> sendSessionMessage(@RequestParam String whatsappNumber, @RequestParam String message)
            throws IOException, InterruptedException {

        HttpResponse<String> response = watiService.sendSessionMessages(whatsappNumber, message);
        return ResponseEntity.status(response.statusCode()).body(response.body());
    }
}
