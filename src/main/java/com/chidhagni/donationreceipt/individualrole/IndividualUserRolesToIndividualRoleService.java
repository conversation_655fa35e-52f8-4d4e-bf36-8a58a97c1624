package com.chidhagni.donationreceipt.individualrole;


//import com.chidhagni.houzer.db.jooq.tables.daos.IndividualRoleDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

//import static com.chidhagni.houzer.db.jooq.tables.Individual.INDIVIDUAL;
//import static com.chidhagni.houzer.db.jooq.tables.Role.ROLE;
//import static com.chidhagni.houzer.db.jooq.tables.Roles.ROLES;
//import static com.chidhagni.houzer.db.jooq.tables.Users.USERS;

@RequiredArgsConstructor
@Service
@Slf4j
public class IndividualUserRolesToIndividualRoleService {


//    private final IndividualRoleDao individualRoleDao;
//
//
//    /**
//     * Fetches a list of IndividualRoleDTO objects containing detailed information about individuals,
//     * their roles, and associated organizations.
//     * <p>
//     * This method retrieves data by joining multiple tables ('USERS', 'INDIVIDUAL', 'ROLE', and 'ROLES'),
//     * and applies a conditional logic to determine the associated organization ID based on the role of the individual.
//     * The organization ID is derived from different user attributes (`companyName`, `meta_data->>'name'`, or
//     * a predefined value like 'EMPLOYEE') depending on the role assigned to the user.
//     * <p>
//     * The fetched data is ordered by the 'USERS.UPDATED_ON' column in ascending order, and pagination is applied
//     * using the `offset` parameter.
//     *
//     * @param lastProcessedRow The number of rows to offset for pagination, indicating the last processed row from the
//     *                         previous batch.
//     * @return A list of IndividualRoleDTO objects containing information about the individual's ID, user ID, role ID,
//     *         roles ID, and the computed organization ID.
//     *
//     * <h3>SQL Logic Explanation:</h3>
//     * <ul>
//     *     <li>For role ID '3676706b-fc31-4b41-956c-3c4c758aa663', the organization ID is determined based on the
//     *         companyName attribute from `USERS.basic_profile`.</li>
//     *     <li>For role ID 'ad60ce4e-f528-4722-82aa-8757baafce7a', the organization ID is determined based on the
//     *         name attribute from 'USERS.meta_data'.</li>
//     *     <li>For role ID 'cafac5b3-77dd-4fcf-b890-6a2f63b9ee44', the organization ID is hardcoded as 'EMPLOYEE'.</li>
//     *     <li>For role ID '107dc277-e80a-4d4c-9f0a-48bcbad5bea3', the organization ID is explicitly set to 'NULL'.</li>
//     *     <li>For all other roles, the organization ID remains 'NULL'.</li>
//     * </ul>
//     *
//     * * @throws InternalServerError if an exception occurs while fetching the data.
//     */
//    public List<IndividualRoleDTO> fetchFullModeIndividualRoleData(Integer lastProcessedRow) {
//        log.info("Fetching IndividualRoleDTOs with lastProcessedRow: {}", lastProcessedRow);
//
//        List<IndividualRoleDTO> individualRoleDTOList = individualRoleDao.ctx()
//                .select(
//                        INDIVIDUAL.ID.as("individual_id"),
//                        USERS.ID.as("user_id"),
//                        ROLE.ID.as("role_id"),
//                        ROLES.ID.as("roles_id"),
//
//                        DSL.field("CASE " +
//                                "WHEN ROLE.ID = uuid('3676706b-fc31-4b41-956c-3c4c758aa663') THEN (" +
//                                "    SELECT org.ID " +
//                                "    FROM organisation org " +
//                                "    WHERE org.name = COALESCE(USERS.basic_profile->>'companyName', '') " +
//                                "    LIMIT 1" +
//                                ")" +
//                                "WHEN ROLE.ID = uuid('ad60ce4e-f528-4722-82aa-8757baafce7a') THEN (" +
//                                "    SELECT org.ID " +
//                                "    FROM organisation org " +
//                                "    WHERE org.name = COALESCE(USERS.meta_data->>'name', '') " +
//                                "    LIMIT 1" +
//                                ")" +
//                                "WHEN ROLE.ID = uuid('cafac5b3-77dd-4fcf-b890-6a2f63b9ee44') THEN (" +
//                                "    SELECT org.ID " +
//                                "    FROM organisation org " +
//                                "    WHERE org.name = 'EMPLOYEE' " +
//                                "    LIMIT 1" +
//                                ")" +
//                                "WHEN ROLE.ID = uuid('107dc277-e80a-4d4c-9f0a-48bcbad5bea3') THEN NULL " +
//                                "ELSE NULL " +
//                                "END"
//                        ).as("org_id")
//                )
//                .from(USERS)
//                .leftJoin(INDIVIDUAL).on(USERS.ID.eq(INDIVIDUAL.ID))
//                .leftJoin(ROLE).on(USERS.ROLE_ID.eq(ROLE.ID))
//                .leftJoin(ROLES).on(ROLE.ID.eq(ROLES.ID))
//                .orderBy(USERS.UPDATED_ON.asc())
//                .offset(lastProcessedRow)
//                .fetchInto(IndividualRoleDTO.class);
//
//        return individualRoleDTOList;
//    }
//
//
//
//    /**
//     * Fetches a list of IndividualRoleDTO objects containing individual role data for a specified time interval.
//     * <p>
//     * This method retrieves data by joining multiple tables ('USERS', 'INDIVIDUAL', 'ROLE', and 'ROLES') and applies
//     * conditional logic to determine the associated organization ID based on the role of the individual.
//     * The organization ID is derived from different user attributes ('companyName', 'meta_data->>'name'', or
//     * a predefined value like 'EMPLOYEE') depending on the role assigned to the user.
//     * <p>
//     * The fetched data is filtered to include only records that have been updated within the given time interval.
//     * It orders the results by 'USERS.UPDATED_ON' in ascending order.
//     *
//     * @param TimeInterval The number of minutes defining the time window. Only records updated within this time interval
//     *                     (from now - TimeInterval to now) will be included.
//     * @return A list of  IndividualRoleDTO objects containing information about the individual's ID, user ID,
//     *         role ID, roles ID, and the computed organization ID.
//     *
//     *
//     * <h3>SQL Logic Explanation:</h3>
//     * <ul>
//     *     <li>For role ID '3676706b-fc31-4b41-956c-3c4c758aa663', the organization ID is determined based on the
//     *         'companyName' attribute from 'USERS.basic_profile'.</li>
//     *     <li>For role ID 'ad60ce4e-f528-4722-82aa-8757baafce7a', the organization ID is determined based on the
//     *         'name' attribute from 'USERS.meta_data'.</li>
//     *     <li>For role ID 'cafac5b3-77dd-4fcf-b890-6a2f63b9ee44', the organization ID is hardcoded as 'EMPLOYEE'.</li>
//     *     <li>For role ID '107dc277-e80a-4d4c-9f0a-48bcbad5bea3', the organization ID is explicitly set to 'NULL'.</li>
//     *     <li>For all other roles, the organization ID remains 'NULL'.</li>
//     * </ul>
//     *
//     * <h3>Time Filtering Logic:</h3>
//     * <ul>
//     *     <li>Defines 'now' as the current timestamp.</li>
//     *     <li>Defines 'windowStartingTime' as 'now - TimeInterval' minutes.</li>
//     *     <li>Filters records where 'USERS.UPDATED_ON' is between 'windowStartingTime' and 'now'.</li>
//     * </ul>
//     *
//     */
//    public List<IndividualRoleDTO> fetchIndividualRolePartialModeData(Integer TimeInterval) {
//        LocalDateTime now = LocalDateTime.now();
//        LocalDateTime windowStartingTime = now.minusMinutes(TimeInterval);
//        return individualRoleDao.ctx()
//                .select(
//                        INDIVIDUAL.ID.as("individual_id"),
//                        USERS.ID.as("user_id"),
//                        ROLE.ID.as("role_id"),
//                        ROLES.ID.as("roles_id"),
//                        DSL.field("CASE " +
//                                "WHEN ROLE.ID = uuid('3676706b-fc31-4b41-956c-3c4c758aa663') THEN (" +
//                                "    SELECT org.ID " +
//                                "    FROM organisation org " +
//                                "    WHERE org.name = COALESCE(USERS.basic_profile->>'companyName', '')" +
//                                ")" +
//                                "WHEN ROLE.ID = uuid('ad60ce4e-f528-4722-82aa-8757baafce7a') THEN (" +
//                                "    SELECT org.ID " +
//                                "    FROM organisation org " +
//                                "    WHERE org.name = COALESCE(USERS.meta_data->>'name', '')" +
//                                ")" +
//                                "WHEN ROLE.ID = uuid('cafac5b3-77dd-4fcf-b890-6a2f63b9ee44') THEN (" +
//                                "    SELECT org.ID " +
//                                "    FROM organisation org " +
//                                "    WHERE org.name = 'EMPLOYEE'" +
//                                ")" +
//                                "WHEN ROLE.ID = uuid('107dc277-e80a-4d4c-9f0a-48bcbad5bea3') THEN NULL " +
//                                "ELSE NULL " +
//                                "END"
//                        ).as("org_id")
//                )
//                .from(USERS)
//                .join(INDIVIDUAL).on(USERS.ID.eq(INDIVIDUAL.ID))
//                .join(ROLE).on(USERS.ROLE_ID.eq(ROLE.ID))
//                .join(ROLES).on(ROLE.ID.eq(ROLES.ID))
//                .where(USERS.UPDATED_ON.between(windowStartingTime, now))
//                .orderBy(USERS.UPDATED_ON.asc())
//                .fetchInto(IndividualRoleDTO.class);
//    }


}
