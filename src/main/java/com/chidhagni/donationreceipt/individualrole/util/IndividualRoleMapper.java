package com.chidhagni.donationreceipt.individualrole.util;

//import com.chidhagni.houzer.db.jooq.tables.pojos.IndividualRole;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface IndividualRoleMapper {
//
//    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
//    @Mapping(target = "individualId", source = "individualId")
//    @Mapping(target = "roleId", source = "roleId")
//    @Mapping(target = "createdOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "createdBy", source = "individualId")
//    @Mapping(target = "updatedBy", ignore = true)
//    @Mapping(target = "orgId", source = "organisationId")
//    IndividualRole createIndividualRole(UUID individualId, UUID roleId, UUID organisationId);
//
//    @Mapping(target = "roleId", source = "roleId")
//    @Mapping(target = "updatedBy", source = "loggedInIndividualId")
//    @Mapping(target = "updatedOn", expression = "java(com.chidhagni.utils.DateUtils.currentDatetime())")
//    @Mapping(target = "id", ignore = true)
//    @Mapping(target = "individualId", ignore = true)
//    @Mapping(target = "createdOn", ignore = true)
//    @Mapping(target = "createdBy", ignore = true)
//    @Mapping(target = "orgId", ignore = true)
//    void updateIndividualRole(UUID roleId, @MappingTarget IndividualRole individualRole, UUID loggedInIndividualId);

}
