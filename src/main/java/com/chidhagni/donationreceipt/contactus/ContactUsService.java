package com.chidhagni.donationreceipt.contactus;


import com.chidhagni.donationreceipt.contactus.dto.request.ContactUsRequestDTO;
import com.chidhagni.donationreceipt.contactus.dto.response.ContactUsResponseDTO;
import com.chidhagni.donationreceipt.contactus.utils.ContactUsMapper;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs;
import com.chidhagni.donationreceipt.notification.NotificationManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.mail.MessagingException;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ContactUsService {

    private final ContactUsRepository contactUsRepository;
    private final ContactUsMapper contactUsMapper;
    private final NotificationManager notificationManager;

    public UUID create(ContactUsRequestDTO contactUsRequestDTO) throws MessagingException {
        ContactUs contactUs =
                contactUsMapper.contactUsDtoToContactUs(contactUsRequestDTO);
        contactUsRepository.saveContactUs(contactUs);
        log.info("ContactUs entry created with [id={}]", contactUs.getId());
        //TO DO SEND EMAIL
        notificationManager.sendContactFormEmailToAdmin(contactUsRequestDTO);
        return contactUs.getId();
    }
}
