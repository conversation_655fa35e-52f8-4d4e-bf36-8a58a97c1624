package com.chidhagni.donationreceipt.contactus;

import com.chidhagni.donationreceipt.db.jooq.tables.daos.ContactUsDao;
import com.chidhagni.donationreceipt.db.jooq.tables.pojos.ContactUs;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.web.server.ServerErrorException;

@Repository
@RequiredArgsConstructor
public class ContactUsRepository {

    private final ContactUsDao contactUsDao;

    public void saveContactUs(ContactUs contactUs) {
        try {
            contactUsDao.insert(contactUs);
        } catch (Exception ex) {
            throw new ServerErrorException(
                    String.format("Exception occurred while creating contact us entry [name=%s]", contactUs.getName()), ex);
        }
    }
}