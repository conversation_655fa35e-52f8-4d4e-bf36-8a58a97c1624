package com.chidhagni.donationreceipt.member;

//
//import com.chidhagni.houzer.db.jooq.tables.pojos.Entity;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Member;
//import com.chidhagni.houzer.db.jooq.tables.pojos.StagingEmailVerification;
//import com.chidhagni.houzer.db.jooq.tables.pojos.Users;
//import com.chidhagni.houzer.entity.shared.enums.EntityCategory;
//import com.chidhagni.houzer.entity.shared.enums.EntityType;
//import com.chidhagni.houzer.login.SignUpRequest;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
public class MemberDTOMemberMapper {
   // public final PasswordEncoder passwordEncoder;

//    public MemberDTOMemberMapper(PasswordEncoder passwordEncoder) {
//        this.passwordEncoder = passwordEncoder;
//    }

//    public MemberDTO map(Member member) {
//        return MemberDTO.builder()
//                .id(member.getId())
//                .firstName(member.getFirstName())
//                .lastName(member.getLastName())
//                .email(member.getLoginEmail())
//                .mobileNumber(member.getLoginMobileNumber())
//                .createdOn(member.getCreatedOn())
//                .updatedOn(member.getUpdatedOn())
//                .build();
//    }
//
//
//    public MemberProfileDTO map(Users user, List<String> permissions, Entity entity) {
//        MemberProfileDTO.MemberProfileDTOBuilder member = MemberProfileDTO.builder()
//                .id(user.getId())
//                .permissions(permissions);
//        if (entity != null) {
//            member.entityId(entity.getId()).entityCategory(EntityCategory.valueOf(entity.getCategory()))
//                    .type(EntityType.valueOf(entity.getType()));
//        }
//        member.firstName(user.getFirstName())
//                .lastName(user.getLastName())
//                .email(user.getEmail())
//                .mobileNumber(user.getMobileNumber())
//                .createdOn(user.getCreatedOn())
//                .updatedOn(user.getUpdatedOn())
//                .build();
//        return member.build();
//    }
//
//
//    public Member map(MemberDTO memberDTO) {
//
//        Member member = new Member();
//        if (memberDTO.getId() == null) {
//            member.setId(UUID.randomUUID());
//            member.setCreatedOn(DateUtils.currentTimeIST());
//        } else {
//            member.setId(memberDTO.getId());
//            member.setCreatedOn(memberDTO.getCreatedOn());
//        }
//
//        member.setFirstName(memberDTO.getFirstName());
//        member.setLastName(memberDTO.getLastName());
//        member.setLoginEmail(memberDTO.getEmail());
//        member.setLoginMobileNumber(memberDTO.getMobileNumber());
//
//        if (memberDTO.getUpdatedOn() == null) {
//            member.setUpdatedOn(DateUtils.currentTimeIST());
//        } else {
//            member.setUpdatedOn(memberDTO.getUpdatedOn());
//        }
//        member.setIsEmpanelled(false);
//        return member;
//    }
//
//    public Member map(SignUpRequest signUpRequest) {
//        Member member = new Member();
//
//        member.setId(UUID.randomUUID());
//        member.setFirstName(signUpRequest.getFirstName());
//        member.setLastName(signUpRequest.getLastName());
//        member.setLoginEmail(signUpRequest.getEmail());
//        member.setLoginMobileNumber(signUpRequest.getMobileNo());
//        member.setCreatedOn(DateUtils.currentTimeIST());
//        member.setUpdatedOn(DateUtils.currentTimeIST());
//        member.setIsEmpanelled(false);
//
//        return member;
//    }
//
//    public Member map(StagingEmailVerification stagingRecord) {
//
//        Member member = new Member();
//
//        member.setId(UUID.randomUUID());
//        member.setCreatedOn(DateUtils.currentTimeIST());
//        member.setUpdatedOn(DateUtils.currentTimeIST());
//
//        member.setFirstName(stagingRecord.getName());
//        member.setLoginEmail(stagingRecord.getEmail());
//        member.setLoginMobileNumber(stagingRecord.getContactNumber());
//        member.setIsVerified(stagingRecord.getIsVerified());
//        member.setIsEmpanelled(false);
//
//
//        return member;
//    }
}
