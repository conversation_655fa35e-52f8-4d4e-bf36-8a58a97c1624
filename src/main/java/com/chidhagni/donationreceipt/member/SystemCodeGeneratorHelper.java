package com.chidhagni.donationreceipt.member;

//import com.chidhagni.houzer.db.jooq.tables.pojos.Entity;
//import com.chidhagni.houzer.entity.profile.architect.ArchitectRegisterDTO;
//import com.chidhagni.houzer.entity.profile.broker.BrokerRegisterDTO;
//import com.chidhagni.houzer.entity.profile.charteredAccountant.CharteredAccountantRegisterDTO;
//import com.chidhagni.houzer.entity.profile.legal.LegalRegisterDTO;
//import com.chidhagni.houzer.entity.profile.pmc.PMCRegisterDTO;
//import com.chidhagni.houzer.entity.profile.society.SocietyRegisterDTO;
//import com.chidhagni.houzer.entity.profile.structural.StructuralEngineerRegisterDTO;
//import com.chidhagni.houzer.entity.shared.SystemCodeGenerator;
//import com.chidhagni.houzer.entity.shared.enums.EntityCategory;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SystemCodeGeneratorHelper {
    @Autowired
    ObjectMapper objectMapper;

//    public void setEntityMetaData(Entity entity, String entityCategory) throws JsonProcessingException {
//        switch (EntityCategory.valueOf(entityCategory)) {
//            case SOCIETY:
//                entity.setMetaData(JSONB.valueOf(objectMapper.writeValueAsString(buildSocietyDTOWithSystemCode())));
//                break;
//
//            case ARCHITECT:
//                entity.setMetaData(JSONB.valueOf(objectMapper.writeValueAsString(buildArchitectDTOWithSystemCode())));
//                break;
//
//            case STRUCTURAL_ENGINEER:
//                entity.setMetaData(JSONB.valueOf(objectMapper.writeValueAsString(buildStructuralDTOWithSystemCode())));
//                break;
//
//            case PMC:
//                entity.setMetaData(JSONB.valueOf(objectMapper.writeValueAsString(buildPMCDTOWithSystemCode())));
//                break;
//
//            case BROKER:
//                entity.setMetaData(JSONB.valueOf(objectMapper.writeValueAsString(buildBrokerDTOWithSystemCode())));
//                break;
//
//            case CHARTERED_ACCOUNTANT:
//                entity.setMetaData(JSONB.valueOf(objectMapper.writeValueAsString(buildCADTOWithSystemCode())));
//                break;
//
//            case LEGAL:
//                entity.setMetaData(JSONB.valueOf(objectMapper.writeValueAsString(buildLegalDTOWithSystemCode())));
//                break;
//
//        }
//
//    }
//
//
//
//    private StructuralEngineerRegisterDTO buildStructuralDTOWithSystemCode() {
//        SystemCodeGenerator systemCodeGenerator = new SystemCodeGenerator();
//        return StructuralEngineerRegisterDTO.builder().id(UUID.randomUUID()).systemCode(systemCodeGenerator.generateCode(SE)).build();
//    }
//
//    private PMCRegisterDTO buildPMCDTOWithSystemCode() {
//        SystemCodeGenerator systemCodeGenerator = new SystemCodeGenerator();
//        return PMCRegisterDTO.builder().id(UUID.randomUUID()).systemCode(systemCodeGenerator.generateCode(PMC)).build();
//    }
//
//    private BrokerRegisterDTO buildBrokerDTOWithSystemCode() {
//        SystemCodeGenerator systemCodeGenerator = new SystemCodeGenerator();
//        return BrokerRegisterDTO.builder().id(UUID.randomUUID()).systemCode(systemCodeGenerator.generateCode(BR)).build();
//    }
//
//    private CharteredAccountantRegisterDTO buildCADTOWithSystemCode() {
//        SystemCodeGenerator systemCodeGenerator = new SystemCodeGenerator();
//        return CharteredAccountantRegisterDTO.builder().id(UUID.randomUUID()).systemCode(systemCodeGenerator.generateCode(CA)).build();
//    }
//
//    private ArchitectRegisterDTO buildArchitectDTOWithSystemCode() {
//        SystemCodeGenerator systemCodeGenerator = new SystemCodeGenerator();
//        return ArchitectRegisterDTO.builder().id(UUID.randomUUID()).systemCode(systemCodeGenerator.generateCode(AR)).build();
//    }
//
//    private SocietyRegisterDTO buildSocietyDTOWithSystemCode() {
//        SystemCodeGenerator systemCodeGenerator = new SystemCodeGenerator();
//        return SocietyRegisterDTO.builder().id(UUID.randomUUID()).systemCode(systemCodeGenerator.generateCode(SC)).build();
//    }
//
//    private LegalRegisterDTO buildLegalDTOWithSystemCode(){
//        SystemCodeGenerator systemCodeGenerator= new SystemCodeGenerator();
//        return LegalRegisterDTO.builder().id(UUID.randomUUID()).systemCode(systemCodeGenerator.generateCode(LG)).build();
//    }

}
