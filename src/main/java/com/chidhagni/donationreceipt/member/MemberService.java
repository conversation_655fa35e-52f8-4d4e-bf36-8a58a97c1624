package com.chidhagni.donationreceipt.member;

//import com.chidhagni.houzer.db.jooq.Tables;
//import com.chidhagni.houzer.db.jooq.tables.daos.*;
//import com.chidhagni.houzer.db.jooq.tables.pojos.*;
//import com.chidhagni.houzer.entity.shared.enums.EntityCategory;
//import com.chidhagni.houzer.entity.shared.enums.EntityType;
//import com.chidhagni.houzer.entitymember.MemberDetailsDTOToMemberMapper;
//import com.chidhagni.houzer.individualPasswordResetAudit.IndividualPasswordResetRepository;
//import com.chidhagni.houzer.individualverificationaudit.constants.VerificationStatusEnum;
//import com.chidhagni.houzer.login.LoginService;
//import com.chidhagni.houzer.login.SignUpDTO;
//import com.chidhagni.houzer.login.SignUpRequest;
//import com.chidhagni.houzer.mail.OtpVerifyDTO;
//import com.chidhagni.houzer.notification.NotificationManager;
//import com.chidhagni.houzer.organisation.constants.DefaultOrganisationCategoryEnums;
//import com.chidhagni.houzer.organisation.constants.RegisteredFor;
//import com.chidhagni.houzer.services.userserviceprofile.UserServiceProfileService;
//import com.chidhagni.houzer.userrole.DefaultRolesEnums;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
//import static com.chidhagni.houzer.db.jooq.tables.Individual.INDIVIDUAL;
//import static com.chidhagni.houzer.entity.shared.enums.EntityCategory.SUPER_ADMIN;

@Slf4j
@Service
public class MemberService {

//    private final MemberDao memberDao;
//    private final EntityDao entityDao;
//    private final EntityMemberDao entityMemberDao;
//    private final NotificationManager notificationManager;
//    private final PasswordEncoder passwordEncoder;
//    private final UserServiceProfileService userServiceProfileService;
//    private final LoginService loginService;
//    private final MemberDetailsDTOToMemberMapper memberDetailsDTOToMemberMapper;
//    private final StagingEmailVerificationDao stagingEmailVerificationDao;
//    private final UsersDao usersDao;
//    private final UsersMapper usersMapper;
//
//    private final RoleDao roleDao;
//
//    private final CommonOperations commonOperations;
//    private final IndividualPasswordResetRepository individualPasswordResetRepository;
//    private final IndividualVerificationAuditDao individualVerificationAuditDao;
//
//    @Value("${server.hostUrl}")
//    public String resetUrl;
//
//    @Autowired
//    DSLContext dslContext;
//
//
//    public MemberService(MemberDao memberDao, EntityDao entityDao, EntityMemberDao entityMemberDao
//            , NotificationManager notificationManager, PasswordEncoder passwordEncoder
//            , UserServiceProfileService userServiceProfileService, LoginService loginService
//            , MemberDetailsDTOToMemberMapper memberDetailsDTOToMemberMapper
//            , StagingEmailVerificationDao stagingEmailVerificationDao, UsersDao usersDao, UsersMapper usersMapper
//            , RoleDao roleDao, CommonOperations commonOperations
//            , IndividualPasswordResetRepository individualPasswordResetRepository
//            , IndividualVerificationAuditDao individualVerificationAuditDao) {
//        this.memberDao = memberDao;
//        this.entityDao = entityDao;
//        this.entityMemberDao = entityMemberDao;
//        this.notificationManager = notificationManager;
//        this.passwordEncoder = passwordEncoder;
//        this.userServiceProfileService = userServiceProfileService;
//        this.loginService = loginService;
//        this.memberDetailsDTOToMemberMapper = memberDetailsDTOToMemberMapper;
//        this.stagingEmailVerificationDao = stagingEmailVerificationDao;
//        this.usersDao = usersDao;
//        this.usersMapper = usersMapper;
//        this.roleDao = roleDao;
//        this.commonOperations = commonOperations;
//        this.individualPasswordResetRepository = individualPasswordResetRepository;
//        this.individualVerificationAuditDao = individualVerificationAuditDao;
//    }
//
////    @Transactional
////    public MemberDTO create(MemberDTO memberDTO) throws MessagingException {
////
////        //TODO:memberDTO not returning to the controller
////        if (!permissionsData.isValidPermissions(memberDTO.getPermissions())) {
////            log.debug("Invalid Permissions Data: {}", memberDTO.getPermissions());
////            throw new IllegalArgumentException("Invalid Data");
////        }
////
////        if (!memberDao.fetchByLoginEmail(memberDTO.getEmail()).isEmpty()) {
////            log.debug("EmailId already Exist");
////            throw new IllegalArgumentException("Email Already Exist");
////        }
////
////        if (!entityDao.existsById(memberDTO.getEntityId())) {
////            log.debug("Invalid Entity ID Data:" + memberDTO.getEntityId());
////            throw new IllegalArgumentException("Invalid Data");
////        }
////
////        //TODO: Check if the entityId is owned by the memberRequesting it.
////        //TODO: Check if the multiple members can have same permissions.
////        //TODO: Know the difference on using this. {} in the logging while using slf4j
////        //TODO: Trigger the Email Notification where the Password is sent in the Email.
////        //TODO: We can return the member information, check for the best practice.
////
////        Member member = memberMapper.map(memberDTO);
////        memberDao.insert(member);
////
////        EntityMember entityMember = entityMemberMapper.map(memberDTO, member.getId());
////        entityMemberDao.insert(entityMember);
////
////        Entity entity = entityDao.fetchOneById(entityMember.getEntityId());
////
////        notificationManager.sendVerificationEmail(member, EntityCategory.valueOf(entity.getCategory()));
////
////        return memberMapper.map(member);
////    }
//
//
//    public UserDTO resetPassword(String emailId, String resetCode, String password) {
//        // First, check if a user with the given email exists.
//        List<Users> usersWithEmail = dslContext.selectFrom(Tables.USERS)
//                .where(eqIfPresent(Tables.USERS.EMAIL, emailId))
//                .fetchInto(Users.class);
//
//        if (usersWithEmail.isEmpty()) {
//            throw new IllegalArgumentException("No user found with the provided email. Please verify your email and try again.");
//        } else {
//            // If the user exists, check for the correct reset code.
//            List<Users> usersWithCorrectResetCode = usersWithEmail.stream()
//                    .filter(user -> Objects.equals(user.getPasswordReset(), resetCode))
//                    .collect(Collectors.toList());
//
//            if (usersWithCorrectResetCode.isEmpty()) {
//                throw new IllegalArgumentException("The password reset link you've used is invalid or has expired. Please request a new reset link.");
//            }
//        }
//
//        Users user = usersWithEmail.get(0);
//        Individual individual
//                = dslContext.select()
//                .from(INDIVIDUAL)
//                .where(INDIVIDUAL.EMAIL.eq(emailId)).limit(1).fetchOneInto(Individual.class);
//
//
//        Duration duration = Duration.between(user.getPasswordResetCreatedOn(), DateUtils.currentTimeIST());
//        if (duration.toHours() >= 24) {
//            throw new IllegalArgumentException("Reset Code Expired");
//        }
//
//        if (passwordEncoder.matches(password, user.getPassword())) {
//            throw new IllegalArgumentException("New password cannot be the same as the old password");
//        }
//        String encodePassword = passwordEncoder.encode(password);
//        user.setPasswordReset(null);
//        user.setPasswordResetCreatedOn(null);
//        user.setPassword(encodePassword);
//
//        usersDao.update(user);
//
//        if(individual == null){
//            log.warn("Individual does not exist with the given email id :: {}", emailId);
//        }
//        if(individual != null){
//            int passwordResetForIndividual = dslContext.update(INDIVIDUAL)
//                    .set(INDIVIDUAL.PASSWORD, encodePassword)
//                    .where(INDIVIDUAL.ID.eq(individual.getId()))
//                    .execute();
//            String msg = passwordResetForIndividual>0?
//                    "Password reset for individual with email id :: {}"
//                    :"Password reset failed for individual with email id :: {}";
//            log.info(msg, emailId);
//        }
//
//        return usersMapper.map(user);
//    }
//
//    public void forgotPassword(String email) throws MessagingException {
//        List<Users> usersList = usersDao.fetchByEmail(email);
//
//        if (usersList.isEmpty()) {
//            log.error("Email Id Not Found");
//            return;
//        }
//
//        Users user = usersList.get(0);
//
//
//        notificationManager.sendResetPasswordCode(user);
//
//    }
//
//    public MemberDetailsDTO updateMember(MemberDetailsDTO memberDetailsDTO) {
//        if (memberDetailsDTO.getEntityCategory() == SUPER_ADMIN) {
//            log.error("Invalid Category");
//            throw new IllegalArgumentException("Invalid Category");
//        }
//
//        List<Member> memberList = memberDao.fetchById(memberDetailsDTO.getId());
//        if (memberList.isEmpty()) {
//            log.error("Invalid Member details: {}", memberDetailsDTO.getId());
//            throw new IllegalArgumentException("Invalid member details");
//        }
//
//        Member member = memberDetailsDTOToMemberMapper.map(memberList.get(0), memberDetailsDTO);
//        List<EntityMember> entityMemberList = entityMemberDao.fetchByMemberId(member.getId());
//
//        if (entityMemberList.isEmpty()) {
//            log.error("Entity Relation Not found for the member: {}", memberDetailsDTO.getId());
//            throw new IllegalArgumentException("Invalid member details");
//        }
//
//        EntityMember entityMember = entityMemberList.get(0);
//        Entity fetchedEntity = entityDao.fetchOneById(entityMember.getEntityId());
//        if (fetchedEntity == null) {
//            log.error("Entity Not found for the member: {}", memberDetailsDTO.getId());
//            throw new IllegalArgumentException("Invalid member details");
//        }
//        Entity entity = memberDetailsDTOToMemberMapper.map(memberDetailsDTO, fetchedEntity);
//
//        try {
//            entityDao.update(entity);
//            memberDao.update(member);
//        } catch (DataAccessException e) {
//            log.error("Error updating the DB for : {} {}", memberDetailsDTO.getId(), e.getMessage());
//            throw new RuntimeException(e.getMessage());
//        }
//
//        return memberDetailsDTOToMemberMapper.map(member, entity);
//    }
//
//    public Boolean verifyOTP(OtpVerifyDTO otpVerifyDTO) {
//        List<Users> usersList = usersDao.fetchByEmail(otpVerifyDTO.getEmail());
//
//        if (usersList.isEmpty()) {
//            throw new IllegalArgumentException("Invalid Data");
//        }
//
//        Users user = usersList.get(0);
//
//        if (isOTPExpired(user) || !Objects.equals(user.getOtpCode(), otpVerifyDTO.getOtpCode())) {
//            throw new IllegalArgumentException("Invalid OTP code");
//        }
//
//        user.setOtpCode(null);
//        user.setOtpCreatedOn(null);
//        usersDao.update(user);
//        return true;
//    }
//
//    private boolean isOTPExpired(Users user) {
//        LocalDateTime otpCreationTime = user.getOtpCreatedOn();
//        LocalDateTime currentTime = DateUtils.currentTimeIST();
//        long timeDifferenceInMinutes = ChronoUnit.MINUTES.between(otpCreationTime, currentTime);
//
//        return timeDifferenceInMinutes >= 10;
//    }
//
//    public void sendOtp(String email) throws MessagingException {
//
//        List<Users> users = usersDao.fetchByEmail(email);
//
//        if (users.isEmpty()) {
//            log.error("Email Id Not Found");
//            return;
//        }
//
//        notificationManager.sendOTP(users.get(0));
//
//    }
//
//    @Transactional
//    public OTPVerifiedDTO verifyOTPV2(OtpVerifyDTO otpVerifyDTO, Boolean isMember) throws JsonProcessingException, MessagingException {
//
//        OTPVerifiedDTO.OTPVerifiedDTOBuilder otpVerifiedDTOBuilder = OTPVerifiedDTO.builder();
//
//        List<StagingEmailVerification> list = stagingEmailVerificationDao.fetchByEmail(otpVerifyDTO.getEmail());
//        if (list.isEmpty()) {
//            log.error("Email Id Not Found");
//            throw new IllegalArgumentException("Invalid OTP code");
//        }
//
//        StagingEmailVerification stagingRecord = list.get(0);
//
//        if (!Objects.equals(stagingRecord.getOtpCode(), otpVerifyDTO.getOtpCode()) || isOTPExpiredV2(stagingRecord) || stagingRecord.getIsVerified()) {
//            log.error("Invalid OTP");
//            throw new IllegalArgumentException("Invalid OTP code");
//        }
//
//        stagingRecord.setIsVerified(true);
//        stagingRecord.setOtpCode(null);
//        stagingRecord.setOtpCreatedOn(null);
//        stagingEmailVerificationDao.update(stagingRecord);
//
//        if (!isMember) {
//            return otpVerifiedDTOBuilder.status("success").passwordResetLink(null).build();
//        }
//
//        UserDTO userDTO = loginService.signUp(SignUpRequest.builder()
//                .firstName(stagingRecord.getName())
//                .email(stagingRecord.getEmail())
//                .mobileNo(stagingRecord.getContactNumber())
//                .entityCategory(EntityCategory.valueOf(stagingRecord.getEntityCategory()))
//                .entityType(EntityType.valueOf(stagingRecord.getEntityType()))
//                .ipAddress(stagingRecord.getIpAddress())
//                .build());
//
//        Users user = usersDao.fetchOneById(userDTO.getId());
//
//        if (user == null) {
//            throw new RuntimeException("User Creation error");
//        }
//
//
//        String resetPasswordLink = generateAndSavePasswordResetCode(user);
//        return otpVerifiedDTOBuilder.status("success").passwordResetLink(resetPasswordLink).build();
//    }
//
//    @Transactional
//    public OTPVerifiedDTO verifyOTPV3(OtpVerifyDTO otpVerifyDTO, Boolean isMember) throws JsonProcessingException, MessagingException {
//
//        OTPVerifiedDTO.OTPVerifiedDTOBuilder otpVerifiedDTOBuilder = OTPVerifiedDTO.builder();
//
//        List<StagingEmailVerification> list = stagingEmailVerificationDao.fetchByEmail(otpVerifyDTO.getEmail());
//        if (list.isEmpty()) {
//            log.error("Email Id Not Found");
//            throw new IllegalArgumentException("Invalid OTP code");
//        }
//
//        StagingEmailVerification stagingRecord = list.get(0);
//
//        if (!Objects.equals(stagingRecord.getOtpCode(), otpVerifyDTO.getOtpCode()) || isOTPExpiredV2(stagingRecord) || stagingRecord.getIsVerified()) {
//            log.error("Invalid OTP");
//            throw new IllegalArgumentException("Invalid OTP code");
//        }
//
//        stagingRecord.setIsVerified(true);
//        stagingRecord.setOtpCode(null);
//        stagingRecord.setOtpCreatedOn(null);
//        stagingEmailVerificationDao.update(stagingRecord);
//
//        // Update Individual Verification Audit to VERIFIED
//        List<IndividualVerificationAudit> individualVerificationAudits = individualVerificationAuditDao.fetchByContactValue(otpVerifyDTO.getEmail());
//        if(individualVerificationAudits!= null && !CollectionUtils.isEmpty(individualVerificationAudits)) {
//            IndividualVerificationAudit auditRecord = individualVerificationAudits.get(0);
//            if (auditRecord != null) {
//                auditRecord.setVerificationStatus(VerificationStatusEnum.VERIFIED.name());
//                individualVerificationAuditDao.update(auditRecord);
//                log.info("Individual Verification Audit status updated to VERIFIED for email: {}", otpVerifyDTO.getEmail());
//            } else {
//                log.warn("No Individual Verification Audit record found for email: {}", otpVerifyDTO.getEmail());
//            }
//        }
//
//        if (!isMember) {
//            return otpVerifiedDTOBuilder.status("success").passwordResetLink(null).build();
//        }
//
//        String stagingRecordName = stagingRecord.getName();
//        String firstName;
//        String lastName;
//
//        // Split the name into words
//        String[] words = stagingRecordName.split("\\s+");
//
//        if (words.length == 1) {
//            firstName = stagingRecordName;
//            lastName = "";
//        } else if (words.length == 2) {
//            firstName = words[0];
//            lastName = words[1];
//        } else {
//            // If more than 2 words, take the first word as firstName and the rest as lastName
//            firstName = words[0];
//            lastName = String.join(" ", Arrays.copyOfRange(words, 1, words.length));
//        }
//
//        UserDTO userDTO = loginService.signUpVersion3(SignUpDTO.builder()
//                .firstName(firstName)
//                .lastName(lastName)
//                .email(stagingRecord.getEmail())
//                .role(DefaultOrganisationCategoryEnums.valueOf(stagingRecord.getRole()))
//                .mobileNo(stagingRecord.getContactNumber())
//                .ipAddress(stagingRecord.getIpAddress())
//                .organisationName(stagingRecord.getOrganisationName())
//                .companyType(stagingRecord.getOrganisationType())
//                .consentAgreement(Boolean.TRUE)
//                .registeredFor(List.of(RegisteredFor.PRO))
//                .googleLocation(stagingRecord.getGoogleLocation())
//                .build(), null);
//
//        Users user = usersDao.fetchOneById(userDTO.getId());
//
//        if (user == null) {
//            throw new RuntimeException("User Creation error");
//        }
//
//        String resetPasswordLink = generateAndSavePasswordResetCode(user);
//
//        if (user.getRoleId() == null) {
//            throw new IllegalArgumentException("Role ID Not Valid");
//        }
//
//        Role role = roleDao.fetchOneById(user.getRoleId());
//
//        if (role == null) {
//            throw new IllegalArgumentException("Role Entity Not Exist");
//        }
//
//        if (role.getId().equals(UUID.fromString(DefaultRolesEnums.SERVICE_PROVIDER.getValue()))) {
//            userServiceProfileService.create(user);
//        }
//
//        notificationManager.sendSuccessRegistrationWithoutResetLink(user, role.getName());
//
//        return otpVerifiedDTOBuilder.status("success").passwordResetLink(resetPasswordLink).build();
//    }
//
//
//    public String generateAndSavePasswordResetCode(Users user) {
//
//        String resetCode = RandomStringUtils.random(64, true, true);
//        user.setPasswordReset(resetCode);
//        user.setPasswordResetCreatedOn(DateUtils.currentTimeIST());
//        usersDao.update(user);
//
//        return encodeLink(resetUrl + "/reset-password/?resetCode=" + resetCode + "&emailId=" + user.getEmail());
//
//    }
//
//
//    private String encodeLink(String link) {
//        return UriComponentsBuilder.fromUriString(link).build().encode().toString();
//    }
//
//    private boolean isOTPExpiredV2(StagingEmailVerification stagingEmailVerification) {
//        LocalDateTime otpCreationTime = stagingEmailVerification.getOtpCreatedOn();
//        LocalDateTime currentTime = DateUtils.currentTimeIST();
//        long timeDifferenceInMinutes = ChronoUnit.MINUTES.between(otpCreationTime, currentTime);
//
//        return timeDifferenceInMinutes >= 10;
//    }

}