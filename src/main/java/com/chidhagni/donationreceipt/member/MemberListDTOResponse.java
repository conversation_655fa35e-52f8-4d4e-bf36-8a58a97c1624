package com.chidhagni.donationreceipt.member;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Data;

import java.util.UUID;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MemberListDTOResponse {
    private UUID id;
    private String firstName;
    private String lastName;
    private String email;
    private String mobileNumber;
    private UUID entityId;
    private Boolean isEmpanelled;
    private String createdOn;
    private String updatedOn;
    private String createdBy;
    private String updatedBy;
}
