package com.chidhagni.donationreceipt.member;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/member")
@RequiredArgsConstructor
public class MemberController {
    private final MemberService memberService;



//    @PostMapping
//    @PreAuthorize("hasRole('ROLE_WRITE_PRIVILEGE') and hasRole('ROLE_TEST1_PRIVILEGE') and hasRole('ROLE_TEST2_PRIVILEGE')")
//    public ResponseEntity<?> create(@Valid @RequestBody MemberDTO body) throws MessagingException, JsonProcessingException {
////        MemberDTO createdMember = memberService.create(body);
////        return ResponseEntity.created(UriComponentsBuilder.fromPath("/member/{id}").buildAndExpand(createdMember.getId()).toUri()).body(createdMember);
//        return ResponseEntity.ok().body(null);
//    }
//
//
//    @PatchMapping
//    //not using any where in the frontend
//    //need to check where the endpoint is used
//    public ResponseEntity<MemberDetailsDTO> updateMember(@RequestBody @Valid MemberDetailsDTO memberDetailsDTO) {
//        MemberDetailsDTO updatedMemberDetailsDTO = memberService.updateMember(memberDetailsDTO);
//        return ResponseEntity.ok().body(updatedMemberDetailsDTO);
//    }


}