package com.chidhagni.filestore.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MinioConfig {
	@Value("${filestore.access.name}")
	String accessKey;
	@Value("${filestore.access.secret}")
	String accessSecret;
	@Value("${filestore.url}")
	String fileStoreUrl;

	@Bean
	public MinioClient generateMinioClient() {
		return MinioClient.builder().endpoint(fileStoreUrl).credentials(accessKey, accessSecret).build();
	}
}
