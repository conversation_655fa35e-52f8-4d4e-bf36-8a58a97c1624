<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Custom Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="/donationreceipt/swagger-ui/swagger-ui.css">
    <style>
        #loginContainer {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 350px;
            text-align: center;
            box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
            padding: 20px;
            border-radius: 5px;
        }

        #loginContainer label {
            display: block;
            margin-bottom: 5px;
            font-size: 16px;
            text-align: left;
        }

        #loginContainer input,
        #loginContainer button {
            display: block;
            margin: 10px 0;
            font-size: 16px;
            width: 100%;
            height: 45px;
            padding: 10px;
            cursor: pointer;
            box-sizing: border-box;
        }

        #loginContainer button {
            line-height: 25px;
        }

        .modal {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 450px;
            height: 750px;
            text-align: center;
            padding: 20px;
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 400px;
            width: 100%;
        }

        .modal button {
            margin: 10px;
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background-color: #007bff;
            color: white;
            border-radius: 5px;
        }

        /* Hide the Authorize button */
        .auth-wrapper, .auth-container {
            display: none !important;
        }
    </style>
</head>
<body>
<div id="loginContainer">
    <h3>Login to Access Swagger UI</h3>
    <label for="email">Email:</label>
    <input type="text" id="email"/>
    <label for="password">Password:</label>
    <input type="password" id="password"/>
    <button onclick="loginAndLoadSwagger()">Login</button>
</div>
<div id="swagger-ui" style="display:none;"></div>
<button id="logoutButton" onclick="logout()" style="display: none; position:absolute; top:10px; right:10px; z-index: 1000;">🔓 Logout</button>

<div id="confirmationDialog" class="modal">
    <div class="modal-content">
        <p>Are you sure you want to log out of other devices?</p>
        <button id="yesButton">Yes</button>
        <button id="noButton">No</button>
    </div>
</div>

<script src="/donationreceipt/swagger-ui/swagger-ui-bundle.js"></script>
<script src="/donationreceipt/swagger-ui/swagger-ui-standalone-preset.js"></script>
<script>
    let jwtToken = sessionStorage.getItem('jwtToken') || '';
    console.log('Initial JWT token:', jwtToken ? 'Present' : 'Absent');

    if (jwtToken) {
        console.log('JWT token found, attempting to load Swagger UI');
        document.getElementById('loginContainer').style.display = 'none';
        document.getElementById('swagger-ui').style.display = 'block';
        document.getElementById('logoutButton').style.display = 'block';
        loadSwagger();
    } else {
        console.log('No JWT token, showing login form');
    }

    function loginAndLoadSwagger() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        console.log('Initiating login with email:', email);

        fetch("/donationreceipt/auth/api/v1/individual-login", {
            method: "POST",
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email, password, overrideExistingLogins: false })
        })
        .then(response => {
            console.log('Login response status:', response.status);
            if (response.ok) {
                return response.json();
            } else {
                throw new Error(`Login failed with status: ${response.status}`);
            }
        })
        .then(data => {
            console.log('Login response data:', data);
            if (data.token && data.token.accessToken) {
                jwtToken = data.token.accessToken;
                sessionStorage.setItem('jwtToken', jwtToken);
                console.log('JWT token stored:', jwtToken);
                document.getElementById('loginContainer').style.display = 'none';
                document.getElementById('swagger-ui').style.display = 'block';
                document.getElementById('logoutButton').style.display = 'block';
                loadSwagger();
            } else {
                console.log('No access token in response, showing confirmation dialog');
                const confirmationDialog = document.getElementById('confirmationDialog');
                confirmationDialog.style.display = 'block';

                const yesButton = document.getElementById('yesButton');
                const noButton = document.getElementById('noButton');

                // Remove existing listeners to prevent duplication
                const newYesButton = yesButton.cloneNode(true);
                yesButton.parentNode.replaceChild(newYesButton, yesButton);
                const newNoButton = noButton.cloneNode(true);
                noButton.parentNode.replaceChild(newNoButton, noButton);

                newYesButton.addEventListener('click', function() {
                    console.log('Confirmed logout of other devices');
                    fetch("/donationreceipt/auth/api/v1/individual-login", {
                        method: "POST",
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ email, password, overrideExistingLogins: true })
                    })
                    .then(response => {
                        console.log('Override login response status:', response.status);
                        if (response.ok) {
                            return response.json();
                        } else {
                            throw new Error(`Override login failed with status: ${response.status}`);
                        }
                    })
                    .then(data => {
                        console.log('Override login response data:', data);
                        if (data.token && data.token.accessToken) {
                            confirmationDialog.style.display = 'none';
                            jwtToken = data.token.accessToken;
                            sessionStorage.setItem('jwtToken', jwtToken);
                            console.log('JWT token stored after override:', jwtToken);
                            document.getElementById('loginContainer').style.display = 'none';
                            document.getElementById('swagger-ui').style.display = 'block';
                            document.getElementById('logoutButton').style.display = 'block';
                            loadSwagger();
                        } else {
                            console.error('No access token in override response');
                            alert('Login failed: No access token received.');
                        }
                    })
                    .catch(error => {
                        console.error('Error during override login:', error.message);
                        alert('Error logging out other devices: ' + error.message);
                    });
                });

                newNoButton.addEventListener('click', function() {
                    console.log('Cancelled logout of other devices');
                    confirmationDialog.style.display = 'none';
                });
            }
        })
        .catch(error => {
            console.error('Error during login:', error.message);
            alert('Login failed: ' + error.message);
        });
    }

    function loadSwagger() {
        console.log('Loading Swagger UI with URL: /donationreceipt/v3/api-docs');
        // Test API docs availability before loading Swagger UI
        fetch('/donationreceipt/v3/api-docs')
            .then(response => {
                console.log('API docs fetch status:', response.status);
                if (!response.ok) {
                    throw new Error(`Failed to fetch API docs: Status ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('API docs fetched successfully:', data.info);
                const ui = SwaggerUIBundle({
                    url: "/donationreceipt/v3/api-docs",
                    dom_id: '#swagger-ui',
                    presets: [SwaggerUIBundle.presets.apis, SwaggerUIStandalonePreset],
                    plugins: [SwaggerUIBundle.plugins.DownloadUrl],
                    // Disable Authorize button
                    swaggerUiConfig: {
                        showAuthPopup: false
                    },
                    requestInterceptor: (request) => {
                        // Skip Authorization header for /v3/api-docs
                        if (request.url.endsWith('/v3/api-docs')) {
                            console.log('Skipping Authorization header for:', request.url);
                            return request;
                        }
                        if (jwtToken) {
                            console.log('Adding Authorization header to request:', request.url);
                            request.headers.Authorization = `Bearer ${jwtToken}`;
                        } else {
                            console.warn('No JWT token available for request:', request.url);
                        }
                        return request;
                    },
                    onComplete: () => {
                        console.log('Swagger UI loaded successfully');
                        // Hide Authorize button via JavaScript
                        const authWrapper = document.querySelector('.auth-wrapper');
                        if (authWrapper) {
                            authWrapper.style.display = 'none';
                        }
                    },
                    onFailure: (error) => {
                        console.error('Failed to load Swagger UI:', error);
                        alert('Failed to load Swagger UI: Check console for details.');
                    }
                });
            })
            .catch(error => {
                console.error('Error fetching API docs:', error.message);
                alert('Cannot load Swagger UI: API docs unavailable at /donationreceipt/v3/api-docs. Error: ' + error.message);
            });
    }

    function logout() {
        console.log('Initiating logout');
        fetch("/donationreceipt/logoutProfile", {
            method: "POST",
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${jwtToken}`
            }
        })
        .then(response => {
            console.log('Logout response status:', response.status);
            if (response.ok) {
                console.log('Logout successful, clearing JWT token');
                sessionStorage.removeItem('jwtToken');
                jwtToken = '';
                document.getElementById('swagger-ui').style.display = 'none';
                document.getElementById('loginContainer').style.display = 'block';
                document.getElementById('logoutButton').style.display = 'none';
            } else {
                throw new Error(`Logout failed with status: ${response.status}`);
            }
        })
        .catch(error => {
            console.error('Error during logout:', error.message);
            alert('Logout failed: ' + error.message);
        });
    }
</script>
</body>
</html>