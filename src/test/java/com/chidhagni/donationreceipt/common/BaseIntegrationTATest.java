package com.chidhagni.donationreceipt.common;

import com.chidhagni.donationreceipt.common.config.IntegrationTestConfig;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.utility.DockerImageName;

@SpringBootTest
@ActiveProfiles("test")
@Import(IntegrationTestConfig.class)
public abstract class BaseIntegrationTATest {

    static final PostgreSQLContainer<?> postGreSqlContainer;

    static {
        postGreSqlContainer = new PostgreSQLContainer<>(DockerImageName
                .parse("postgres")
                .withTag("11.17-alpine")
        ).waitingFor(
                Wait.forListeningPort()
        );
        postGreSqlContainer.start();
    }

    static void postgresqlProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postGreSqlContainer::getJdbcUrl);
        registry.add("spring.datasource.username", postGreSqlContainer::getUsername);
        registry.add("spring.datasource.password", postGreSqlContainer::getPassword);

    }
}
