package com.chidhagni.test;

import com.chidhagni.config.JwtAuthenticationEntryPoint;
import com.chidhagni.config.JwtRequestFilter;
import com.chidhagni.service.UserDetailsServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

public class BaseAPITest {

    @Autowired
    protected MockMvc mvc;

    @MockBean
    protected UserDetailsServiceImpl userDetailsService;

    @MockBean
    protected JwtRequestFilter jwtRequestFilter;

    @MockBean
    protected JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint;

}
