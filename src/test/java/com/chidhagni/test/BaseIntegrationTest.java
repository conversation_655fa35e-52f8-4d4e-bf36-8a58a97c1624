package com.chidhagni.test;

import org.jooq.DSLContext;
import org.jooq.Table;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public abstract class BaseIntegrationTest {

    @Autowired
    private DSLContext context;

    protected abstract Table[] getTable();

    @BeforeEach
    public void setUp() {
        truncateTable(getTable());
    }

    @AfterEach
    public void cleanUp() {
        truncateTable(getTable());
    }

    protected void truncateTable(Table[] tables) {
        for (Table table : tables) {
            context.truncate(table).cascade().execute();
        }
    }
}
