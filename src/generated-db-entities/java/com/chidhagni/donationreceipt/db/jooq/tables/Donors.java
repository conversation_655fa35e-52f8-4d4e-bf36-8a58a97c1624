/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.donationreceipt.db.jooq.tables;


import com.chidhagni.donationreceipt.db.jooq.DefaultSchema;
import com.chidhagni.donationreceipt.db.jooq.Keys;
import com.chidhagni.donationreceipt.db.jooq.tables.records.DonorsRecord;
import com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData;
import com.chidhagni.donationreceipt.donors.jooq.DonorMetadataJsonConverter;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Donors extends TableImpl<DonorsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>donors</code>
     */
    public static final Donors DONORS = new Donors();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DonorsRecord> getRecordType() {
        return DonorsRecord.class;
    }

    /**
     * The column <code>donors.id</code>.
     */
    public final TableField<DonorsRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>donors.tenant_org_id</code>.
     */
    public final TableField<DonorsRecord, UUID> TENANT_ORG_ID = createField(DSL.name("tenant_org_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donors.name</code>.
     */
    public final TableField<DonorsRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>donors.email</code>.
     */
    public final TableField<DonorsRecord, String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>donors.mobile_number</code>.
     */
    public final TableField<DonorsRecord, String> MOBILE_NUMBER = createField(DSL.name("mobile_number"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>donors.pan_no</code>.
     */
    public final TableField<DonorsRecord, String> PAN_NO = createField(DSL.name("pan_no"), SQLDataType.VARCHAR(20), this, "");

    /**
     * The column <code>donors.meta_data</code>.
     */
    public final TableField<DonorsRecord, DonorMetaData> META_DATA = createField(DSL.name("meta_data"), SQLDataType.JSONB, this, "", new DonorMetadataJsonConverter());

    /**
     * The column <code>donors.is_active</code>.
     */
    public final TableField<DonorsRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field("true", SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>donors.created_by</code>.
     */
    public final TableField<DonorsRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donors.updated_by</code>.
     */
    public final TableField<DonorsRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>donors.created_on</code>.
     */
    public final TableField<DonorsRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>donors.updated_on</code>.
     */
    public final TableField<DonorsRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false).defaultValue(DSL.field("CURRENT_TIMESTAMP", SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>donors.encrypted_pan_no</code>.
     */
    public final TableField<DonorsRecord, String> ENCRYPTED_PAN_NO = createField(DSL.name("encrypted_pan_no"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>donors.pan_no_nonce</code>.
     */
    public final TableField<DonorsRecord, String> PAN_NO_NONCE = createField(DSL.name("pan_no_nonce"), SQLDataType.VARCHAR(32).nullable(false), this, "");

    private Donors(Name alias, Table<DonorsRecord> aliased) {
        this(alias, aliased, null);
    }

    private Donors(Name alias, Table<DonorsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>donors</code> table reference
     */
    public Donors(String alias) {
        this(DSL.name(alias), DONORS);
    }

    /**
     * Create an aliased <code>donors</code> table reference
     */
    public Donors(Name alias) {
        this(alias, DONORS);
    }

    /**
     * Create a <code>donors</code> table reference
     */
    public Donors() {
        this(DSL.name("donors"), null);
    }

    public <O extends Record> Donors(Table<O> child, ForeignKey<O, DonorsRecord> key) {
        super(child, key, DONORS);
    }

    @Override
    public Schema getSchema() {
        return DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<DonorsRecord> getPrimaryKey() {
        return Keys.DONORS_ID_PK;
    }

    @Override
    public List<UniqueKey<DonorsRecord>> getKeys() {
        return Arrays.<UniqueKey<DonorsRecord>>asList(Keys.DONORS_ID_PK);
    }

    @Override
    public List<ForeignKey<DonorsRecord, ?>> getReferences() {
        return Arrays.<ForeignKey<DonorsRecord, ?>>asList(Keys.DONORS__TENANT_ORG_ID_FK);
    }

    private transient Organisation _organisation;

    public Organisation organisation() {
        if (_organisation == null)
            _organisation = new Organisation(this, Keys.DONORS__TENANT_ORG_ID_FK);

        return _organisation;
    }

    @Override
    public Donors as(String alias) {
        return new Donors(DSL.name(alias), this);
    }

    @Override
    public Donors as(Name alias) {
        return new Donors(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Donors rename(String name) {
        return new Donors(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Donors rename(Name name) {
        return new Donors(name, null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, UUID, String, String, String, String, DonorMetaData, Boolean, UUID, UUID, LocalDateTime, LocalDateTime, String, String> fieldsRow() {
        return (Row14) super.fieldsRow();
    }
}
