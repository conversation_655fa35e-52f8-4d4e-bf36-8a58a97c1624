plugins {
	id 'org.springframework.boot' version '2.6.6'
	id 'jacoco'
	id 'nu.studer.jooq' version '7.1'
	id 'org.sonarqube' version '4.3.0.3225'
}

apply plugin: 'java'
apply plugin: 'idea'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'jacoco'

group = 'com.chidhagni'
version = '0.0.1-SNAPSHOT'

sourceCompatibility = '11'
targetCompatibility = '11'

repositories {
	mavenLocal()
	mavenCentral()
	maven { url 'https://smoochorg.bintray.com' }
}

dependencies {
	testImplementation 'junit:junit:4.13.1'

	// Minio For DMS
	implementation 'io.minio:minio:8.2.0'
	implementation 'org.apache.commons:commons-lang3'

	// Database driver for JOOQ
	jooqGenerator "org.postgresql:postgresql"

	// Oauth2
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'

	// PDF Generation
	implementation 'com.itextpdf:itext7-core:7.1.15'
	implementation 'com.itextpdf:html2pdf:2.1.6'

	// Jsoup extracting data from HTML and manipulates
	implementation 'org.jsoup:jsoup:1.14.3'

	// Lombok
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'

	// Spring dependencies
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	runtimeOnly 'org.springframework.boot:spring-boot-starter-validation'

	// DB dependencies
	runtimeOnly 'org.postgresql:postgresql'
	implementation 'org.liquibase:liquibase-core'
	implementation 'org.jooq:jooq'
	implementation 'com.zaxxer:HikariCP'
	implementation 'org.springframework.boot:spring-boot-starter-jdbc'

	// ApiDocs dependencies
	implementation "org.springdoc:springdoc-openapi-ui:1.6.7"
	implementation "org.springdoc:springdoc-openapi-security:1.6.7"

	// Test dependencies
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'org.springframework.security:spring-security-test'
	implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
	implementation 'io.jsonwebtoken:jjwt-impl:0.11.2'
	implementation 'io.jsonwebtoken:jjwt-jackson:0.11.2'
	implementation 'org.springframework.security:spring-security-oauth2-client:5.6.0'
	implementation 'org.springframework.security:spring-security-oauth2-jose:5.6.0'
	implementation 'org.springframework.security:spring-security-oauth2-resource-server:5.6.0'
	implementation 'com.auth0:java-jwt:3.18.2'

	// JavaMailSender
	implementation 'org.springframework.boot:spring-boot-starter-mail'
	implementation 'org.thymeleaf:thymeleaf-spring5'
	implementation 'nz.net.ultraq.thymeleaf:thymeleaf-layout-dialect'

	implementation 'org.json:json:20210307'

	implementation 'org.springframework.boot:spring-boot-starter'
	runtimeOnly 'ch.qos.logback:logback-classic'

	implementation 'ch.qos.logback:logback-core:1.2.3'
	implementation 'ch.qos.logback:logback-classic:1.2.3'

	implementation 'com.razorpay:razorpay-java:1.3.9'

	//shed lock dependencies
	implementation("net.javacrumbs.shedlock:shedlock-spring:4.29.0")
	implementation("net.javacrumbs.shedlock:shedlock-provider-jdbc-template:4.29.0")

	implementation 'org.jetbrains:annotations:17.0.0'

	testImplementation 'org.testcontainers:testcontainers:1.19.0'
	testImplementation 'org.testcontainers:postgresql:1.19.0'

	implementation 'com.jayway.jsonpath:json-path:2.9.0'

	// MapStruct library dependency
	implementation 'org.mapstruct:mapstruct:1.5.2.Final'

	// MapStruct annotation processor for code generation
	annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.2.Final'

	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.15.3'

	implementation 'org.apache.poi:poi-ooxml:5.2.3'


	//Google API Client
	implementation 'com.google.api-client:google-api-client:1.34.1'
	implementation 'com.google.apis:google-api-services-drive:v3-rev197-1.25.0'
	implementation 'com.google.http-client:google-http-client-jackson2:1.41.3'
	implementation 'com.google.auth:google-auth-library-oauth2-http:1.20.0'
}

compileJava {
	options.compilerArgs << "-Xlint:unchecked"
}

test {
	useJUnitPlatform()
	reports {
		junitXml.required.set(true)
		html.required.set(true)
	}
}

tasks.withType(Test) {
	testLogging.showStandardStreams = true
	afterTest { desc, result ->
		println "Executing test ${desc.name} [${desc.className}] with result: ${result.resultType}"
	}
	testLogging {
		events "passed", "skipped", "failed"
	}
}

task unitTest(type: Test) {
	useJUnitPlatform {
		maxParallelForks = Runtime.runtime.availableProcessors()
		include '**/*UTest.class'
	}
	failFast = true
}

task integrationTest(type: Test) {
	useJUnitPlatform {
		includeEngines 'junit-jupiter'
		include '**/*ITest.class'
	}
	failFast = true
}

task apiTest(type: Test) {
	useJUnitPlatform {
		includeEngines 'junit-jupiter'
		include '**/*ApiTest.class'
	}
	failFast = true
}

task jooqGen {
	dependsOn += 'generateJooq'
}
jooq {
	configurations {
		main {
			generateSchemaSourceOnCompilation = false
			generationTool {
				jdbc {
					driver = 'org.postgresql.Driver'
					url = '*************************************************'
					user = 'donationreceipt'
					password = 'donationreceipt'
					properties {
						property {
							key = 'useSSL'
							value = 'false'
						}
					}
				}
				generator {
					name = 'org.jooq.codegen.DefaultGenerator'
					database {

						forcedTypes {
								forcedType {
									userType = "com.chidhagni.donationreceipt.individual.IndividualMetaDataDTO"
									converter = "com.chidhagni.donationreceipt.individual.jooq.IndividualMetaDataJsonConverter"
									includeTypes = "JSONB"
									includeExpression = "(?i)([A-Z_-]+\\.)*(individual\\.meta_data)"
								}
								forcedType {
									userType = "java.util.List<com.chidhagni.donationreceipt.roles.dto.request.RoleNode>"
									converter = "com.chidhagni.donationreceipt.roles.utils.NodeJsonConverter"
									includeTypes = "JSONB"
									includeExpression = "(?i)([A-Z_-]+\\.)*(roles\\.permissions)"
								}
								forcedType {
									userType = "java.util.List<com.chidhagni.donationreceipt.roles.dto.request.RoleNode>"
									converter = "com.chidhagni.donationreceipt.roles.utils.NodeJsonConverter"
									includeTypes = "JSONB"
									includeExpression = "(?i)([A-Z_-]+\\.)*(individual_permission\\.permissions)"
								}
								forcedType {
									userType = "com.chidhagni.donationreceipt.documentrepo.dto.request.TagsDTO"
									converter = "com.chidhagni.donationreceipt.documentrepo.jooq.DocumentRepoTagsJsonConverter"
									includeTypes = "JSONB"
									includeExpression = "(?i)([A-Z_-]+\\.)*(document_repo\\.tags)"
								}
								forcedType {
									userType = "com.chidhagni.donationreceipt.individualverificationaudit.dto.request.IndividualVerificationMetaData"
									converter = "com.chidhagni.donationreceipt.individualverificationaudit.jooq.IndividualVerificationJsonConverter"
									includeTypes = "JSONB"
									includeExpression = "(?i)([A-Z_-]+\\.)*(individual_verification_audit\\.meta_data)"
								}
								forcedType {
									userType = "com.chidhagni.donationreceipt.organisation.MetaDataDTO"
									converter = "com.chidhagni.donationreceipt.organisation.jooq.OrganisationMetaDataJsonConverter"
									includeTypes = "JSONB"
									includeExpression = "(?i)([A-Z_-]+\\.)*(organisation\\.meta_data)"
								}
							    forcedType {
								userType = "com.chidhagni.donationreceipt.donationreceipts.dto.request.DonationReceiptMetaDataDTO"
								converter = "com.chidhagni.donationreceipt.donationreceipts.jooq.DonationReceiptsMetaDataJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(donation_receipts\\.meta_data)"
							    }
								forcedType {
									userType = "com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO"
									converter = "com.chidhagni.donationreceipt.documentrepo.jooq.docSenderMetaDataJsonConverter"
									includeTypes = "JSONB"
									includeExpression = "(?i)([A-Z_-]+\\.)*(document_repo\\.sender)"
								}
								forcedType {
									userType = "java.util.List<com.chidhagni.donationreceipt.documentrepo.dto.request. participantDetailsDTO>"
									converter = "com.chidhagni.donationreceipt.documentrepo.jooq.docReceiverMetaDataJsonConverter"
									includeTypes = "JSONB"
									includeExpression = "(?i)([A-Z_-]+\\.)*(document_repo\\.recipients)"
								}
							forcedType {
								userType = "com.chidhagni.donationreceipt.donors.dto.request.DonorMetaData"
								converter = "com.chidhagni.donationreceipt.donors.jooq.DonorMetadataJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(donors\\.meta_data)"
							}
							forcedType {
								userType = "com.chidhagni.donationreceipt.donorgroups.dto.request.DonorGroupFilters"
								converter = "com.chidhagni.donationreceipt.donorgroups.jooq.DonorGroupFiltersJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(donor_groups\\.filters)"
							}

							}

						name = 'org.jooq.meta.postgres.PostgresDatabase'
						inputSchema = 'public'
						outputSchemaToDefault = true
						excludes = 'DATABASECHANGELOG|DATABASECHANGELOGLOCK|SHEDLOCK'
					}
					generate {
						relations = false
						deprecated = false
						records = true
						pojos = true
						daos = true
						springAnnotations = true
						javaTimeTypes = true
						fluentSetters = true
						pojosEqualsAndHashCode = true
					}

					target {
						packageName = 'com.chidhagni.donationreceipt.db.jooq'
						directory = 'src/generated-db-entities/java/'
					}
				}

			}
		}
	}
}

jacocoTestReport {
	dependsOn test
	executionData.setFrom(fileTree(dir: "$buildDir/jacoco", include: "**/*.exec"))

	reports {
		html.required.set(true)
		xml.required.set(true)
		csv.required.set(false)
	}

	classDirectories.setFrom(files(project.sourceSets.main.output))
	afterEvaluate {
		classDirectories.setFrom(files(classDirectories.files.collect {
			fileTree(dir: it, exclude: ['com/chidhagni/donationreceipt/db/jooq/**'])
		}))
	}

	// Ensure jacocoTestReport runs after compileJava and processResources
	mustRunAfter compileJava
	mustRunAfter processResources
	dependsOn test
}

sonarqube {
	properties {
		property "sonar.coverage.jacoco.xmlReportPaths",
				"$buildDir/reports/jacoco/test/jacocoTestReport.xml"
		property "sonar.exclusions", "src/generated-db-entities/**, src/**/config/**"
	}
}

// Treat the generated sources as a separate source set so that Pull Requests are not confusing
sourceSets {
	main {
		java {
			srcDirs 'src/generated-db-entities/java'
		}
	}
}

bootRun {
	// The profile can be passed as ./gradlew clean bootRun -Dspring.profiles.active=dev
	systemProperties['spring.profiles.active'] = project.gradle.startParameter.systemPropertiesArgs['spring.profiles.active']
}

// Add this section to customize JAR naming based on the 'profile' property
bootJar {
	archiveFileName = "donationreceipt-${project.properties['profile']}.jar"
}